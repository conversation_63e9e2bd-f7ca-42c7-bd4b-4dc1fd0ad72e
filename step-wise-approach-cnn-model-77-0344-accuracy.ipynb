{"cells": [{"metadata": {"_uuid": "f0cabcad77a2253757f400696d74a8a30c2e4cc9"}, "cell_type": "markdown", "source": "# Welcome to my kernel \nSkin cancer is the most common human malignancy, is primarily diagnosed visually, beginning with an initial clinical screening and followed potentially by dermoscopic analysis, a biopsy and histopathological examination. Automated classification of skin lesions using images is a challenging task owing to the fine-grained variability in the appearance of skin lesions.\n\nThis the **HAM10000 (\"Human Against Machine with 10000 training images\")** dataset.It consists of 10015 dermatoscopicimages which are released as a training set for academic machine learning purposes and are publiclyavailable through the ISIC archive. This benchmark dataset can be used for machine learning and for comparisons with human experts. \n\nIt has 7 different classes of skin cancer which are listed below :<br>\n**1. Melanocytic nevi <br>**\n**2. Melanoma <br>**\n**3. Benign keratosis-like lesions <br>**\n**4. Basal cell carcinoma <br>**\n**5. Actinic keratoses <br>**\n**6. Vascular lesions <br>**\n**7. Dermatofibroma<br>**\n\nIn this kernel I will try to detect 7 different classes of skin cancer using Convolution Neural Network with keras tensorflow in backend and then analyse the result to see how the model can be useful in practical scenario.<br>\nWe will move step by step process to classify 7 classes of cancer.\n\nIn this kernel I have followed following 14 steps for model building and evaluation which are as follows : <br>\n**Step 1 : Importing Essential Libraries**<br>\n**Step 2: Making Dictionary of images and labels** <br>\n**Step 3: Reading and Processing Data** <br>\n**Step 4: Data Cleaning** <br>\n**Step 5: Exploratory data analysis (EDA)** <br>\n**Step 6: Loading & Resizing of images **<br>\n**Step 7: Train Test Split**<br>\n**Step 8: Normalization**<br>\n**Step 9: Label Encoding** <br>\n**Step 10: Train validation split** <br>\n**Step 11: Model Building (CNN)** <br>\n**Step 12: Setting Optimizer & Annealing** <br>\n**Step 13: Fitting the model**<br>\n**Step 14: Model Evaluation (Testing and validation accuracy, confusion matrix, analysis of misclassified instances)** <br>\n\n<img src=\"https://image.ibb.co/n8PBkL/cover.png\">"}, {"metadata": {"_uuid": "3ce2b3bf606c3bf06d9f61249f580a55812f9e42"}, "cell_type": "markdown", "source": "# Step 1 : importing Essential Libraries"}, {"metadata": {"_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "trusted": true}, "cell_type": "code", "source": "%matplotlib inline\nimport matplotlib.pyplot as plt\nimport numpy as np\nimport pandas as pd\nimport os\nfrom glob import glob\nimport seaborn as sns\nfrom PIL import Image\nnp.random.seed(123)\nfrom sklearn.preprocessing import label_binarize\nfrom sklearn.metrics import confusion_matrix\nimport itertools\n\nimport keras\nfrom keras.utils.np_utils import to_categorical # used for converting labels to one-hot-encoding\nfrom keras.models import Sequential\nfrom keras.layers import Dense, Dropout, Flatten, Conv2D, MaxPool2D\nfrom keras import backend as K\nimport itertools\nfrom keras.layers.normalization import BatchNormalization\nfrom keras.utils.np_utils import to_categorical # convert to one-hot-encoding\n\nfrom keras.optimizers import Adam\nfrom keras.preprocessing.image import ImageDataGenerator\nfrom keras.callbacks import ReduceLROnPlateau\nfrom sklearn.model_selection import train_test_split", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "03a061e80db267cf498df46c6fab5b2d6f44b7d8"}, "cell_type": "code", "source": "#1. Function to plot model's validation loss and validation accuracy\ndef plot_model_history(model_history):\n    fig, axs = plt.subplots(1,2,figsize=(15,5))\n    # summarize history for accuracy\n    axs[0].plot(range(1,len(model_history.history['acc'])+1),model_history.history['acc'])\n    axs[0].plot(range(1,len(model_history.history['val_acc'])+1),model_history.history['val_acc'])\n    axs[0].set_title('Model Accuracy')\n    axs[0].set_ylabel('Accuracy')\n    axs[0].set_xlabel('Epoch')\n    axs[0].set_xticks(np.arange(1,len(model_history.history['acc'])+1),len(model_history.history['acc'])/10)\n    axs[0].legend(['train', 'val'], loc='best')\n    # summarize history for loss\n    axs[1].plot(range(1,len(model_history.history['loss'])+1),model_history.history['loss'])\n    axs[1].plot(range(1,len(model_history.history['val_loss'])+1),model_history.history['val_loss'])\n    axs[1].set_title('Model Loss')\n    axs[1].set_ylabel('Loss')\n    axs[1].set_xlabel('Epoch')\n    axs[1].set_xticks(np.arange(1,len(model_history.history['loss'])+1),len(model_history.history['loss'])/10)\n    axs[1].legend(['train', 'val'], loc='best')\n    plt.show()\n", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "c170def1ed6bd1e279dc6d5ae86a95cf6cfd2efb"}, "cell_type": "markdown", "source": "# Step 2 : Making Dictionary of images and labels\nIn this step I have made the image path dictionary by joining the folder path from base directory base_skin_dir and merge the images in jpg format from both the folders HAM10000_images_part1.zip and HAM10000_images_part2.zip"}, {"metadata": {"_cell_guid": "79c7e3d0-c299-4dcb-8224-4455121ee9b0", "_uuid": "d629ff2d2480ee46fbb7e2d37f6b5fab8052498a", "trusted": true}, "cell_type": "code", "source": "base_skin_dir = os.path.join('..', 'input')\n\n# Merging images from both folders HAM10000_images_part1.zip and HAM10000_images_part2.zip into one dictionary\n\nimageid_path_dict = {os.path.splitext(os.path.basename(x))[0]: x\n                     for x in glob(os.path.join(base_skin_dir, '*', '*.jpg'))}\n\n# This dictionary is useful for displaying more human-friendly labels later on\n\nlesion_type_dict = {\n    'nv': 'Melanocytic nevi',\n    'mel': 'Melanoma',\n    'bkl': 'Benign keratosis-like lesions ',\n    'bcc': 'Basal cell carcinoma',\n    'akiec': 'Actinic keratoses',\n    'vasc': 'Vascular lesions',\n    'df': 'Dermatofibroma'\n}", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "81603d003c5cea8f6e76a1bb69aa4c29a89eebc8"}, "cell_type": "markdown", "source": "# Step 3 : Reading & Processing data\n\nIn this step we have read the csv by joining the path of image folder which is the base folder where all the images are placed named base_skin_dir.\nAfter that we made some new columns which is easily understood for later reference such as we have made column path which contains the image_id, cell_type which contains the short name of lesion type and at last we have made the categorical column cell_type_idx in which we have categorize the lesion type in to codes from 0 to 6"}, {"metadata": {"trusted": true, "_uuid": "68f34a08751a6e16569818ce8b18d9fca93223ad"}, "cell_type": "code", "source": "\n\nskin_df = pd.read_csv(os.path.join(base_skin_dir, 'HAM10000_metadata.csv'))\n\n# Creating New Columns for better readability\n\nskin_df['path'] = skin_df['image_id'].map(imageid_path_dict.get)\nskin_df['cell_type'] = skin_df['dx'].map(lesion_type_dict.get) \nskin_df['cell_type_idx'] = pd.Categorical(skin_df['cell_type']).codes\n", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "0de2d23671ac649a0e20f426fb891a4e8f7e903a"}, "cell_type": "code", "source": "# Now lets see the sample of tile_df to look on newly made columns\nskin_df.head()", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "9a8277864a96024b0da585294397e965933f4f75"}, "cell_type": "markdown", "source": "# Step 4 : Data Cleaning\nIn this step we check for Missing values and datatype of each field "}, {"metadata": {"trusted": true, "_uuid": "091723e3ecf2a032a873c1a9939a9788e5a47bcd"}, "cell_type": "code", "source": "skin_df.isnull().sum()", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "a621886714714d923e175b5d6103a7c8780ec92c"}, "cell_type": "markdown", "source": "As it is evident from the above that only age has null values which is 57 so we will fill the null values by their mean."}, {"metadata": {"trusted": true, "_uuid": "a5f4b33555008f83543bb867ec4ed7f5a4ff2e43"}, "cell_type": "code", "source": "skin_df['age'].fillna((skin_df['age'].mean()), inplace=True)", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "5bc2f6e2f475c890065469eec9fcf759b2d4b2c6"}, "cell_type": "markdown", "source": "Now, lets check the presence of null values  again"}, {"metadata": {"trusted": true, "_uuid": "1767c5491f1daa55c8a63e8b26dfa964726998d1"}, "cell_type": "code", "source": "skin_df.isnull().sum()", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "f701dda65f9f91f315fa899d6984b6e4ed71b490"}, "cell_type": "code", "source": "print(skin_df.dtypes)", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "759026191d74e7d94ac08d81121913d06a053a1d"}, "cell_type": "markdown", "source": "# Step 5 : EDA\nIn this we will explore different features of the dataset , their distrubtions and actual counts"}, {"metadata": {"_uuid": "26b2411336be49dbfa988dece2febfdba1f40d08"}, "cell_type": "markdown", "source": "Plot to see distribution of 7 different classes of cell type"}, {"metadata": {"trusted": true, "_uuid": "fc479dfe198cd5b781573f5c17c3a052d4fe196f"}, "cell_type": "code", "source": "fig, ax1 = plt.subplots(1, 1, figsize= (10, 5))\nskin_df['cell_type'].value_counts().plot(kind='bar', ax=ax1)", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "69aabd5c3892fd231b5a59295093816c404491f9"}, "cell_type": "markdown", "source": "Its seems from the above plot that in this dataset cell type Melanecytic nevi has very large number of instances in comparison to other cell types"}, {"metadata": {"_uuid": "0da35254577f2ced80b74357142718c88fe2fc23"}, "cell_type": "markdown", "source": "Plotting of Technical Validation field (ground truth) which is dx_type to see the distribution of its 4 categories which are listed below :<br>\n**1. Histopathology(Histo):**  Histopathologic diagnoses of excised lesions have been\nperformed by specialized dermatopathologists. <br>\n**2. Confocal:** Reflectance confocal microscopy is an in-vivo imaging technique with a resolution at near-cellular level , and some facial benign with a grey-world assumption of all training-set images in Lab-color space before\nand after  manual histogram changes.<br>\n**3. Follow-up:** If nevi monitored by digital dermatoscopy did not show any changes during 3 follow-up visits or 1.5 years biologists  accepted this as evidence of biologic benignity. Only nevi, but no other benign diagnoses were labeled with this type of ground-truth because dermatologists usually do not monitor dermatofibromas, seborrheic keratoses, or vascular lesions. <br>\n**4. Consensus:** For typical benign cases without histopathology or followup biologists  provide an expert-consensus rating of authors PT and HK. They applied the consensus label only if both authors independently gave the same unequivocal benign diagnosis. Lesions with this type of groundtruth were usually photographed for educational reasons and did not need\nfurther follow-up or biopsy for confirmation.\n"}, {"metadata": {"trusted": true, "_uuid": "574f2c782b7091305a7d921046a5a1843df09873"}, "cell_type": "code", "source": "skin_df['dx_type'].value_counts().plot(kind='bar')", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "6431d7eb231a5d95781b5e8d6bd1310ae0651845"}, "cell_type": "markdown", "source": "Plotting the distribution of localization field "}, {"metadata": {"trusted": true, "_uuid": "cfbcd68a065cd1abc003d14c8d3c6eb716379b8a"}, "cell_type": "code", "source": "skin_df['localization'].value_counts().plot(kind='bar')", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "c124326fdc4d314a0d259af1be1aa84f1929ff83"}, "cell_type": "markdown", "source": "It seems back , lower extremity,trunk and upper extremity are heavily compromised regions of skin cancer "}, {"metadata": {"_uuid": "0fde8ae3f6b6c539c324ab8c0cae3d24628d1545"}, "cell_type": "markdown", "source": "Now, check the distribution of Age"}, {"metadata": {"trusted": true, "_uuid": "6c1097e45fb83a20d8c611cd40d443194d29c517"}, "cell_type": "code", "source": "skin_df['age'].hist(bins=40)", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "c32f703247867e0813bb00e781be5cc33b0f9d15"}, "cell_type": "markdown", "source": "It seems that there are larger instances of patients having age from 30 to 60"}, {"metadata": {"_uuid": "99f16eba52d0f9beacbe7dc8696b82a5df74fa2f"}, "cell_type": "markdown", "source": "Lets see the distribution of males and females"}, {"metadata": {"trusted": true, "_uuid": "dab9e33ef4149e65017ba09189be91323525f761"}, "cell_type": "code", "source": "\nskin_df['sex'].value_counts().plot(kind='bar')", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "e6f83afbde1b2e200f887d7c7af7d4efd0f576ab"}, "cell_type": "markdown", "source": "Now lets visualize agewise distribution of skin cancer types"}, {"metadata": {"trusted": true, "_uuid": "247f579092e51a1424e38f8d3536badad7638c27"}, "cell_type": "code", "source": "sns.scatterplot('age','cell_type_idx',data=skin_df)", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "08f1498acb37964ad7e78c84eb528f2aab21be69"}, "cell_type": "markdown", "source": "It seems that skin cancer types 0,1, 3 and 5 which are Melanocytic nevi,dermatofibroma,Basal cell carcinoma and Vascular lesions are not much prevalant below the age of 20 years "}, {"metadata": {"_uuid": "9761d0e5dc4a2a9951fd21b832860c81ce067e9b"}, "cell_type": "markdown", "source": "Sexwise distribution of skin cancer type"}, {"metadata": {"trusted": true, "_uuid": "bbe16ff852e4d0aa591481dbfd08df93ea819275"}, "cell_type": "code", "source": "sns.factorplot('sex','cell_type_idx',data=skin_df)", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "92f3186f6f1106f3ec491d99313b506eb514f011"}, "cell_type": "markdown", "source": "# Step 6: Loading and resizing of images\nIn this step images will be loaded into the column named image from the image path from the image folder. We also resize the images as the original dimension of images are 450 x 600 x3 which TensorFlow can't handle, so that's why we resize it into 100 x 75. As this step resize all the 10015 images dimensions into 100x 75 so be patient it will take some time."}, {"metadata": {"trusted": true, "_uuid": "eea3ca5052d0b52b31336485258ca5f41089d980"}, "cell_type": "code", "source": "skin_df['image'] = skin_df['path'].map(lambda x: np.asarray(Image.open(x).resize((100,75))))", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "6b0728eb3b75623a4b181232f8c8a2617668045f"}, "cell_type": "code", "source": "skin_df.head()", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "c53e9c8b13ef01ab3e6fd94a448fa0c33b42262f"}, "cell_type": "markdown", "source": "**As we can see image column has been added in its color format code** "}, {"metadata": {"_uuid": "dfed158580f3916f007e2208ffe35a4031e5fff9"}, "cell_type": "markdown", "source": "Most interesting part its always better to see sample of images\nBelow we will show images of each cancer type"}, {"metadata": {"trusted": true, "_uuid": "f56069ceb60c2f1103684ca1c024fa5c48dffb4f"}, "cell_type": "code", "source": "n_samples = 5\nfig, m_axs = plt.subplots(7, n_samples, figsize = (4*n_samples, 3*7))\nfor n_axs, (type_name, type_rows) in zip(m_axs, \n                                         skin_df.sort_values(['cell_type']).groupby('cell_type')):\n    n_axs[0].set_title(type_name)\n    for c_ax, (_, c_row) in zip(n_axs, type_rows.sample(n_samples, random_state=1234).iterrows()):\n        c_ax.imshow(c_row['image'])\n        c_ax.axis('off')\nfig.savefig('category_samples.png', dpi=300)", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "9f7d1941762e258ba69a0627189c6728c7e6203d"}, "cell_type": "code", "source": "# Checking the image size distribution\nskin_df['image'].map(lambda x: x.shape).value_counts()", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "04ae71131a85390f67afc736cd572a77d77f8cb0"}, "cell_type": "code", "source": "features=skin_df.drop(columns=['cell_type_idx'],axis=1)\ntarget=skin_df['cell_type_idx']", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "6e983d68b0a343bdf313ccb70c7cd38afd32c89b"}, "cell_type": "markdown", "source": "# Step 7 : Train Test Split\nIn this step we have splitted the dataset into training and testing set of 80:20 ratio"}, {"metadata": {"trusted": true, "_uuid": "3fe1228e7657f49d7323adc36c865790e9ac05eb"}, "cell_type": "code", "source": "x_train_o, x_test_o, y_train_o, y_test_o = train_test_split(features, target, test_size=0.20,random_state=1234)", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "2d1c806e6c6e46916ffb40b5e2848c66c33ed719"}, "cell_type": "markdown", "source": "# Step 8 : Normalization"}, {"metadata": {"_uuid": "0e93ca14260608cc778b7e784ecb501fb226de5e"}, "cell_type": "markdown", "source": "I choosed to normalize the x_train, x_test by substracting from theor mean values and then dividing by thier standard deviation."}, {"metadata": {"trusted": true, "_uuid": "cd19d9fa10edf4cd89f178db0291be76dbdcbfef"}, "cell_type": "code", "source": "x_train = np.asarray(x_train_o['image'].tolist())\nx_test = np.asarray(x_test_o['image'].tolist())\n\nx_train_mean = np.mean(x_train)\nx_train_std = np.std(x_train)\n\nx_test_mean = np.mean(x_test)\nx_test_std = np.std(x_test)\n\nx_train = (x_train - x_train_mean)/x_train_std\nx_test = (x_test - x_test_mean)/x_test_std", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "73783a09c84893277c34c194431deef120e8e470"}, "cell_type": "markdown", "source": "# Step 9 : Label Encoding\nLabels are 7 different classes of skin cancer types from 0 to 6. We need to encode these lables to one hot vectors "}, {"metadata": {"trusted": true, "_uuid": "392812e39f11353c68a45314b3a7d7308e6a9d13"}, "cell_type": "code", "source": "# Perform one-hot encoding on the labels\ny_train = to_categorical(y_train_o, num_classes = 7)\ny_test = to_categorical(y_test_o, num_classes = 7)", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "595ee1067d7f0c82ea07fdfec4ff9617d9d64d2c"}, "cell_type": "markdown", "source": "# Step 10 : Splitting training and validation split\nI choosed to split the train set in two parts : a small fraction (10%) became the validation set which the model is evaluated and the rest (90%) is used to train the model."}, {"metadata": {"trusted": true, "_uuid": "ed38171b197633a3cb7e0b1f596315456da2b2cf"}, "cell_type": "code", "source": "x_train, x_validate, y_train, y_validate = train_test_split(x_train, y_train, test_size = 0.1, random_state = 2)", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "9aa121fe6240bc4ef91df54b50862df61948b336"}, "cell_type": "code", "source": "# Reshape image in 3 dimensions (height = 75px, width = 100px , canal = 3)\nx_train = x_train.reshape(x_train.shape[0], *(75, 100, 3))\nx_test = x_test.reshape(x_test.shape[0], *(75, 100, 3))\nx_validate = x_validate.reshape(x_validate.shape[0], *(75, 100, 3))", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "857f705a561f046a1d63ffa17a8a0b1e8da16ff5"}, "cell_type": "markdown", "source": "# Step 11: Model Building \n# CNN\nI used the Keras Sequential API, where you have just to add one layer at a time, starting from the input.\n\nThe first is the convolutional (Conv2D) layer. It is like a set of learnable filters. I choosed to set 32 filters for the two firsts conv2D layers and 64 filters for the two last ones. Each filter transforms a part of the image (defined by the kernel size) using the kernel filter. The kernel filter matrix is applied on the whole image. Filters can be seen as a transformation of the image.\n\nThe CNN can isolate features that are useful everywhere from these transformed images (feature maps).\n\nThe second important layer in CNN is the pooling (MaxPool2D) layer. This layer simply acts as a downsampling filter. It looks at the 2 neighboring pixels and picks the maximal value. These are used to reduce computational cost, and to some extent also reduce overfitting. We have to choose the pooling size (i.e the area size pooled each time) more the pooling dimension is high, more the downsampling is important.\n\nCombining convolutional and pooling layers, CNN are able to combine local features and learn more global features of the image.\n\nDropout is a regularization method, where a proportion of nodes in the layer are randomly ignored (setting their wieghts to zero) for each training sample. This drops randomly a propotion of the network and forces the network to learn features in a distributed way. This technique also improves generalization and reduces the overfitting.\n\n'relu' is the rectifier (activation function max(0,x). The rectifier activation function is used to add non linearity to the network.\n\nThe Flatten layer is use to convert the final feature maps into a one single 1D vector. This flattening step is needed so that you can make use of fully connected layers after some convolutional/maxpool layers. It combines all the found local features of the previous convolutional layers.\n\nIn the end i used the features in two fully-connected (Dense) layers which is just artificial an neural networks (ANN) classifier. In the last layer(Dense(10,activation=\"softmax\")) the net outputs distribution of probability of each class."}, {"metadata": {"trusted": true, "_uuid": "2d1658a1c2ea5379e1e2064f7fcd5ff1313046a4"}, "cell_type": "code", "source": "# Set the CNN model \n# my CNN architechture is In -> [[Conv2D->relu]*2 -> MaxPool2D -> Dropout]*2 -> Flatten -> Dense -> Dropout -> Out\ninput_shape = (75, 100, 3)\nnum_classes = 7\n\nmodel = Sequential()\nmodel.add(Conv2D(32, kernel_size=(3, 3),activation='relu',padding = 'Same',input_shape=input_shape))\nmodel.add(Conv2D(32,kernel_size=(3, 3), activation='relu',padding = 'Same',))\nmodel.add(MaxPool2D(pool_size = (2, 2)))\nmodel.add(Dropout(0.25))\n\nmodel.add(Conv2D(64, (3, 3), activation='relu',padding = 'Same'))\nmodel.add(Conv2D(64, (3, 3), activation='relu',padding = 'Same'))\nmodel.add(MaxPool2D(pool_size=(2, 2)))\nmodel.add(Dropout(0.40))\n\nmodel.add(Flatten())\nmodel.add(Dense(128, activation='relu'))\nmodel.add(Dropout(0.5))\nmodel.add(Dense(num_classes, activation='softmax'))\nmodel.summary()", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "e08855b5e5cad917a627b310748a10093395d8f3"}, "cell_type": "markdown", "source": "# Step 12: Setting Optimizer and Annealer\n\nOnce our layers are added to the model, we need to set up a score function, a loss function and an optimisation algorithm.\nWe define the loss function to measure how poorly our model performs on images with known labels. It is the error rate between the oberved labels and the predicted ones. We use a specific form for categorical classifications (>2 classes) called the \"categorical_crossentropy\".\nThe most important function is the optimizer. This function will iteratively improve parameters (filters kernel values, weights and bias of neurons ...) in order to minimise the loss.\nI choosed Adam optimizer because it combines the advantages of two other extensions of stochastic gradient descent. Specifically:\n\n1. Adaptive Gradient Algorithm (AdaGrad) that maintains a per-parameter learning rate that improves performance on problems with sparse gradients (e.g. natural language and computer vision problems).\n\n2. Root Mean Square Propagation (RMSProp) that also maintains per-parameter learning rates that are adapted based on the average of recent magnitudes of the gradients for the weight (e.g. how quickly it is changing). This means the algorithm does well on online and non-stationary problems (e.g. noisy).\n\n<PERSON> realizes the benefits of both AdaGrad and RMSProp.\n\nAdam is a popular algorithm in the field of deep learning because it achieves good results fast.\n\nThe metric function \"accuracy\" is used is to evaluate the performance our model. This metric function is similar to the loss function, except that the results from the metric evaluation are not used when training the model (only for evaluation)."}, {"metadata": {"trusted": true, "_uuid": "c754af8f1d34d3cfb6adb93e6824b5a5e6dc8506"}, "cell_type": "code", "source": "# Define the optimizer\noptimizer = <PERSON>(lr=0.001, beta_1=0.9, beta_2=0.999, epsilon=None, decay=0.0, amsgrad=False)", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "c34b677f1a804669e6e01042d0f15b3f29675e1b"}, "cell_type": "code", "source": "# Compile the model\nmodel.compile(optimizer = optimizer , loss = \"categorical_crossentropy\", metrics=[\"accuracy\"])", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "11526af016938dab9e9799cdb13fae7f353ca7fb"}, "cell_type": "code", "source": "# Set a learning rate annealer\nlearning_rate_reduction = ReduceLROnPlateau(monitor='val_acc', \n                                            patience=3, \n                                            verbose=1, \n                                            factor=0.5, \n                                            min_lr=0.00001)", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "ee70861394de8ca8bea6d611ffbe8dd8d9d3b9f2"}, "cell_type": "markdown", "source": "In order to make the optimizer converge faster and closest to the global minimum of the loss function, i used an annealing method of the learning rate (LR).\n\nThe LR is the step by which the optimizer walks through the 'loss landscape'. The higher LR, the bigger are the steps and the quicker is the convergence. However the sampling is very poor with an high LR and the optimizer could probably fall into a local minima.\n\nIts better to have a decreasing learning rate during the training to reach efficiently the global minimum of the loss function.\n\nTo keep the advantage of the fast computation time with a high LR, i decreased the LR dynamically every X steps (epochs) depending if it is necessary (when accuracy is not improved).\n\nWith the ReduceLROnPlateau function from Keras.callbacks, i choose to reduce the LR by half if the accuracy is not improved after 3 epochs."}, {"metadata": {"_uuid": "84be7b2fa9289e5653d779e8ecc75a5b612500d7"}, "cell_type": "markdown", "source": "# Data Augmentation\nIt is the optional step. In order to avoid overfitting problem, we need to expand artificially our HAM 10000 dataset. We can make your existing dataset even larger. The idea is to alter the training data with small transformations to reproduce the variations \n\nApproaches that alter the training data in ways that change the array representation while keeping the label the same are known as data augmentation techniques. Some popular augmentations people use are grayscales, horizontal flips, vertical flips, random crops, color jitters, translations, rotations, and much more.\n\nBy applying just a couple of these transformations to our training data, we can easily double or triple the number of training examples and create a very robust model."}, {"metadata": {"trusted": true, "_uuid": "9eb25849d947e28d0daa18df08fa9ab84043263a"}, "cell_type": "code", "source": "# With data augmentation to prevent overfitting \n\ndatagen = ImageDataGenerator(\n        featurewise_center=False,  # set input mean to 0 over the dataset\n        samplewise_center=False,  # set each sample mean to 0\n        featurewise_std_normalization=False,  # divide inputs by std of the dataset\n        samplewise_std_normalization=False,  # divide each input by its std\n        zca_whitening=False,  # apply ZCA whitening\n        rotation_range=10,  # randomly rotate images in the range (degrees, 0 to 180)\n        zoom_range = 0.1, # Randomly zoom image \n        width_shift_range=0.1,  # randomly shift images horizontally (fraction of total width)\n        height_shift_range=0.1,  # randomly shift images vertically (fraction of total height)\n        horizontal_flip=False,  # randomly flip images\n        vertical_flip=False)  # randomly flip images\n\ndatagen.fit(x_train)\n", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "1dffc6cc83cb5c8940410f1c73d0a4074f8efe10"}, "cell_type": "markdown", "source": "For the data augmentation, i choosed to :\nRandomly rotate some training images by 10 degrees Randomly Zoom by 10% some training images Randomly shift images horizontally by 10% of the width Randomly shift images vertically by 10% of the height \nOnce our model is ready, we fit the training dataset ."}, {"metadata": {"_uuid": "e7644b4ef037b8e051466691e2e2197364be43b2"}, "cell_type": "markdown", "source": "# Step 13: Fitting the model\nIn this step finally I fit the model into x_train, y_train. In this step I have choosen batch size of 10 and 50 epochs   as small as your batch size will be more efficiently your model will train and I have choosen 50 epochs to give the model sufficient epochs to train "}, {"metadata": {"trusted": true, "_uuid": "55083e7f7d76cb7131b655701021ba2745627c43"}, "cell_type": "code", "source": "# Fit the model\nepochs = 50 \nbatch_size = 10\nhistory = model.fit_generator(datagen.flow(x_train,y_train, batch_size=batch_size),\n                              epochs = epochs, validation_data = (x_validate,y_validate),\n                              verbose = 1, steps_per_epoch=x_train.shape[0] // batch_size\n                              , callbacks=[learning_rate_reduction])", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "6d4fc72b2aa6fb8848c51257affb6fb872efb725"}, "cell_type": "markdown", "source": "# Step 14: Model Evaluation \nIn this step we will check the testing accuracy and validation accuracy of our model,plot confusion matrix and also check the missclassified images count of each type"}, {"metadata": {"trusted": true, "_uuid": "23eb3c8cbfcd575973d680898e6235b818ed241a"}, "cell_type": "code", "source": "loss, accuracy = model.evaluate(x_test, y_test, verbose=1)\nloss_v, accuracy_v = model.evaluate(x_validate, y_validate, verbose=1)\nprint(\"Validation: accuracy = %f  ;  loss_v = %f\" % (accuracy_v, loss_v))\nprint(\"Test: accuracy = %f  ;  loss = %f\" % (accuracy, loss))\nmodel.save(\"model.h5\")\n", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "7b1b665dacff5521aa9d5c2c0926119a3829d623"}, "cell_type": "code", "source": "plot_model_history(history)", "execution_count": null, "outputs": []}, {"metadata": {"trusted": true, "_uuid": "b43ebca1425823701fc4fc349af72b5fe8677e04"}, "cell_type": "code", "source": "# Function to plot confusion matrix    \ndef plot_confusion_matrix(cm, classes,\n                          normalize=False,\n                          title='Confusion matrix',\n                          cmap=plt.cm.Blues):\n    \"\"\"\n    This function prints and plots the confusion matrix.\n    Normalization can be applied by setting `normalize=True`.\n    \"\"\"\n    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n    plt.title(title)\n    plt.colorbar()\n    tick_marks = np.arange(len(classes))\n    plt.xticks(tick_marks, classes, rotation=45)\n    plt.yticks(tick_marks, classes)\n\n    if normalize:\n        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n\n    thresh = cm.max() / 2.\n    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n        plt.text(j, i, cm[i, j],\n                 horizontalalignment=\"center\",\n                 color=\"white\" if cm[i, j] > thresh else \"black\")\n\n    plt.tight_layout()\n    plt.ylabel('True label')\n    plt.xlabel('Predicted label')\n\n# Predict the values from the validation dataset\nY_pred = model.predict(x_validate)\n# Convert predictions classes to one hot vectors \nY_pred_classes = np.argmax(Y_pred,axis = 1) \n# Convert validation observations to one hot vectors\nY_true = np.argmax(y_validate,axis = 1) \n# compute the confusion matrix\nconfusion_mtx = confusion_matrix(Y_true, Y_pred_classes)\n\n \n\n# plot the confusion matrix\nplot_confusion_matrix(confusion_mtx, classes = range(7)) ", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "4db6351f7614f4be476a985ed5eeab24943f36dc"}, "cell_type": "markdown", "source": "Now, lets which category has much incorrect predictions"}, {"metadata": {"trusted": true, "_uuid": "e8499e6c3506c816ea86ca97c8461d5c040b6a3e"}, "cell_type": "code", "source": "label_frac_error = 1 - np.diag(confusion_mtx) / np.sum(confusion_mtx, axis=1)\nplt.bar(np.arange(7),label_frac_error)\nplt.xlabel('True Label')\nplt.ylabel('Fraction classified incorrectly')", "execution_count": null, "outputs": []}, {"metadata": {"_uuid": "08f770804c454d643ee3774a9843bcf155a7436f"}, "cell_type": "markdown", "source": "# Conclusion\nIt seems our model has maximum number of incorrect predictions for Basal cell carcinoma which has code 3, then second most missclassified type is Vascular lesions code 5 then Melanocytic nevi code  0 where as Actinic keratoses code 4 has least misclassified type.\n\nWe can also further tune our model to easily achieve the accuracy above 80% and I think still this model is efficient in comparison to detection with human eyes having 77.0344% accuracy \n\nI hope kagglers like my stepwise approach to classify cancer types. If like then kindly dont forget to hit the **like**\n"}, {"metadata": {"trusted": true, "_uuid": "e78862469a6b3df331180843ecbb41dd5a182314"}, "cell_type": "code", "source": "", "execution_count": null, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.6.6", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}}, "nbformat": 4, "nbformat_minor": 1}