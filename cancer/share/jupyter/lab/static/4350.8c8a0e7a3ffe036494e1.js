(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[4350],{32017:e=>{"use strict";e.exports=function e(n,t){if(n===t)return true;if(n&&t&&typeof n=="object"&&typeof t=="object"){if(n.constructor!==t.constructor)return false;var i,r,s;if(Array.isArray(n)){i=n.length;if(i!=t.length)return false;for(r=i;r--!==0;)if(!e(n[r],t[r]))return false;return true}if(n.constructor===RegExp)return n.source===t.source&&n.flags===t.flags;if(n.valueOf!==Object.prototype.valueOf)return n.valueOf()===t.valueOf();if(n.toString!==Object.prototype.toString)return n.toString()===t.toString();s=Object.keys(n);i=s.length;if(i!==Object.keys(t).length)return false;for(r=i;r--!==0;)if(!Object.prototype.hasOwnProperty.call(t,s[r]))return false;for(r=i;r--!==0;){var o=s[r];if(!e(n[o],t[o]))return false}return true}return n!==n&&t!==t}},72492:e=>{"use strict";e.exports=function(e,n){if(!n)n={};if(typeof n==="function")n={cmp:n};var t=typeof n.cycles==="boolean"?n.cycles:false;var i=n.cmp&&function(e){return function(n){return function(t,i){var r={key:t,value:n[t]};var s={key:i,value:n[i]};return e(r,s)}}}(n.cmp);var r=[];return function e(n){if(n&&n.toJSON&&typeof n.toJSON==="function"){n=n.toJSON()}if(n===undefined)return;if(typeof n=="number")return isFinite(n)?""+n:"null";if(typeof n!=="object")return JSON.stringify(n);var s,o;if(Array.isArray(n)){o="[";for(s=0;s<n.length;s++){if(s)o+=",";o+=e(n[s])||"null"}return o+"]"}if(n===null)return"null";if(r.indexOf(n)!==-1){if(t)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var a=r.push(n)-1;var u=Object.keys(n).sort(i&&i(n));o="";for(s=0;s<u.length;s++){var c=u[s];var l=e(n[c]);if(!l)continue;if(o)o+=",";o+=JSON.stringify(c)+":"+l}r.splice(a,1);return"{"+o+"}"}(e)}},45948:(e,n,t)=>{"use strict";t.d(n,{P:()=>h});const i="view",r="[",s="]",o="{",a="}",u=":",c=",",l="@",f=">",d=/[[\]{}]/,p={"*":1,arc:1,area:1,group:1,image:1,line:1,path:1,rect:1,rule:1,shape:1,symbol:1,text:1,trail:1};let g,m;function h(e,n,t){g=n||i;m=t||p;return v(e.trim()).map(O)}function b(e){return m[e]}function y(e,n,t,i,r){const s=e.length;let o=0,a;for(;n<s;++n){a=e[n];if(!o&&a===t)return n;else if(r&&r.indexOf(a)>=0)--o;else if(i&&i.indexOf(a)>=0)++o}return n}function v(e){const n=[],t=e.length;let i=0,u=0;while(u<t){u=y(e,u,c,r+o,s+a);n.push(e.substring(i,u).trim());i=++u}if(n.length===0){throw"Empty event selector: "+e}return n}function O(e){return e[0]==="["?x(e):w(e)}function x(e){const n=e.length;let t=1,i;t=y(e,t,s,r,s);if(t===n){throw"Empty between selector: "+e}i=v(e.substring(1,t));if(i.length!==2){throw"Between selector must have two elements: "+e}e=e.slice(t+1).trim();if(e[0]!==f){throw"Expected '>' after between selector: "+e}i=i.map(O);const o=O(e.slice(1).trim());if(o.between){return{between:i,stream:o}}else{o.between=i}return o}function w(e){const n={source:g},t=[];let i=[0,0],c=0,f=0,p=e.length,m=0,h,v;if(e[p-1]===a){m=e.lastIndexOf(o);if(m>=0){try{i=j(e.substring(m+1,p-1))}catch(O){throw"Invalid throttle specification: "+e}e=e.slice(0,m).trim();p=e.length}else throw"Unmatched right brace: "+e;m=0}if(!p)throw e;if(e[0]===l)c=++m;h=y(e,m,u);if(h<p){t.push(e.substring(f,h).trim());f=m=++h}m=y(e,m,r);if(m===p){t.push(e.substring(f,p).trim())}else{t.push(e.substring(f,m).trim());v=[];f=++m;if(f===p)throw"Unmatched left bracket: "+e}while(m<p){m=y(e,m,s);if(m===p)throw"Unmatched left bracket: "+e;v.push(e.substring(f,m).trim());if(m<p-1&&e[++m]!==r)throw"Expected left bracket: "+e;f=++m}if(!(p=t.length)||d.test(t[p-1])){throw"Invalid event selector: "+e}if(p>1){n.type=t[1];if(c){n.markname=t[0].slice(1)}else if(b(t[0])){n.marktype=t[0]}else{n.source=t[0]}}else{n.type=t[0]}if(n.type.slice(-1)==="!"){n.consume=true;n.type=n.type.slice(0,-1)}if(v!=null)n.filter=v;if(i[0])n.throttle=i[0];if(i[1])n.debounce=i[1];return n}function j(e){const n=e.split(c);if(!e.length||n.length>2)throw e;return n.map((n=>{const t=+n;if(t!==t)throw e;return t}))}},54350:(e,n,t)=>{"use strict";t.r(n);t.d(n,{accessPathDepth:()=>Q,accessPathWithDatum:()=>W,compile:()=>Iw,contains:()=>F,deepEqual:()=>h,deleteNestedProperty:()=>R,duplicate:()=>b,entries:()=>M,every:()=>D,fieldIntersection:()=>_,flatAccessWithDatum:()=>H,getFirstDefined:()=>X,hasIntersection:()=>B,hash:()=>w,internalField:()=>ne,isBoolean:()=>L,isEmpty:()=>z,isEqual:()=>S,isInternalField:()=>te,isNullOrFalse:()=>j,isNumeric:()=>re,keys:()=>N,logicalExpr:()=>U,mergeDeep:()=>A,never:()=>y,normalize:()=>bd,normalizeAngle:()=>ie,omit:()=>O,pick:()=>v,prefixGenerator:()=>P,removePathFromField:()=>V,replaceAll:()=>K,replacePathInField:()=>Y,resetIdCounter:()=>ee,setEqual:()=>E,some:()=>$,stringify:()=>x,titleCase:()=>I,unique:()=>C,uniqueId:()=>Z,vals:()=>T,varName:()=>q,version:()=>Gw});const i={rE:"5.6.1"};var r=t(26372);var s=t(18729);var o=t.n(s);var a=t(32017);var u=t.n(a);var c=t(72492);var l=t.n(c);function f(e){return!!e.or}function d(e){return!!e.and}function p(e){return!!e.not}function g(e,n){if(p(e)){g(e.not,n)}else if(d(e)){for(const t of e.and){g(t,n)}}else if(f(e)){for(const t of e.or){g(t,n)}}else{n(e)}}function m(e,n){if(p(e)){return{not:m(e.not,n)}}else if(d(e)){return{and:e.and.map((e=>m(e,n)))}}else if(f(e)){return{or:e.or.map((e=>m(e,n)))}}else{return n(e)}}const h=u();const b=o();function y(e){throw new Error(e)}function v(e,n){const t={};for(const i of n){if((0,r.mQ)(e,i)){t[i]=e[i]}}return t}function O(e,n){const t=Object.assign({},e);for(const i of n){delete t[i]}return t}Set.prototype["toJSON"]=function(){return`Set(${[...this].map((e=>l()(e))).join(",")})`};const x=l();function w(e){if((0,r.Et)(e)){return e}const n=(0,r.Kg)(e)?e:l()(e);if(n.length<250){return n}let t=0;for(let i=0;i<n.length;i++){const e=n.charCodeAt(i);t=(t<<5)-t+e;t=t&t}return t}function j(e){return e===false||e===null}function F(e,n){return e.includes(n)}function $(e,n){let t=0;for(const[i,r]of e.entries()){if(n(r,i,t++)){return true}}return false}function D(e,n){let t=0;for(const[i,r]of e.entries()){if(!n(r,i,t++)){return false}}return true}function A(e,...n){for(const t of n){k(e,t!==null&&t!==void 0?t:{})}return e}function k(e,n){for(const t of N(n)){(0,r.AU)(e,t,n[t],true)}}function C(e,n){const t=[];const i={};let r;for(const s of e){r=n(s);if(r in i){continue}i[r]=1;t.push(s)}return t}function S(e,n){const t=N(e);const i=N(n);if(t.length!==i.length){return false}for(const r of t){if(e[r]!==n[r]){return false}}return true}function E(e,n){if(e.size!==n.size){return false}for(const t of e){if(!n.has(t)){return false}}return true}function B(e,n){for(const t of e){if(n.has(t)){return true}}return false}function P(e){const n=new Set;for(const t of e){const e=(0,r.iv)(t);const i=e.map(((e,n)=>n===0?e:`[${e}]`));const s=i.map(((e,n)=>i.slice(0,n+1).join("")));for(const t of s){n.add(t)}}return n}function _(e,n){if(e===undefined||n===undefined){return true}return B(P(e),P(n))}function z(e){return N(e).length===0}const N=Object.keys;const T=Object.values;const M=Object.entries;function L(e){return e===true||e===false}function q(e){const n=e.replace(/\W/g,"_");return(e.match(/^\d+/)?"_":"")+n}function U(e,n){if(p(e)){return`!(${U(e.not,n)})`}else if(d(e)){return`(${e.and.map((e=>U(e,n))).join(") && (")})`}else if(f(e)){return`(${e.or.map((e=>U(e,n))).join(") || (")})`}else{return n(e)}}function R(e,n){if(n.length===0){return true}const t=n.shift();if(t in e&&R(e[t],n)){delete e[t]}return z(e)}function I(e){return e.charAt(0).toUpperCase()+e.substr(1)}function W(e,n="datum"){const t=(0,r.iv)(e);const i=[];for(let s=1;s<=t.length;s++){const e=`[${t.slice(0,s).map(r.r$).join("][")}]`;i.push(`${n}${e}`)}return i.join(" && ")}function H(e,n="datum"){return`${n}[${(0,r.r$)((0,r.iv)(e).join("."))}]`}function G(e){return e.replace(/(\[|\]|\.|'|")/g,"\\$1")}function Y(e){return`${(0,r.iv)(e).map(G).join("\\.")}`}function K(e,n,t){return e.replace(new RegExp(n.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"g"),t)}function V(e){return`${(0,r.iv)(e).join(".")}`}function Q(e){if(!e){return 0}return(0,r.iv)(e).length}function X(...e){for(const n of e){if(n!==undefined){return n}}return undefined}let J=42;function Z(e){const n=++J;return e?String(e)+n:n}function ee(){J=42}function ne(e){return te(e)?e:`__${e}`}function te(e){return e.startsWith("__")}function ie(e){if(e===undefined){return undefined}return(e%360+360)%360}function re(e){if((0,r.Et)(e)){return true}return!isNaN(e)&&!isNaN(parseFloat(e))}var se=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};const oe="row";const ae="column";const ue="facet";const ce="x";const le="y";const fe="x2";const de="y2";const pe="xOffset";const ge="yOffset";const me="radius";const he="radius2";const be="theta";const ye="theta2";const ve="latitude";const Oe="longitude";const xe="latitude2";const we="longitude2";const je="color";const Fe="fill";const $e="stroke";const De="shape";const Ae="size";const ke="angle";const Ce="opacity";const Se="fillOpacity";const Ee="strokeOpacity";const Be="strokeWidth";const Pe="strokeDash";const _e="text";const ze="order";const Ne="detail";const Te="key";const Me="tooltip";const Le="href";const qe="url";const Ue="description";const Re={x:1,y:1,x2:1,y2:1};const Ie={theta:1,theta2:1,radius:1,radius2:1};function We(e){return e in Ie}const He={longitude:1,longitude2:1,latitude:1,latitude2:1};function Ge(e){switch(e){case ve:return"y";case xe:return"y2";case Oe:return"x";case we:return"x2"}}function Ye(e){return e in He}const Ke=N(He);const Ve=Object.assign(Object.assign(Object.assign(Object.assign({},Re),Ie),He),{xOffset:1,yOffset:1,color:1,fill:1,stroke:1,opacity:1,fillOpacity:1,strokeOpacity:1,strokeWidth:1,strokeDash:1,size:1,angle:1,shape:1,order:1,text:1,detail:1,key:1,tooltip:1,href:1,url:1,description:1});function Qe(e){return e===je||e===Fe||e===$e}const Xe={row:1,column:1,facet:1};const Je=N(Xe);const Ze=Object.assign(Object.assign({},Ve),Xe);const en=N(Ze);const{order:nn,detail:tn,tooltip:rn}=Ze,sn=se(Ze,["order","detail","tooltip"]);const{row:on,column:an,facet:un}=sn,cn=se(sn,["row","column","facet"]);const ln=N(sn);const fn=N(cn);function dn(e){return!!cn[e]}function pn(e){return!!Ze[e]}const gn=[fe,de,xe,we,ye,he];function mn(e){const n=hn(e);return n!==e}function hn(e){switch(e){case fe:return ce;case de:return le;case xe:return ve;case we:return Oe;case ye:return be;case he:return me}return e}function bn(e){if(We(e)){switch(e){case be:return"startAngle";case ye:return"endAngle";case me:return"outerRadius";case he:return"innerRadius"}}return e}function yn(e){switch(e){case ce:return fe;case le:return de;case ve:return xe;case Oe:return we;case be:return ye;case me:return he}return undefined}function vn(e){switch(e){case ce:case fe:return"width";case le:case de:return"height"}return undefined}function On(e){switch(e){case ce:return"xOffset";case le:return"yOffset";case fe:return"x2Offset";case de:return"y2Offset";case be:return"thetaOffset";case me:return"radiusOffset";case ye:return"theta2Offset";case he:return"radius2Offset"}return undefined}function xn(e){switch(e){case ce:return"xOffset";case le:return"yOffset"}return undefined}function wn(e){switch(e){case"xOffset":return"x";case"yOffset":return"y"}}const jn=N(Ve);const{x:Fn,y:$n,x2:Dn,y2:An,xOffset:kn,yOffset:Cn,latitude:Sn,longitude:En,latitude2:Bn,longitude2:Pn,theta:_n,theta2:zn,radius:Nn,radius2:Tn}=Ve,Mn=se(Ve,["x","y","x2","y2","xOffset","yOffset","latitude","longitude","latitude2","longitude2","theta","theta2","radius","radius2"]);const Ln=N(Mn);const qn={x:1,y:1};const Un=N(qn);function Rn(e){return e in qn}const In={theta:1,radius:1};const Wn=N(In);function Hn(e){return e==="width"?ce:le}const Gn={xOffset:1,yOffset:1};const Yn=N(Gn);function Kn(e){return e in Gn}const{text:Vn,tooltip:Qn,href:Xn,url:Jn,description:Zn,detail:et,key:nt,order:tt}=Mn,it=se(Mn,["text","tooltip","href","url","description","detail","key","order"]);const rt=N(it);function st(e){return!!Mn[e]}function ot(e){switch(e){case je:case Fe:case $e:case Ae:case De:case Ce:case Be:case Pe:return true;case Se:case Ee:case ke:return false}}const at=Object.assign(Object.assign(Object.assign(Object.assign({},qn),In),Gn),it);const ut=N(at);function ct(e){return!!at[e]}function lt(e,n){return gt(e)[n]}const ft={arc:"always",area:"always",bar:"always",circle:"always",geoshape:"always",image:"always",line:"always",rule:"always",point:"always",rect:"always",square:"always",trail:"always",text:"always",tick:"always"};const{geoshape:dt}=ft,pt=se(ft,["geoshape"]);function gt(e){switch(e){case je:case Fe:case $e:case Ue:case Ne:case Te:case Me:case Le:case ze:case Ce:case Se:case Ee:case Be:case ue:case oe:case ae:return ft;case ce:case le:case pe:case ge:case ve:case Oe:return pt;case fe:case de:case xe:case we:return{area:"always",bar:"always",image:"always",rect:"always",rule:"always",circle:"binned",point:"binned",square:"binned",tick:"binned",line:"binned",trail:"binned"};case Ae:return{point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",text:"always",line:"always",trail:"always"};case Pe:return{line:"always",point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",geoshape:"always"};case De:return{point:"always",geoshape:"always"};case _e:return{text:"always"};case ke:return{point:"always",square:"always",text:"always"};case qe:return{image:"always"};case be:return{text:"always",arc:"always"};case me:return{text:"always",arc:"always"};case ye:case he:return{arc:"always"}}}function mt(e){switch(e){case ce:case le:case be:case me:case pe:case ge:case Ae:case ke:case Be:case Ce:case Se:case Ee:case fe:case de:case ye:case he:return undefined;case ue:case oe:case ae:case De:case Pe:case _e:case Me:case Le:case qe:case Ue:return"discrete";case je:case Fe:case $e:return"flexible";case ve:case Oe:case xe:case we:case Ne:case Te:case ze:return undefined}}const ht={argmax:1,argmin:1,average:1,count:1,distinct:1,product:1,max:1,mean:1,median:1,min:1,missing:1,q1:1,q3:1,ci0:1,ci1:1,stderr:1,stdev:1,stdevp:1,sum:1,valid:1,values:1,variance:1,variancep:1};const bt={count:1,min:1,max:1};function yt(e){return!!e&&!!e["argmin"]}function vt(e){return!!e&&!!e["argmax"]}function Ot(e){return(0,r.Kg)(e)&&!!ht[e]}const xt=new Set(["count","valid","missing","distinct"]);function wt(e){return(0,r.Kg)(e)&&xt.has(e)}function jt(e){return(0,r.Kg)(e)&&F(["min","max"],e)}const Ft=new Set(["count","sum","distinct","valid","missing"]);const $t=new Set(["mean","average","median","q1","q3","min","max"]);function Dt(e){if((0,r.Lm)(e)){e=Yu(e,undefined)}return"bin"+N(e).map((n=>St(e[n])?q(`_${n}_${M(e[n])}`):q(`_${n}_${e[n]}`))).join("")}function At(e){return e===true||Ct(e)&&!e.binned}function kt(e){return e==="binned"||Ct(e)&&e.binned===true}function Ct(e){return(0,r.Gv)(e)}function St(e){return e===null||e===void 0?void 0:e["param"]}function Et(e){switch(e){case oe:case ae:case Ae:case je:case Fe:case $e:case Be:case Ce:case Se:case Ee:case De:return 6;case Pe:return 4;default:return 10}}function Bt(e){return!!(e===null||e===void 0?void 0:e.expr)}function Pt(e){const n=N(e||{});const t={};for(const i of n){t[i]=Vt(e[i])}return t}var _t=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function zt(e){const{anchor:n,frame:t,offset:i,orient:r,angle:s,limit:o,color:a,subtitleColor:u,subtitleFont:c,subtitleFontSize:l,subtitleFontStyle:f,subtitleFontWeight:d,subtitleLineHeight:p,subtitlePadding:g}=e,m=_t(e,["anchor","frame","offset","orient","angle","limit","color","subtitleColor","subtitleFont","subtitleFontSize","subtitleFontStyle","subtitleFontWeight","subtitleLineHeight","subtitlePadding"]);const h=Object.assign(Object.assign({},m),a?{fill:a}:{});const b=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},n?{anchor:n}:{}),t?{frame:t}:{}),i?{offset:i}:{}),r?{orient:r}:{}),s!==undefined?{angle:s}:{}),o!==undefined?{limit:o}:{});const y=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},u?{subtitleColor:u}:{}),c?{subtitleFont:c}:{}),l?{subtitleFontSize:l}:{}),f?{subtitleFontStyle:f}:{}),d?{subtitleFontWeight:d}:{}),p?{subtitleLineHeight:p}:{}),g?{subtitlePadding:g}:{});const O=v(e,["align","baseline","dx","dy","limit"]);return{titleMarkConfig:h,subtitleMarkConfig:O,nonMarkTitleProperties:b,subtitle:y}}function Nt(e){return(0,r.Kg)(e)||(0,r.cy)(e)&&(0,r.Kg)(e[0])}function Tt(e){return!!(e===null||e===void 0?void 0:e.signal)}function Mt(e){return!!e["step"]}function Lt(e){if(!(0,r.cy)(e)){return"fields"in e&&!("data"in e)}return false}function qt(e){if(!(0,r.cy)(e)){return"fields"in e&&"data"in e}return false}function Ut(e){if(!(0,r.cy)(e)){return"field"in e&&"data"in e}return false}const Rt={aria:1,description:1,ariaRole:1,ariaRoleDescription:1,blend:1,opacity:1,fill:1,fillOpacity:1,stroke:1,strokeCap:1,strokeWidth:1,strokeOpacity:1,strokeDash:1,strokeDashOffset:1,strokeJoin:1,strokeOffset:1,strokeMiterLimit:1,startAngle:1,endAngle:1,padAngle:1,innerRadius:1,outerRadius:1,size:1,shape:1,interpolate:1,tension:1,orient:1,align:1,baseline:1,text:1,dir:1,dx:1,dy:1,ellipsis:1,limit:1,radius:1,theta:1,angle:1,font:1,fontSize:1,fontWeight:1,fontStyle:1,lineBreak:1,lineHeight:1,cursor:1,href:1,tooltip:1,cornerRadius:1,cornerRadiusTopLeft:1,cornerRadiusTopRight:1,cornerRadiusBottomLeft:1,cornerRadiusBottomRight:1,aspect:1,width:1,height:1,url:1,smooth:1};const It=N(Rt);const Wt={arc:1,area:1,group:1,image:1,line:1,path:1,rect:1,rule:1,shape:1,symbol:1,text:1,trail:1};const Ht=["cornerRadius","cornerRadiusTopLeft","cornerRadiusTopRight","cornerRadiusBottomLeft","cornerRadiusBottomRight"];var Gt=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};const Yt=" – ";function Kt(e){const n=(0,r.cy)(e.condition)?e.condition.map(Qt):Qt(e.condition);return Object.assign(Object.assign({},Vt(e)),{condition:n})}function Vt(e){if(Bt(e)){const{expr:n}=e,t=Gt(e,["expr"]);return Object.assign({signal:n},t)}return e}function Qt(e){if(Bt(e)){const{expr:n}=e,t=Gt(e,["expr"]);return Object.assign({signal:n},t)}return e}function Xt(e){if(Bt(e)){const{expr:n}=e,t=Gt(e,["expr"]);return Object.assign({signal:n},t)}if(Tt(e)){return e}return e!==undefined?{value:e}:undefined}function Jt(e){if(Tt(e)){return e.signal}return(0,r.r$)(e)}function Zt(e){if(Tt(e)){return e.signal}return(0,r.r$)(e.value)}function ei(e){if(Tt(e)){return e.signal}return e==null?null:(0,r.r$)(e)}function ni(e,n,t){for(const i of t){const t=ri(i,n.markDef,n.config);if(t!==undefined){e[i]=Xt(t)}}return e}function ti(e){var n;return[].concat(e.type,(n=e.style)!==null&&n!==void 0?n:[])}function ii(e,n,t,i={}){const{vgChannel:r,ignoreVgConfig:s}=i;if(r&&n[r]!==undefined){return n[r]}else if(n[e]!==undefined){return n[e]}else if(s&&(!r||r===e)){return undefined}return ri(e,n,t,i)}function ri(e,n,t,{vgChannel:i}={}){return X(i?si(e,n,t.style):undefined,si(e,n,t.style),i?t[n.type][i]:undefined,t[n.type][e],i?t.mark[i]:t.mark[e])}function si(e,n,t){return oi(e,ti(n),t)}function oi(e,n,t){n=(0,r.YO)(n);let i;for(const r of n){const n=t[r];if(n&&n[e]!==undefined){i=n[e]}}return i}function ai(e,n){return(0,r.YO)(e).reduce(((e,t)=>{var i;e.field.push(Du(t,n));e.order.push((i=t.sort)!==null&&i!==void 0?i:"ascending");return e}),{field:[],order:[]})}function ui(e,n){const t=[...e];n.forEach((e=>{for(const n of t){if(h(n,e)){return}}t.push(e)}));return t}function ci(e,n){if(h(e,n)||!n){return e}else if(!e){return n}else{return[...(0,r.YO)(e),...(0,r.YO)(n)].join(", ")}}function li(e,n){const t=e.value;const i=n.value;if(t==null||i===null){return{explicit:e.explicit,value:null}}else if((Nt(t)||Tt(t))&&(Nt(i)||Tt(i))){return{explicit:e.explicit,value:ci(t,i)}}else if(Nt(t)||Tt(t)){return{explicit:e.explicit,value:t}}else if(Nt(i)||Tt(i)){return{explicit:e.explicit,value:i}}else if(!Nt(t)&&!Tt(t)&&!Nt(i)&&!Tt(i)){return{explicit:e.explicit,value:ui(t,i)}}throw new Error("It should never reach here")}function fi(e){return`Invalid specification ${x(e)}. Make sure the specification includes at least one of the following properties: "mark", "layer", "facet", "hconcat", "vconcat", "concat", or "repeat".`}const di='Autosize "fit" only works for single views and layered views.';function pi(e){const n=e=="width"?"Width":"Height";return`${n} "container" only works for single views and layered views.`}function gi(e){const n=e=="width"?"Width":"Height";const t=e=="width"?"x":"y";return`${n} "container" only works well with autosize "fit" or "fit-${t}".`}function mi(e){return e?`Dropping "fit-${e}" because spec has discrete ${vn(e)}.`:`Dropping "fit" because spec has discrete size.`}function hi(e){return`Unknown field for ${e}. Cannot calculate view size.`}function bi(e){return`Cannot project a selection on encoding channel "${e}", which has no field.`}function yi(e,n){return`Cannot project a selection on encoding channel "${e}" as it uses an aggregate function ("${n}").`}function vi(e){return`The "nearest" transform is not supported for ${e} marks.`}function Oi(e){return`Selection not supported for ${e} yet.`}function xi(e){return`Cannot find a selection named "${e}".`}const wi="Scale bindings are currently only supported for scales with unbinned, continuous domains.";const ji="Legend bindings are only supported for selections over an individual field or encoding channel.";function Fi(e){return`Lookups can only be performed on selection parameters. "${e}" is a variable parameter.`}function $i(e){return`Cannot define and lookup the "${e}" selection in the same view. `+`Try moving the lookup into a second, layered view?`}const Di="The same selection must be used to override scale domains in a layered view.";const Ai='Interval selections should be initialized using "x" and/or "y" keys.';function ki(e){return`Unknown repeated value "${e}".`}function Ci(e){return`The "columns" property cannot be used when "${e}" has nested row/column.`}const Si="Axes cannot be shared in concatenated or repeated views yet (https://github.com/vega/vega-lite/issues/2415).";function Ei(e){return`Unrecognized parse "${e}".`}function Bi(e,n,t){return`An ancestor parsed field "${e}" as ${t} but a child wants to parse the field as ${n}.`}const Pi="Attempt to add the same child twice.";function _i(e){return`Ignoring an invalid transform: ${x(e)}.`}const zi='If "from.fields" is not specified, "as" has to be a string that specifies the key to be used for the data from the secondary source.';function Ni(e){return`Config.customFormatTypes is not true, thus custom format type and format for channel ${e} are dropped.`}function Ti(e){const{parentProjection:n,projection:t}=e;return`Layer's shared projection ${x(n)} is overridden by a child projection ${x(t)}.`}const Mi="Arc marks uses theta channel rather than angle, replacing angle with theta.";function Li(e){return`${e}Offset dropped because ${e} is continuous`}function qi(e){return`There is no ${e} encoding. Replacing ${e}Offset encoding as ${e}.`}function Ui(e,n,t){return`Channel ${e} is a ${n}. Converted to {value: ${x(t)}}.`}function Ri(e){return`Invalid field type "${e}".`}function Ii(e,n){return`Invalid field type "${e}" for aggregate: "${n}", using "quantitative" instead.`}function Wi(e){return`Invalid aggregation operator "${e}".`}function Hi(e,n){return`Missing type for channel "${e}", using "${n}" instead.`}function Gi(e,n){const{fill:t,stroke:i}=n;return`Dropping color ${e} as the plot also has ${t&&i?"fill and stroke":t?"fill":"stroke"}.`}function Yi(e){return`Position range does not support relative band size for ${e}.`}function Ki(e,n){return`Dropping ${x(e)} from channel "${n}" since it does not contain any data field, datum, value, or signal.`}const Vi="Line marks cannot encode size with a non-groupby field. You may want to use trail marks instead.";function Qi(e,n,t){return`${e} dropped as it is incompatible with "${n}"${t?` when ${t}`:""}.`}function Xi(e){return`${e} encoding has no scale, so specified scale is ignored.`}function Ji(e){return`${e}-encoding is dropped as ${e} is not a valid encoding channel.`}function Zi(e){return`${e} encoding should be discrete (ordinal / nominal / binned).`}function er(e){return`${e} encoding should be discrete (ordinal / nominal / binned) or use a discretizing scale (e.g. threshold).`}function nr(e){return`Facet encoding dropped as ${e.join(" and ")} ${e.length>1?"are":"is"} also specified.`}function tr(e,n){return`Using discrete channel "${e}" to encode "${n}" field can be misleading as it does not encode ${n==="ordinal"?"order":"magnitude"}.`}function ir(e){return`The ${e} for range marks cannot be an expression`}function rr(e,n){const t=e&&n?"x2 and y2":e?"x2":"y2";return`Line mark is for continuous lines and thus cannot be used with ${t}. We will use the rule mark (line segments) instead.`}function sr(e,n){return`Specified orient "${e}" overridden with "${n}".`}const or="Custom domain scale cannot be unioned with default field-based domain.";function ar(e){return`Cannot use the scale property "${e}" with non-color channel.`}function ur(e){return`Cannot use the relative band size with ${e} scale.`}function cr(e){return`Using unaggregated domain with raw field has no effect (${x(e)}).`}function lr(e){return`Unaggregated domain not applicable for "${e}" since it produces values outside the origin domain of the source data.`}function fr(e){return`Unaggregated domain is currently unsupported for log scale (${x(e)}).`}function dr(e){return`Cannot apply size to non-oriented mark "${e}".`}function pr(e,n,t){return`Channel "${e}" does not work with "${n}" scale. We are using "${t}" scale instead.`}function gr(e,n){return`FieldDef does not work with "${e}" scale. We are using "${n}" scale instead.`}function mr(e,n,t){return`${t}-scale's "${n}" is dropped as it does not work with ${e} scale.`}function hr(e,n){return`Scale type "${n}" does not work with mark "${e}".`}function br(e){return`The step for "${e}" is dropped because the ${e==="width"?"x":"y"} is continuous.`}function yr(e,n,t,i){return`Conflicting ${n.toString()} property "${e.toString()}" (${x(t)} and ${x(i)}). Using ${x(t)}.`}function vr(e,n,t,i){return`Conflicting ${n.toString()} property "${e.toString()}" (${x(t)} and ${x(i)}). Using the union of the two domains.`}function Or(e){return`Setting the scale to be independent for "${e}" means we also have to set the guide (axis or legend) to be independent.`}function xr(e){return`Dropping sort property ${x(e)} as unioned domains only support boolean or op "count", "min", and "max".`}const wr="Domains that should be unioned has conflicting sort properties. Sort will be set to true.";const jr="Detected faceted independent scales that union domain of multiple fields from different data sources. We will use the first field. The result view size may be incorrect.";const Fr="Detected faceted independent scales that union domain of the same fields from different source. We will assume that this is the same field from a different fork of the same data source. However, if this is not the case, the result view size may be incorrect.";const $r="Detected faceted independent scales that union domain of multiple fields from the same data source. We will use the first field. The result view size may be incorrect.";const Dr="Invalid channel for axis.";function Ar(e){return`Cannot stack "${e}" if there is already "${e}2".`}function kr(e){return`Cannot stack non-linear scale (${e}).`}function Cr(e){return`Stacking is applied even though the aggregate function is non-summative ("${e}").`}function Sr(e,n){return`Invalid ${e}: ${x(n)}.`}function Er(e){return`Dropping day from datetime ${x(e)} as day cannot be combined with other units.`}function Br(e,n){return`${n?"extent ":""}${n&&e?"and ":""}${e?"center ":""}${n&&e?"are ":"is "}not needed when data are aggregated.`}function Pr(e,n,t){return`${e} is not usually used with ${n} for ${t}.`}function _r(e,n){return`Continuous axis should not have customized aggregation function ${e}; ${n} already agregates the axis.`}function zr(e){return`1D error band does not support ${e}.`}function Nr(e){return`Channel ${e} is required for "binned" bin.`}function Tr(e){return`Channel ${e} should not be used with "binned" bin.`}function Mr(e){return`Domain for ${e} is required for threshold scale.`}var Lr=undefined&&undefined.__classPrivateFieldSet||function(e,n,t,i,r){if(i==="m")throw new TypeError("Private method is not writable");if(i==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof n==="function"?e!==n||!r:!n.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i==="a"?r.call(e,t):r?r.value=t:n.set(e,t),t};var qr=undefined&&undefined.__classPrivateFieldGet||function(e,n,t,i){if(t==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof n==="function"?e!==n||!i:!n.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?i:t==="a"?i.call(e):i?i.value:n.get(e)};var Ur;const Rr=(0,r.vF)(r.P$);let Ir=Rr;class Wr{constructor(){this.warns=[];this.infos=[];this.debugs=[];Ur.set(this,Warn)}level(e){if(e){Lr(this,Ur,e,"f");return this}return qr(this,Ur,"f")}warn(...e){if(qr(this,Ur,"f")>=Warn)this.warns.push(...e);return this}info(...e){if(qr(this,Ur,"f")>=Info)this.infos.push(...e);return this}debug(...e){if(qr(this,Ur,"f")>=Debug)this.debugs.push(...e);return this}error(...e){if(qr(this,Ur,"f")>=ErrorLevel)throw Error(...e);return this}}Ur=new WeakMap;function Hr(e){return()=>{Ir=new Wr;e(Ir);Yr()}}function Gr(e){Ir=e;return Ir}function Yr(){Ir=Rr;return Ir}function Kr(...e){Ir.error(...e)}function Vr(...e){Ir.warn(...e)}function Qr(...e){Ir.info(...e)}function Xr(...e){Ir.debug(...e)}function Jr(e){if(e&&(0,r.Gv)(e)){for(const n of ds){if(n in e){return true}}}return false}const Zr=["january","february","march","april","may","june","july","august","september","october","november","december"];const es=Zr.map((e=>e.substr(0,3)));const ns=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];const ts=ns.map((e=>e.substr(0,3)));function is(e){if(re(e)){e=+e}if((0,r.Et)(e)){if(e>4){Vr(Sr("quarter",e))}return e-1}else{throw new Error(Sr("quarter",e))}}function rs(e){if(re(e)){e=+e}if((0,r.Et)(e)){return e-1}else{const n=e.toLowerCase();const t=Zr.indexOf(n);if(t!==-1){return t}const i=n.substr(0,3);const r=es.indexOf(i);if(r!==-1){return r}throw new Error(Sr("month",e))}}function ss(e){if(re(e)){e=+e}if((0,r.Et)(e)){return e%7}else{const n=e.toLowerCase();const t=ns.indexOf(n);if(t!==-1){return t}const i=n.substr(0,3);const r=ts.indexOf(i);if(r!==-1){return r}throw new Error(Sr("day",e))}}function os(e,n){const t=[];if(n&&e.day!==undefined){if(N(e).length>1){Vr(Er(e));e=b(e);delete e.day}}if(e.year!==undefined){t.push(e.year)}else{t.push(2012)}if(e.month!==undefined){const i=n?rs(e.month):e.month;t.push(i)}else if(e.quarter!==undefined){const i=n?is(e.quarter):e.quarter;t.push((0,r.Et)(i)?i*3:`${i}*3`)}else{t.push(0)}if(e.date!==undefined){t.push(e.date)}else if(e.day!==undefined){const i=n?ss(e.day):e.day;t.push((0,r.Et)(i)?i+1:`${i}+1`)}else{t.push(1)}for(const i of["hours","minutes","seconds","milliseconds"]){const n=e[i];t.push(typeof n==="undefined"?0:n)}return t}function as(e){const n=os(e,true);const t=n.join(", ");if(e.utc){return`utc(${t})`}else{return`datetime(${t})`}}function us(e){const n=os(e,false);const t=n.join(", ");if(e.utc){return`utc(${t})`}else{return`datetime(${t})`}}function cs(e){const n=os(e,true);if(e.utc){return+new Date(Date.UTC(...n))}else{return+new Date(...n)}}var ls=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};const fs={year:1,quarter:1,month:1,week:1,day:1,dayofyear:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1};const ds=N(fs);function ps(e){return!!fs[e]}const gs={utcyear:1,utcquarter:1,utcmonth:1,utcweek:1,utcday:1,utcdayofyear:1,utcdate:1,utchours:1,utcminutes:1,utcseconds:1,utcmilliseconds:1};const ms={yearquarter:1,yearquartermonth:1,yearmonth:1,yearmonthdate:1,yearmonthdatehours:1,yearmonthdatehoursminutes:1,yearmonthdatehoursminutesseconds:1,yearweek:1,yearweekday:1,yearweekdayhours:1,yearweekdayhoursminutes:1,yearweekdayhoursminutesseconds:1,yeardayofyear:1,quartermonth:1,monthdate:1,monthdatehours:1,monthdatehoursminutes:1,monthdatehoursminutesseconds:1,weekday:1,weeksdayhours:1,weekdayhoursminutes:1,weekdayhoursminutesseconds:1,dayhours:1,dayhoursminutes:1,dayhoursminutesseconds:1,hoursminutes:1,hoursminutesseconds:1,minutesseconds:1,secondsmilliseconds:1};const hs={utcyearquarter:1,utcyearquartermonth:1,utcyearmonth:1,utcyearmonthdate:1,utcyearmonthdatehours:1,utcyearmonthdatehoursminutes:1,utcyearmonthdatehoursminutesseconds:1,utcyearweek:1,utcyearweekday:1,utcyearweekdayhours:1,utcyearweekdayhoursminutes:1,utcyearweekdayhoursminutesseconds:1,utcyeardayofyear:1,utcquartermonth:1,utcmonthdate:1,utcmonthdatehours:1,utcmonthdatehoursminutes:1,utcmonthdatehoursminutesseconds:1,utcweekday:1,utcweeksdayhours:1,utcweekdayhoursminutes:1,utcweekdayhoursminutesseconds:1,utcdayhours:1,utcdayhoursminutes:1,utcdayhoursminutesseconds:1,utchoursminutes:1,utchoursminutesseconds:1,utcminutesseconds:1,utcsecondsmilliseconds:1};function bs(e){return e.startsWith("utc")}function ys(e){return e.substr(3)}const vs={"year-month":"%b %Y ","year-month-date":"%b %d, %Y "};function Os(e){return ds.filter((n=>xs(e,n)))}function xs(e,n){const t=e.indexOf(n);if(t<0){return false}if(t>0&&n==="seconds"&&e.charAt(t-1)==="i"){return false}if(e.length>t+3&&n==="day"&&e.charAt(t+3)==="o"){return false}if(t>0&&n==="year"&&e.charAt(t-1)==="f"){return false}return true}function ws(e,n,{end:t}={end:false}){const i=W(n);const r=bs(e)?"utc":"";function s(e){if(e==="quarter"){return`(${r}quarter(${i})-1)`}else{return`${r}${e}(${i})`}}let o;const a={};for(const u of ds){if(xs(e,u)){a[u]=s(u);o=u}}if(t){a[o]+="+1"}return us(a)}function js(e){if(!e){return undefined}const n=Os(e);return`timeUnitSpecifier(${x(n)}, ${x(vs)})`}function Fs(e,n,t){if(!e){return undefined}const i=js(e);const r=t||bs(e);return`${r?"utc":"time"}Format(${n}, ${i})`}function $s(e){if(!e){return undefined}let n;if((0,r.Kg)(e)){n={unit:e}}else if((0,r.Gv)(e)){n=Object.assign(Object.assign({},e),e.unit?{unit:e.unit}:{})}if(bs(n.unit)){n.utc=true;n.unit=ys(n.unit)}return n}function Ds(e){const n=$s(e),{utc:t}=n,i=ls(n,["utc"]);if(i.unit){return(t?"utc":"")+N(i).map((e=>q(`${e==="unit"?"":`_${e}_`}${i[e]}`))).join("")}else{return(t?"utc":"")+"timeunit"+N(i).map((e=>q(`_${e}_${i[e]}`))).join("")}}function As(e){return e===null||e===void 0?void 0:e["param"]}function ks(e){return!!(e===null||e===void 0?void 0:e.field)&&e.equal!==undefined}function Cs(e){return!!(e===null||e===void 0?void 0:e.field)&&e.lt!==undefined}function Ss(e){return!!(e===null||e===void 0?void 0:e.field)&&e.lte!==undefined}function Es(e){return!!(e===null||e===void 0?void 0:e.field)&&e.gt!==undefined}function Bs(e){return!!(e===null||e===void 0?void 0:e.field)&&e.gte!==undefined}function Ps(e){if(e===null||e===void 0?void 0:e.field){if((0,r.cy)(e.range)&&e.range.length===2){return true}else if(Tt(e.range)){return true}}return false}function _s(e){return!!(e===null||e===void 0?void 0:e.field)&&((0,r.cy)(e.oneOf)||(0,r.cy)(e.in))}function zs(e){return!!(e===null||e===void 0?void 0:e.field)&&e.valid!==undefined}function Ns(e){return _s(e)||ks(e)||Ps(e)||Cs(e)||Es(e)||Ss(e)||Bs(e)}function Ts(e,n){return Ju(e,{timeUnit:n,wrapTime:true})}function Ms(e,n){return e.map((e=>Ts(e,n)))}function Ls(e,n=true){var t;const{field:i}=e;const r=(t=$s(e.timeUnit))===null||t===void 0?void 0:t.unit;const s=r?`time(${ws(r,i)})`:Du(e,{expr:"datum"});if(ks(e)){return`${s}===${Ts(e.equal,r)}`}else if(Cs(e)){const n=e.lt;return`${s}<${Ts(n,r)}`}else if(Es(e)){const n=e.gt;return`${s}>${Ts(n,r)}`}else if(Ss(e)){const n=e.lte;return`${s}<=${Ts(n,r)}`}else if(Bs(e)){const n=e.gte;return`${s}>=${Ts(n,r)}`}else if(_s(e)){return`indexof([${Ms(e.oneOf,r).join(",")}], ${s}) !== -1`}else if(zs(e)){return qs(s,e.valid)}else if(Ps(e)){const{range:t}=e;const i=Tt(t)?{signal:`${t.signal}[0]`}:t[0];const o=Tt(t)?{signal:`${t.signal}[1]`}:t[1];if(i!==null&&o!==null&&n){return"inrange("+s+", ["+Ts(i,r)+", "+Ts(o,r)+"])"}const a=[];if(i!==null){a.push(`${s} >= ${Ts(i,r)}`)}if(o!==null){a.push(`${s} <= ${Ts(o,r)}`)}return a.length>0?a.join(" && "):"true"}throw new Error(`Invalid field predicate: ${x(e)}`)}function qs(e,n=true){if(n){return`isValid(${e}) && isFinite(+${e})`}else{return`!isValid(${e}) || !isFinite(+${e})`}}function Us(e){var n;if(Ns(e)&&e.timeUnit){return Object.assign(Object.assign({},e),{timeUnit:(n=$s(e.timeUnit))===null||n===void 0?void 0:n.unit})}return e}var Rs=t(78352);const Is={quantitative:"quantitative",ordinal:"ordinal",temporal:"temporal",nominal:"nominal",geojson:"geojson"};function Ws(e){return e in Is}function Hs(e){return e==="quantitative"||e==="temporal"}function Gs(e){return e==="ordinal"||e==="nominal"}const Ys=Is.quantitative;const Ks=Is.ordinal;const Vs=Is.temporal;const Qs=Is.nominal;const Xs=Is.geojson;const Js=N(Is);function Zs(e){if(e){e=e.toLowerCase();switch(e){case"q":case Ys:return"quantitative";case"t":case Vs:return"temporal";case"o":case Ks:return"ordinal";case"n":case Qs:return"nominal";case Xs:return"geojson"}}return undefined}var eo=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};const no={LINEAR:"linear",LOG:"log",POW:"pow",SQRT:"sqrt",SYMLOG:"symlog",IDENTITY:"identity",SEQUENTIAL:"sequential",TIME:"time",UTC:"utc",QUANTILE:"quantile",QUANTIZE:"quantize",THRESHOLD:"threshold",BIN_ORDINAL:"bin-ordinal",ORDINAL:"ordinal",POINT:"point",BAND:"band"};const to={linear:"numeric",log:"numeric",pow:"numeric",sqrt:"numeric",symlog:"numeric",identity:"numeric",sequential:"numeric",time:"time",utc:"time",ordinal:"ordinal","bin-ordinal":"bin-ordinal",point:"ordinal-position",band:"ordinal-position",quantile:"discretizing",quantize:"discretizing",threshold:"discretizing"};const io=N(to);function ro(e,n){const t=to[e];const i=to[n];return t===i||t==="ordinal-position"&&i==="time"||i==="ordinal-position"&&t==="time"}const so={linear:0,log:1,pow:1,sqrt:1,symlog:1,identity:1,sequential:1,time:0,utc:0,point:10,band:11,ordinal:0,"bin-ordinal":0,quantile:0,quantize:0,threshold:0};function oo(e){return so[e]}const ao=new Set(["linear","log","pow","sqrt","symlog"]);const uo=new Set([...ao,"time","utc"]);function co(e){return ao.has(e)}const lo=new Set(["quantile","quantize","threshold"]);const fo=new Set([...uo,...lo,"sequential","identity"]);const po=new Set(["ordinal","bin-ordinal","point","band"]);const go=new Set(["time","utc"]);function mo(e){return po.has(e)}function ho(e){return fo.has(e)}function bo(e){return uo.has(e)}function yo(e){return lo.has(e)}const vo={pointPadding:.5,barBandPaddingInner:.1,rectBandPaddingInner:0,bandWithNestedOffsetPaddingInner:.2,bandWithNestedOffsetPaddingOuter:.2,minBandSize:2,minFontSize:8,maxFontSize:40,minOpacity:.3,maxOpacity:.8,minSize:9,minStrokeWidth:1,maxStrokeWidth:4,quantileCount:4,quantizeCount:4,zero:true};function Oo(e){return!(0,r.Kg)(e)&&!!e["name"]}function xo(e){return e===null||e===void 0?void 0:e["param"]}function wo(e){return e===null||e===void 0?void 0:e["unionWith"]}function jo(e){return(0,Rs.isObject)(e)&&"field"in e}const Fo={type:1,domain:1,domainMax:1,domainMin:1,domainMid:1,align:1,range:1,rangeMax:1,rangeMin:1,scheme:1,bins:1,reverse:1,round:1,clamp:1,nice:1,base:1,exponent:1,constant:1,interpolate:1,zero:1,padding:1,paddingInner:1,paddingOuter:1};const $o=N(Fo);const{type:Do,domain:Ao,range:ko,rangeMax:Co,rangeMin:So,scheme:Eo}=Fo,Bo=eo(Fo,["type","domain","range","rangeMax","rangeMin","scheme"]);const Po=N(Bo);function _o(e,n){switch(n){case"type":case"domain":case"reverse":case"range":return true;case"scheme":case"interpolate":return!["point","band","identity"].includes(e);case"bins":return!["point","band","identity","ordinal"].includes(e);case"round":return bo(e)||e==="band"||e==="point";case"padding":case"rangeMin":case"rangeMax":return bo(e)||["point","band"].includes(e);case"paddingOuter":case"align":return["point","band"].includes(e);case"paddingInner":return e==="band";case"domainMax":case"domainMid":case"domainMin":case"clamp":return bo(e);case"nice":return bo(e)||e==="quantize"||e==="threshold";case"exponent":return e==="pow";case"base":return e==="log";case"constant":return e==="symlog";case"zero":return ho(e)&&!F(["log","time","utc","threshold","quantile"],e)}}function zo(e,n){switch(n){case"interpolate":case"scheme":case"domainMid":if(!Qe(e)){return ar(n)}return undefined;case"align":case"type":case"bins":case"domain":case"domainMax":case"domainMin":case"range":case"base":case"exponent":case"constant":case"nice":case"padding":case"paddingInner":case"paddingOuter":case"rangeMax":case"rangeMin":case"reverse":case"round":case"clamp":case"zero":return undefined}}function No(e,n){if(F([Ks,Qs],n)){return e===undefined||mo(e)}else if(n===Vs){return F([no.TIME,no.UTC,undefined],e)}else if(n===Ys){return co(e)||yo(e)||e===undefined}return true}function To(e,n,t=false){if(!ct(e)){return false}switch(e){case ce:case le:case pe:case ge:case be:case me:if(bo(n)){return true}else if(n==="band"){return true}else if(n==="point"){return!t}return false;case Ae:case Be:case Ce:case Se:case Ee:case ke:return bo(n)||yo(n)||F(["band","point","ordinal"],n);case je:case Fe:case $e:return n!=="band";case Pe:case De:return n==="ordinal"||yo(n)}}const Mo={arc:"arc",area:"area",bar:"bar",image:"image",line:"line",point:"point",rect:"rect",rule:"rule",text:"text",tick:"tick",trail:"trail",circle:"circle",square:"square",geoshape:"geoshape"};const Lo=Mo.arc;const qo=Mo.area;const Uo=Mo.bar;const Ro=Mo.image;const Io=Mo.line;const Wo=Mo.point;const Ho=Mo.rect;const Go=Mo.rule;const Yo=Mo.text;const Ko=Mo.tick;const Vo=Mo.trail;const Qo=Mo.circle;const Xo=Mo.square;const Jo=Mo.geoshape;function Zo(e){return e in Mo}function ea(e){return["line","area","trail"].includes(e)}function na(e){return["rect","bar","image","arc"].includes(e)}const ta=new Set(N(Mo));function ia(e){return e["type"]}function ra(e){const n=ia(e)?e.type:e;return ta.has(n)}const sa=["stroke","strokeWidth","strokeDash","strokeDashOffset","strokeOpacity","strokeJoin","strokeMiterLimit"];const oa=["fill","fillOpacity"];const aa=[...sa,...oa];const ua={color:1,filled:1,invalid:1,order:1,radius2:1,theta2:1,timeUnitBandSize:1,timeUnitBandPosition:1};const ca=N(ua);const la={area:["line","point"],bar:["binSpacing","continuousBandSize","discreteBandSize"],rect:["binSpacing","continuousBandSize","discreteBandSize"],line:["point"],tick:["bandSize","thickness"]};const fa={color:"#4c78a8",invalid:"filter",timeUnitBandSize:1};const da={mark:1,arc:1,area:1,bar:1,circle:1,image:1,line:1,point:1,rect:1,rule:1,square:1,text:1,tick:1,trail:1,geoshape:1};const pa=N(da);function ga(e){return e&&e["band"]!=undefined}const ma={horizontal:["cornerRadiusTopRight","cornerRadiusBottomRight"],vertical:["cornerRadiusTopLeft","cornerRadiusTopRight"]};const ha=5;const ba={binSpacing:1,continuousBandSize:ha,timeUnitBandPosition:.5};const ya={binSpacing:0,continuousBandSize:ha,timeUnitBandPosition:.5};const va={thickness:1};function Oa(e){return ia(e)?e.type:e}function xa(e){const{channel:n,channelDef:t,markDef:i,scale:r,config:s}=e;const o=ka(e);if(fu(t)&&!wt(t.aggregate)&&r&&bo(r.get("type"))){return wa({fieldDef:t,channel:n,markDef:i,ref:o,config:s})}return o}function wa({fieldDef:e,channel:n,markDef:t,ref:i,config:r}){if(ea(t.type)){return i}const s=ii("invalid",t,r);if(s===null){return[ja(e,n),i]}return i}function ja(e,n){const t=Fa(e,true);const i=hn(n);const r=i==="y"?{field:{group:"height"}}:{value:0};return Object.assign({test:t},r)}function Fa(e,n=true){return qs((0,r.Kg)(e)?e:Du(e,{expr:"datum"}),!n)}function $a(e){const{datum:n}=e;if(Jr(n)){return as(n)}return`${x(n)}`}function Da(e,n,t,i){const r={};if(n){r.scale=n}if(pu(e)){const{datum:n}=e;if(Jr(n)){r.signal=as(n)}else if(Tt(n)){r.signal=n.signal}else if(Bt(n)){r.signal=n.expr}else{r.value=n}}else{r.field=Du(e,t)}if(i){const{offset:e,band:n}=i;if(e){r.offset=e}if(n){r.band=n}}return r}function Aa({scaleName:e,fieldOrDatumDef:n,fieldOrDatumDef2:t,offset:i,startSuffix:r,bandPosition:s=.5}){const o=0<s&&s<1?"datum":undefined;const a=Du(n,{expr:o,suffix:r});const u=t!==undefined?Du(t,{expr:o}):Du(n,{suffix:"end",expr:o});const c={};if(s===0||s===1){c.scale=e;const n=s===0?a:u;c.field=n}else{const n=Tt(s)?`${s.signal} * ${a} + (1-${s.signal}) * ${u}`:`${s} * ${a} + ${1-s} * ${u}`;c.signal=`scale("${e}", ${n})`}if(i){c.offset=i}return c}function ka({channel:e,channelDef:n,channel2Def:t,markDef:i,config:s,scaleName:o,scale:a,stack:u,offset:c,defaultRef:l,bandPosition:f}){var d;if(n){if(bu(n)){const r=a===null||a===void 0?void 0:a.get("type");if(yu(n)){f!==null&&f!==void 0?f:f=ru({fieldDef:n,fieldDef2:t,markDef:i,config:s});const{bin:a,timeUnit:l,type:d}=n;if(At(a)||f&&l&&d===Vs){if(u===null||u===void 0?void 0:u.impute){return Da(n,o,{binSuffix:"mid"},{offset:c})}if(f&&!mo(r)){return Aa({scaleName:o,fieldOrDatumDef:n,bandPosition:f,offset:c})}return Da(n,o,ec(n,e)?{binSuffix:"range"}:{},{offset:c})}else if(kt(a)){if(fu(t)){return Aa({scaleName:o,fieldOrDatumDef:n,fieldOrDatumDef2:t,bandPosition:f,offset:c})}else{const n=e===ce?fe:de;Vr(Nr(n))}}}return Da(n,o,mo(r)?{binSuffix:"range"}:{},{offset:c,band:r==="band"?(d=f!==null&&f!==void 0?f:n.bandPosition)!==null&&d!==void 0?d:.5:undefined})}else if(vu(n)){const t=n.value;const i=c?{offset:c}:{};return Object.assign(Object.assign({},Ca(e,t)),i)}}if((0,r.Tn)(l)){l=l()}if(l){return Object.assign(Object.assign({},l),c?{offset:c}:{})}return l}function Ca(e,n){if(F(["x","x2"],e)&&n==="width"){return{field:{group:"width"}}}else if(F(["y","y2"],e)&&n==="height"){return{field:{group:"height"}}}return Xt(n)}function Sa(e){return e&&e!=="number"&&e!=="time"}function Ea(e,n,t){return`${e}(${n}${t?`, ${x(t)}`:""})`}const Ba=" – ";function Pa({fieldOrDatumDef:e,format:n,formatType:t,expr:i,normalizeStack:r,config:s}){var o,a;if(Sa(t)){return za({fieldOrDatumDef:e,format:n,formatType:t,expr:i,config:s})}const u=_a(e,i,r);const c=du(e);if(n===undefined&&t===undefined&&s.customFormatTypes){if(c==="quantitative"){if(r&&s.normalizedNumberFormatType)return za({fieldOrDatumDef:e,format:s.normalizedNumberFormat,formatType:s.normalizedNumberFormatType,expr:i,config:s});if(s.numberFormatType){return za({fieldOrDatumDef:e,format:s.numberFormat,formatType:s.numberFormatType,expr:i,config:s})}}if(c==="temporal"&&s.timeFormatType&&fu(e)&&e.timeUnit===undefined){return za({fieldOrDatumDef:e,format:s.timeFormat,formatType:s.timeFormatType,expr:i,config:s})}}if(Qu(e)){const t=Ia({field:u,timeUnit:fu(e)?(o=$s(e.timeUnit))===null||o===void 0?void 0:o.unit:undefined,format:n,formatType:s.timeFormatType,rawTimeFormat:s.timeFormat,isUTCScale:Ou(e)&&((a=e.scale)===null||a===void 0?void 0:a.type)===no.UTC});return t?{signal:t}:undefined}n=Ma({type:c,specifiedFormat:n,config:s,normalizeStack:r});if(fu(e)&&At(e.bin)){const r=Du(e,{expr:i,binSuffix:"end"});return{signal:Ra(u,r,n,t,s)}}else if(n||du(e)==="quantitative"){return{signal:`${qa(u,n)}`}}else{return{signal:`isValid(${u}) ? ${u} : ""+${u}`}}}function _a(e,n,t){if(fu(e)){if(t){return`${Du(e,{expr:n,suffix:"end"})}-${Du(e,{expr:n,suffix:"start"})}`}else{return Du(e,{expr:n})}}else{return $a(e)}}function za({fieldOrDatumDef:e,format:n,formatType:t,expr:i,normalizeStack:r,config:s,field:o}){o!==null&&o!==void 0?o:o=_a(e,i,r);if(o!=="datum.value"&&fu(e)&&At(e.bin)){const r=Du(e,{expr:i,binSuffix:"end"});return{signal:Ra(o,r,n,t,s)}}return{signal:Ea(t,o,n)}}function Na(e,n,t,i,r,s){var o;if(Sa(i)){return undefined}else if(t===undefined&&i===undefined&&r.customFormatTypes){if(du(e)==="quantitative"){if(r.normalizedNumberFormatType&&xu(e)&&e.stack==="normalize"){return undefined}if(r.numberFormatType){return undefined}}}if(xu(e)&&e.stack==="normalize"&&r.normalizedNumberFormat){return Ma({type:"quantitative",config:r,normalizeStack:true})}if(Qu(e)){const n=fu(e)?(o=$s(e.timeUnit))===null||o===void 0?void 0:o.unit:undefined;if(n===undefined&&r.customFormatTypes&&r.timeFormatType){return undefined}return La({specifiedFormat:t,timeUnit:n,config:r,omitTimeFormatConfig:s})}return Ma({type:n,specifiedFormat:t,config:r})}function Ta(e,n,t){var i;if(e&&(Tt(e)||e==="number"||e==="time")){return e}if(Qu(n)&&t!=="time"&&t!=="utc"){return fu(n)&&((i=$s(n===null||n===void 0?void 0:n.timeUnit))===null||i===void 0?void 0:i.utc)?"utc":"time"}return undefined}function Ma({type:e,specifiedFormat:n,config:t,normalizeStack:i}){if((0,r.Kg)(n)){return n}if(e===Ys){return i?t.normalizedNumberFormat:t.numberFormat}return undefined}function La({specifiedFormat:e,timeUnit:n,config:t,omitTimeFormatConfig:i}){if(e){return e}if(n){return{signal:js(n)}}return i?undefined:t.timeFormat}function qa(e,n){return`format(${e}, "${n||""}")`}function Ua(e,n,t,i){var s;if(Sa(t)){return Ea(t,e,n)}return qa(e,(s=(0,r.Kg)(n)?n:undefined)!==null&&s!==void 0?s:i.numberFormat)}function Ra(e,n,t,i,r){if(t===undefined&&i===undefined&&r.customFormatTypes&&r.numberFormatType){return Ra(e,n,r.numberFormat,r.numberFormatType,r)}const s=Ua(e,t,i,r);const o=Ua(n,t,i,r);return`${qs(e,false)} ? "null" : ${s} + "${Ba}" + ${o}`}function Ia({field:e,timeUnit:n,format:t,formatType:i,rawTimeFormat:s,isUTCScale:o}){if(!n||t){if(!n&&i){return`${i}(${e}, '${t}')`}t=(0,r.Kg)(t)?t:s;return`${o?"utc":"time"}Format(${e}, '${t}')`}else{return Fs(n,e,o)}}const Wa="min";const Ha={x:1,y:1,color:1,fill:1,stroke:1,strokeWidth:1,size:1,shape:1,fillOpacity:1,strokeOpacity:1,opacity:1,text:1};function Ga(e){return e in Ha}function Ya(e){return!!(e===null||e===void 0?void 0:e["encoding"])}function Ka(e){return e&&(e["op"]==="count"||!!e["field"])}function Va(e){return e&&(0,r.cy)(e)}function Qa(e){return"row"in e||"column"in e}function Xa(e){return!!e&&"header"in e}function Ja(e){return"facet"in e}var Za=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function eu(e){return e["param"]}function nu(e){return e&&!(0,r.Kg)(e)&&"repeat"in e}function tu(e){const{field:n,timeUnit:t,bin:i,aggregate:r}=e;return Object.assign(Object.assign(Object.assign(Object.assign({},t?{timeUnit:t}:{}),i?{bin:i}:{}),r?{aggregate:r}:{}),{field:n})}function iu(e){return"sort"in e}function ru({fieldDef:e,fieldDef2:n,markDef:t,config:i}){if(bu(e)&&e.bandPosition!==undefined){return e.bandPosition}if(fu(e)){const{timeUnit:r,bin:s}=e;if(r&&!n){return na(t.type)?0:ri("timeUnitBandPosition",t,i)}else if(At(s)){return.5}}return undefined}function su({channel:e,fieldDef:n,fieldDef2:t,markDef:i,config:r,scaleType:s,useVlSizeChannel:o}){var a,u,c;const l=vn(e);const f=ii(o?"size":l,i,r,{vgChannel:l});if(f!==undefined){return f}if(fu(n)){const{timeUnit:e,bin:o}=n;if(e&&!t){return{band:ri("timeUnitBandSize",i,r)}}else if(At(o)&&!mo(s)){return{band:1}}}if(na(i.type)){if(s){if(mo(s)){return((a=r[i.type])===null||a===void 0?void 0:a.discreteBandSize)||{band:1}}else{return(u=r[i.type])===null||u===void 0?void 0:u.continuousBandSize}}return(c=r[i.type])===null||c===void 0?void 0:c.discreteBandSize}return undefined}function ou(e,n,t,i){if(At(e.bin)||e.timeUnit&&yu(e)&&e.type==="temporal"){return ru({fieldDef:e,fieldDef2:n,markDef:t,config:i})!==undefined}return false}function au(e){return e&&"condition"in e}function uu(e){const n=e===null||e===void 0?void 0:e["condition"];return!!n&&!(0,r.cy)(n)&&fu(n)}function cu(e){const n=e===null||e===void 0?void 0:e["condition"];return!!n&&!(0,r.cy)(n)&&bu(n)}function lu(e){const n=e===null||e===void 0?void 0:e["condition"];return!!n&&((0,r.cy)(n)||vu(n))}function fu(e){return e&&(!!e["field"]||e["aggregate"]==="count")}function du(e){return e===null||e===void 0?void 0:e["type"]}function pu(e){return e&&"datum"in e}function gu(e){return yu(e)&&!Au(e)||hu(e)}function mu(e){return du(e)==="quantitative"||hu(e)}function hu(e){return pu(e)&&(0,r.Et)(e.datum)}function bu(e){return fu(e)||pu(e)}function yu(e){return e&&("field"in e||e["aggregate"]==="count")&&"type"in e}function vu(e){return e&&"value"in e&&"value"in e}function Ou(e){return e&&("scale"in e||"sort"in e)}function xu(e){return e&&("axis"in e||"stack"in e||"impute"in e)}function wu(e){return e&&"legend"in e}function ju(e){return e&&("format"in e||"formatType"in e)}function Fu(e){return O(e,["legend","axis","header","scale"])}function $u(e){return"op"in e}function Du(e,n={}){var t,i,r;let s=e.field;const o=n.prefix;let a=n.suffix;let u="";if(Cu(e)){s=ne("count")}else{let o;if(!n.nofn){if($u(e)){o=e.op}else{const{bin:c,aggregate:l,timeUnit:f}=e;if(At(c)){o=Dt(c);a=((t=n.binSuffix)!==null&&t!==void 0?t:"")+((i=n.suffix)!==null&&i!==void 0?i:"")}else if(l){if(vt(l)){u=`["${s}"]`;s=`argmax_${l.argmax}`}else if(yt(l)){u=`["${s}"]`;s=`argmin_${l.argmin}`}else{o=String(l)}}else if(f){o=Ds(f);a=(!["range","mid"].includes(n.binSuffix)&&n.binSuffix||"")+((r=n.suffix)!==null&&r!==void 0?r:"")}}}if(o){s=s?`${o}_${s}`:o}}if(a){s=`${s}_${a}`}if(o){s=`${o}_${s}`}if(n.forAs){return V(s)}else if(n.expr){return H(s,n.expr)+u}else{return Y(s)+u}}function Au(e){switch(e.type){case"nominal":case"ordinal":case"geojson":return true;case"quantitative":return fu(e)&&!!e.bin;case"temporal":return false}throw new Error(Ri(e.type))}function ku(e){var n;return Ou(e)&&yo((n=e.scale)===null||n===void 0?void 0:n.type)}function Cu(e){return e.aggregate==="count"}function Su(e,n){var t;const{field:i,bin:r,timeUnit:s,aggregate:o}=e;if(o==="count"){return n.countTitle}else if(At(r)){return`${i} (binned)`}else if(s){const e=(t=$s(s))===null||t===void 0?void 0:t.unit;if(e){return`${i} (${Os(e).join("-")})`}}else if(o){if(vt(o)){return`${i} for max ${o.argmax}`}else if(yt(o)){return`${i} for min ${o.argmin}`}else{return`${I(o)} of ${i}`}}return i}function Eu(e){const{aggregate:n,bin:t,timeUnit:i,field:r}=e;if(vt(n)){return`${r} for argmax(${n.argmax})`}else if(yt(n)){return`${r} for argmin(${n.argmin})`}const s=$s(i);const o=n||(s===null||s===void 0?void 0:s.unit)||(s===null||s===void 0?void 0:s.maxbins)&&"timeunit"||At(t)&&"bin";if(o){return`${o.toUpperCase()}(${r})`}else{return r}}const Bu=(e,n)=>{switch(n.fieldTitle){case"plain":return e.field;case"functional":return Eu(e);default:return Su(e,n)}};let Pu=Bu;function _u(e){Pu=e}function zu(){_u(Bu)}function Nu(e,n,{allowDisabling:t,includeDefault:i=true}){var r,s;const o=(r=Tu(e))===null||r===void 0?void 0:r.title;if(!fu(e)){return o!==null&&o!==void 0?o:e.title}const a=e;const u=i?Mu(a,n):undefined;if(t){return X(o,a.title,u)}else{return(s=o!==null&&o!==void 0?o:a.title)!==null&&s!==void 0?s:u}}function Tu(e){if(xu(e)&&e.axis){return e.axis}else if(wu(e)&&e.legend){return e.legend}else if(Xa(e)&&e.header){return e.header}return undefined}function Mu(e,n){return Pu(e,n)}function Lu(e){var n;if(ju(e)){const{format:n,formatType:t}=e;return{format:n,formatType:t}}else{const t=(n=Tu(e))!==null&&n!==void 0?n:{};const{format:i,formatType:r}=t;return{format:i,formatType:r}}}function qu(e,n){var t;switch(n){case"latitude":case"longitude":return"quantitative";case"row":case"column":case"facet":case"shape":case"strokeDash":return"nominal";case"order":return"ordinal"}if(iu(e)&&(0,r.cy)(e.sort)){return"ordinal"}const{aggregate:i,bin:s,timeUnit:o}=e;if(o){return"temporal"}if(s||i&&!vt(i)&&!yt(i)){return"quantitative"}if(Ou(e)&&((t=e.scale)===null||t===void 0?void 0:t.type)){switch(to[e.scale.type]){case"numeric":case"discretizing":return"quantitative";case"time":return"temporal"}}return"nominal"}function Uu(e){if(fu(e)){return e}else if(uu(e)){return e.condition}return undefined}function Ru(e){if(bu(e)){return e}else if(cu(e)){return e.condition}return undefined}function Iu(e,n,t,i={}){if((0,r.Kg)(e)||(0,r.Et)(e)||(0,r.Lm)(e)){const t=(0,r.Kg)(e)?"string":(0,r.Et)(e)?"number":"boolean";Vr(Ui(n,t,e));return{value:e}}if(bu(e)){return Wu(e,n,t,i)}else if(cu(e)){return Object.assign(Object.assign({},e),{condition:Wu(e.condition,n,t,i)})}return e}function Wu(e,n,t,i){if(ju(e)){const{format:r,formatType:s}=e,o=Za(e,["format","formatType"]);if(Sa(s)&&!t.customFormatTypes){Vr(Ni(n));return Wu(o,n,t,i)}}else{const r=xu(e)?"axis":wu(e)?"legend":Xa(e)?"header":null;if(r&&e[r]){const s=e[r],{format:o,formatType:a}=s,u=Za(s,["format","formatType"]);if(Sa(a)&&!t.customFormatTypes){Vr(Ni(n));return Wu(Object.assign(Object.assign({},e),{[r]:u}),n,t,i)}}}if(fu(e)){return Gu(e,n,i)}return Hu(e)}function Hu(e){let n=e["type"];if(n){return e}const{datum:t}=e;n=(0,r.Et)(t)?"quantitative":(0,r.Kg)(t)?"nominal":Jr(t)?"temporal":undefined;return Object.assign(Object.assign({},e),{type:n})}function Gu(e,n,{compositeMark:t=false}={}){const{aggregate:i,timeUnit:s,bin:o,field:a}=e;const u=Object.assign({},e);if(!t&&i&&!Ot(i)&&!vt(i)&&!yt(i)){Vr(Wi(i));delete u.aggregate}if(s){u.timeUnit=$s(s)}if(a){u.field=`${a}`}if(At(o)){u.bin=Yu(o,n)}if(kt(o)&&!Rn(n)){Vr(Tr(n))}if(yu(u)){const{type:e}=u;const n=Zs(e);if(e!==n){u.type=n}if(e!=="quantitative"){if(wt(i)){Vr(Ii(e,i));u.type="quantitative"}}}else if(!mn(n)){const e=qu(u,n);u["type"]=e}if(yu(u)){const{compatible:e,warning:t}=Vu(u,n)||{};if(e===false){Vr(t)}}if(iu(u)&&(0,r.Kg)(u.sort)){const{sort:e}=u;if(Ga(e)){return Object.assign(Object.assign({},u),{sort:{encoding:e}})}const n=e.substr(1);if(e.charAt(0)==="-"&&Ga(n)){return Object.assign(Object.assign({},u),{sort:{encoding:n,order:"descending"}})}}if(Xa(u)){const{header:e}=u;if(e){const{orient:n}=e,t=Za(e,["orient"]);if(n){return Object.assign(Object.assign({},u),{header:Object.assign(Object.assign({},t),{labelOrient:e.labelOrient||n,titleOrient:e.titleOrient||n})})}}}return u}function Yu(e,n){if((0,r.Lm)(e)){return{maxbins:Et(n)}}else if(e==="binned"){return{binned:true}}else if(!e.maxbins&&!e.step){return Object.assign(Object.assign({},e),{maxbins:Et(n)})}else{return e}}const Ku={compatible:true};function Vu(e,n){const t=e.type;if(t==="geojson"&&n!=="shape"){return{compatible:false,warning:`Channel ${n} should not be used with a geojson data.`}}switch(n){case oe:case ae:case ue:if(!Au(e)){return{compatible:false,warning:Zi(n)}}return Ku;case ce:case le:case pe:case ge:case je:case Fe:case $e:case _e:case Ne:case Te:case Me:case Le:case qe:case ke:case be:case me:case Ue:return Ku;case Oe:case we:case ve:case xe:if(t!==Ys){return{compatible:false,warning:`Channel ${n} should be used with a quantitative field only, not ${e.type} field.`}}return Ku;case Ce:case Se:case Ee:case Be:case Ae:case ye:case he:case fe:case de:if(t==="nominal"&&!e["sort"]){return{compatible:false,warning:`Channel ${n} should not be used with an unsorted discrete field.`}}return Ku;case De:case Pe:if(!Au(e)&&!ku(e)){return{compatible:false,warning:er(n)}}return Ku;case ze:if(e.type==="nominal"&&!("sort"in e)){return{compatible:false,warning:`Channel order is inappropriate for nominal field, which has no inherent order.`}}return Ku}}function Qu(e){const{formatType:n}=Lu(e);return n==="time"||!n&&Xu(e)}function Xu(e){return e&&(e["type"]==="temporal"||fu(e)&&!!e.timeUnit)}function Ju(e,{timeUnit:n,type:t,wrapTime:i,undefinedIfExprNotRequired:s}){var o;const a=n&&((o=$s(n))===null||o===void 0?void 0:o.unit);let u=a||t==="temporal";let c;if(Bt(e)){c=e.expr}else if(Tt(e)){c=e.signal}else if(Jr(e)){u=true;c=as(e)}else if((0,r.Kg)(e)||(0,r.Et)(e)){if(u){c=`datetime(${x(e)})`;if(ps(a)){if((0,r.Et)(e)&&e<1e4||(0,r.Kg)(e)&&isNaN(Date.parse(e))){c=as({[a]:e})}}}}if(c){return i&&u?`time(${c})`:c}return s?undefined:x(e)}function Zu(e,n){const{type:t}=e;return n.map((n=>{const i=Ju(n,{timeUnit:fu(e)?e.timeUnit:undefined,type:t,undefinedIfExprNotRequired:true});if(i!==undefined){return{signal:i}}return n}))}function ec(e,n){if(!At(e.bin)){console.warn("Only call this method for binned field defs.");return false}return ct(n)&&["ordinal","nominal"].includes(e.type)}const nc={labelAlign:{part:"labels",vgProp:"align"},labelBaseline:{part:"labels",vgProp:"baseline"},labelColor:{part:"labels",vgProp:"fill"},labelFont:{part:"labels",vgProp:"font"},labelFontSize:{part:"labels",vgProp:"fontSize"},labelFontStyle:{part:"labels",vgProp:"fontStyle"},labelFontWeight:{part:"labels",vgProp:"fontWeight"},labelOpacity:{part:"labels",vgProp:"opacity"},labelOffset:null,labelPadding:null,gridColor:{part:"grid",vgProp:"stroke"},gridDash:{part:"grid",vgProp:"strokeDash"},gridDashOffset:{part:"grid",vgProp:"strokeDashOffset"},gridOpacity:{part:"grid",vgProp:"opacity"},gridWidth:{part:"grid",vgProp:"strokeWidth"},tickColor:{part:"ticks",vgProp:"stroke"},tickDash:{part:"ticks",vgProp:"strokeDash"},tickDashOffset:{part:"ticks",vgProp:"strokeDashOffset"},tickOpacity:{part:"ticks",vgProp:"opacity"},tickSize:null,tickWidth:{part:"ticks",vgProp:"strokeWidth"}};function tc(e){return e===null||e===void 0?void 0:e.condition}const ic=["domain","grid","labels","ticks","title"];const rc={grid:"grid",gridCap:"grid",gridColor:"grid",gridDash:"grid",gridDashOffset:"grid",gridOpacity:"grid",gridScale:"grid",gridWidth:"grid",orient:"main",bandPosition:"both",aria:"main",description:"main",domain:"main",domainCap:"main",domainColor:"main",domainDash:"main",domainDashOffset:"main",domainOpacity:"main",domainWidth:"main",format:"main",formatType:"main",labelAlign:"main",labelAngle:"main",labelBaseline:"main",labelBound:"main",labelColor:"main",labelFlush:"main",labelFlushOffset:"main",labelFont:"main",labelFontSize:"main",labelFontStyle:"main",labelFontWeight:"main",labelLimit:"main",labelLineHeight:"main",labelOffset:"main",labelOpacity:"main",labelOverlap:"main",labelPadding:"main",labels:"main",labelSeparation:"main",maxExtent:"main",minExtent:"main",offset:"both",position:"main",tickCap:"main",tickColor:"main",tickDash:"main",tickDashOffset:"main",tickMinStep:"both",tickOffset:"both",tickOpacity:"main",tickRound:"both",ticks:"main",tickSize:"main",tickWidth:"both",title:"main",titleAlign:"main",titleAnchor:"main",titleAngle:"main",titleBaseline:"main",titleColor:"main",titleFont:"main",titleFontSize:"main",titleFontStyle:"main",titleFontWeight:"main",titleLimit:"main",titleLineHeight:"main",titleOpacity:"main",titlePadding:"main",titleX:"main",titleY:"main",encode:"both",scale:"both",tickBand:"both",tickCount:"both",tickExtra:"both",translate:"both",values:"both",zindex:"both"};const sc={orient:1,aria:1,bandPosition:1,description:1,domain:1,domainCap:1,domainColor:1,domainDash:1,domainDashOffset:1,domainOpacity:1,domainWidth:1,format:1,formatType:1,grid:1,gridCap:1,gridColor:1,gridDash:1,gridDashOffset:1,gridOpacity:1,gridWidth:1,labelAlign:1,labelAngle:1,labelBaseline:1,labelBound:1,labelColor:1,labelFlush:1,labelFlushOffset:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelLineHeight:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labels:1,labelSeparation:1,maxExtent:1,minExtent:1,offset:1,position:1,tickBand:1,tickCap:1,tickColor:1,tickCount:1,tickDash:1,tickDashOffset:1,tickExtra:1,tickMinStep:1,tickOffset:1,tickOpacity:1,tickRound:1,ticks:1,tickSize:1,tickWidth:1,title:1,titleAlign:1,titleAnchor:1,titleAngle:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titlePadding:1,titleX:1,titleY:1,translate:1,values:1,zindex:1};const oc=Object.assign(Object.assign({},sc),{style:1,labelExpr:1,encoding:1});function ac(e){return!!oc[e]}const uc=N(oc);const cc={axis:1,axisBand:1,axisBottom:1,axisDiscrete:1,axisLeft:1,axisPoint:1,axisQuantitative:1,axisRight:1,axisTemporal:1,axisTop:1,axisX:1,axisXBand:1,axisXDiscrete:1,axisXPoint:1,axisXQuantitative:1,axisXTemporal:1,axisY:1,axisYBand:1,axisYDiscrete:1,axisYPoint:1,axisYQuantitative:1,axisYTemporal:1};const lc=N(cc);function fc(e){return"mark"in e}class dc{constructor(e,n){this.name=e;this.run=n}hasMatchingType(e){if(fc(e)){return Oa(e.mark)===this.name}return false}}var pc=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function gc(e,n){const t=e&&e[n];if(t){if((0,r.cy)(t)){return $(t,(e=>!!e.field))}else{return fu(t)||uu(t)}}return false}function mc(e,n){const t=e&&e[n];if(t){if((0,r.cy)(t)){return $(t,(e=>!!e.field))}else{return fu(t)||pu(t)||cu(t)}}return false}function hc(e,n){if(Rn(n)){const t=e[n];if((fu(t)||pu(t))&&Gs(t.type)){const t=xn(n);return mc(e,t)}}return false}function bc(e){return $(en,(n=>{if(gc(e,n)){const t=e[n];if((0,r.cy)(t)){return $(t,(e=>!!e.aggregate))}else{const e=Uu(t);return e&&!!e.aggregate}}return false}))}function yc(e,n){const t=[];const i=[];const r=[];const s=[];const o={};jc(e,((a,u)=>{if(fu(a)){const{field:c,aggregate:l,bin:f,timeUnit:d}=a,p=pc(a,["field","aggregate","bin","timeUnit"]);if(l||d||f){const e=Tu(a);const g=e===null||e===void 0?void 0:e.title;let m=Du(a,{forAs:true});const h=Object.assign(Object.assign(Object.assign({},g?[]:{title:Nu(a,n,{allowDisabling:true})}),p),{field:m});if(l){let e;if(vt(l)){e="argmax";m=Du({op:"argmax",field:l.argmax},{forAs:true});h.field=`${m}.${c}`}else if(yt(l)){e="argmin";m=Du({op:"argmin",field:l.argmin},{forAs:true});h.field=`${m}.${c}`}else if(l!=="boxplot"&&l!=="errorbar"&&l!=="errorband"){e=l}if(e){const n={op:e,as:m};if(c){n.field=c}s.push(n)}}else{t.push(m);if(yu(a)&&At(f)){i.push({bin:f,field:c,as:m});t.push(Du(a,{binSuffix:"end"}));if(ec(a,u)){t.push(Du(a,{binSuffix:"range"}))}if(Rn(u)){const e={field:`${m}_end`};o[`${u}2`]=e}h.bin="binned";if(!mn(u)){h["type"]=Ys}}else if(d){r.push({timeUnit:d,field:c,as:m});const e=yu(a)&&a.type!==Vs&&"time";if(e){if(u===_e||u===Me){h["formatType"]=e}else if(st(u)){h["legend"]=Object.assign({formatType:e},h["legend"])}else if(Rn(u)){h["axis"]=Object.assign({formatType:e},h["axis"])}}}}o[u]=h}else{t.push(c);o[u]=e[u]}}else{o[u]=e[u]}}));return{bins:i,timeUnits:r,aggregate:s,groupby:t,encoding:o}}function vc(e,n,t){const i=lt(n,t);if(!i){return false}else if(i==="binned"){const t=e[n===fe?ce:le];if(fu(t)&&fu(e[n])&&kt(t.bin)){return true}else{return false}}return true}function Oc(e,n,t,i){const s={};for(const r of N(e)){if(!pn(r)){Vr(Ji(r))}}for(let o of jn){if(!e[o]){continue}const a=e[o];if(Kn(o)){const e=wn(o);const n=s[e];if(fu(n)){if(Hs(n.type)){if(fu(a)){Vr(Li(e));continue}}}else{o=e;Vr(qi(e))}}if(o==="angle"&&n==="arc"&&!e.theta){Vr(Mi);o=be}if(!vc(e,o,n)){Vr(Qi(o,n));continue}if(o===Ae&&n==="line"){const n=Uu(e[o]);if(n===null||n===void 0?void 0:n.aggregate){Vr(Vi);continue}}if(o===je&&(t?"fill"in e:"stroke"in e)){Vr(Gi("encoding",{fill:"fill"in e,stroke:"stroke"in e}));continue}if(o===Ne||o===ze&&!(0,r.cy)(a)&&!vu(a)||o===Me&&(0,r.cy)(a)){if(a){s[o]=(0,r.YO)(a).reduce(((e,n)=>{if(!fu(n)){Vr(Ki(n,o))}else{e.push(Gu(n,o))}return e}),[])}}else{if(o===Me&&a===null){s[o]=null}else if(!fu(a)&&!pu(a)&&!vu(a)&&!au(a)&&!Tt(a)){Vr(Ki(a,o));continue}s[o]=Iu(a,o,i)}}return s}function xc(e,n){const t={};for(const i of N(e)){const r=Iu(e[i],i,n,{compositeMark:true});t[i]=r}return t}function wc(e){const n=[];for(const t of N(e)){if(gc(e,t)){const i=e[t];const s=(0,r.YO)(i);for(const e of s){if(fu(e)){n.push(e)}else if(uu(e)){n.push(e.condition)}}}}return n}function jc(e,n,t){if(!e){return}for(const i of N(e)){const s=e[i];if((0,r.cy)(s)){for(const e of s){n.call(t,e,i)}}else{n.call(t,s,i)}}}function Fc(e,n,t,i){if(!e){return t}return N(e).reduce(((t,s)=>{const o=e[s];if((0,r.cy)(o)){return o.reduce(((e,t)=>n.call(i,e,t,s)),t)}else{return n.call(i,t,o,s)}}),t)}function $c(e,n){return N(n).reduce(((t,i)=>{switch(i){case ce:case le:case Le:case Ue:case qe:case fe:case de:case pe:case ge:case be:case ye:case me:case he:case ve:case Oe:case xe:case we:case _e:case De:case ke:case Me:return t;case ze:if(e==="line"||e==="trail"){return t}case Ne:case Te:{const e=n[i];if((0,r.cy)(e)||fu(e)){for(const n of(0,r.YO)(e)){if(!n.aggregate){t.push(Du(n,{}))}}}return t}case Ae:if(e==="trail"){return t}case je:case Fe:case $e:case Ce:case Se:case Ee:case Pe:case Be:{const e=Uu(n[i]);if(e&&!e.aggregate){t.push(Du(e,{}))}return t}}}),[])}var Dc=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function Ac(e){const{tooltip:n}=e,t=Dc(e,["tooltip"]);if(!n){return{filteredEncoding:t}}let i;let s;if((0,r.cy)(n)){for(const e of n){if(e.aggregate){if(!i){i=[]}i.push(e)}else{if(!s){s=[]}s.push(e)}}if(i){t.tooltip=i}}else{if(n["aggregate"]){t.tooltip=n}else{s=n}}if((0,r.cy)(s)&&s.length===1){s=s[0]}return{customTooltipWithoutAggregatedField:s,filteredEncoding:t}}function kc(e,n,t,i=true){if("tooltip"in t){return{tooltip:t.tooltip}}const r=e.map((({fieldPrefix:e,titlePrefix:t})=>{const r=i?` of ${Cc(n)}`:"";return{field:e+n.field,type:n.type,title:Tt(t)?{signal:`${t}"${escape(r)}"`}:t+r}}));const s=wc(t).map(Fu);return{tooltip:[...r,...C(s,w)]}}function Cc(e){const{title:n,field:t}=e;return X(n,t)}function Sc(e,n,t,i,s){const{scale:o,axis:a}=t;return({partName:u,mark:c,positionPrefix:l,endPositionPrefix:f=undefined,extraEncoding:d={}})=>{const p=Cc(t);return Ec(e,u,s,{mark:c,encoding:Object.assign(Object.assign(Object.assign({[n]:Object.assign(Object.assign(Object.assign({field:`${l}_${t.field}`,type:t.type},p!==undefined?{title:p}:{}),o!==undefined?{scale:o}:{}),a!==undefined?{axis:a}:{})},(0,r.Kg)(f)?{[`${n}2`]:{field:`${f}_${t.field}`}}:{}),i),d)})}}function Ec(e,n,t,i){const{clip:s,color:o,opacity:a}=e;const u=e.type;if(e[n]||e[n]===undefined&&t[n]){return[Object.assign(Object.assign({},i),{mark:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},t[n]),s?{clip:s}:{}),o?{color:o}:{}),a?{opacity:a}:{}),ia(i.mark)?i.mark:{type:i.mark}),{style:`${u}-${String(n)}`}),(0,r.Lm)(e[n])?{}:e[n])})]}return[]}function Bc(e,n,t){const{encoding:i}=e;const r=n==="vertical"?"y":"x";const s=i[r];const o=i[`${r}2`];const a=i[`${r}Error`];const u=i[`${r}Error2`];return{continuousAxisChannelDef:Pc(s,t),continuousAxisChannelDef2:Pc(o,t),continuousAxisChannelDefError:Pc(a,t),continuousAxisChannelDefError2:Pc(u,t),continuousAxis:r}}function Pc(e,n){if(e===null||e===void 0?void 0:e.aggregate){const{aggregate:t}=e,i=Dc(e,["aggregate"]);if(t!==n){Vr(_r(t,n))}return i}else{return e}}function _c(e,n){const{mark:t,encoding:i}=e;const{x:r,y:s}=i;if(ia(t)&&t.orient){return t.orient}if(gu(r)){if(gu(s)){const e=fu(r)&&r.aggregate;const t=fu(s)&&s.aggregate;if(!e&&t===n){return"vertical"}else if(!t&&e===n){return"horizontal"}else if(e===n&&t===n){throw new Error("Both x and y cannot have aggregate")}else{if(Qu(s)&&!Qu(r)){return"horizontal"}return"vertical"}}return"horizontal"}else if(gu(s)){return"vertical"}else{throw new Error(`Need a valid continuous axis for ${n}s`)}}var zc=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};const Nc="boxplot";const Tc=["box","median","outliers","rule","ticks"];const Mc=new dc(Nc,qc);function Lc(e){if((0,r.Et)(e)){return"tukey"}return e}function qc(e,{config:n}){var t,i;e=Object.assign(Object.assign({},e),{encoding:xc(e.encoding,n)});const{mark:s,encoding:o,params:a,projection:u}=e,c=zc(e,["mark","encoding","params","projection"]);const l=ia(s)?s:{type:s};if(a){Vr(Oi("boxplot"))}const f=(t=l.extent)!==null&&t!==void 0?t:n.boxplot.extent;const d=ii("size",l,n);const p=l.invalid;const g=Lc(f);const{bins:m,timeUnits:h,transform:b,continuousAxisChannelDef:y,continuousAxis:v,groupby:x,aggregate:w,encodingWithoutContinuousAxis:j,ticksOrient:F,boxOrient:$,customTooltipWithoutAggregatedField:D}=Rc(e,f,n);const{color:A,size:k}=j,C=zc(j,["color","size"]);const S=e=>Sc(l,v,y,e,n.boxplot);const E=S(C);const B=S(j);const P=S(Object.assign(Object.assign({},C),k?{size:k}:{}));const _=kc([{fieldPrefix:g==="min-max"?"upper_whisker_":"max_",titlePrefix:"Max"},{fieldPrefix:"upper_box_",titlePrefix:"Q3"},{fieldPrefix:"mid_box_",titlePrefix:"Median"},{fieldPrefix:"lower_box_",titlePrefix:"Q1"},{fieldPrefix:g==="min-max"?"lower_whisker_":"min_",titlePrefix:"Min"}],y,j);const N={type:"tick",color:"black",opacity:1,orient:F,invalid:p,aria:false};const T=g==="min-max"?_:kc([{fieldPrefix:"upper_whisker_",titlePrefix:"Upper Whisker"},{fieldPrefix:"lower_whisker_",titlePrefix:"Lower Whisker"}],y,j);const M=[...E({partName:"rule",mark:{type:"rule",invalid:p,aria:false},positionPrefix:"lower_whisker",endPositionPrefix:"lower_box",extraEncoding:T}),...E({partName:"rule",mark:{type:"rule",invalid:p,aria:false},positionPrefix:"upper_box",endPositionPrefix:"upper_whisker",extraEncoding:T}),...E({partName:"ticks",mark:N,positionPrefix:"lower_whisker",extraEncoding:T}),...E({partName:"ticks",mark:N,positionPrefix:"upper_whisker",extraEncoding:T})];const L=[...g!=="tukey"?M:[],...B({partName:"box",mark:Object.assign(Object.assign({type:"bar"},d?{size:d}:{}),{orient:$,invalid:p,ariaRoleDescription:"box"}),positionPrefix:"lower_box",endPositionPrefix:"upper_box",extraEncoding:_}),...P({partName:"median",mark:Object.assign(Object.assign(Object.assign({type:"tick",invalid:p},(0,r.Gv)(n.boxplot.median)&&n.boxplot.median.color?{color:n.boxplot.median.color}:{}),d?{size:d}:{}),{orient:F,aria:false}),positionPrefix:"mid_box",extraEncoding:_})];if(g==="min-max"){return Object.assign(Object.assign({},c),{transform:((i=c.transform)!==null&&i!==void 0?i:[]).concat(b),layer:L})}const q=`datum["lower_box_${y.field}"]`;const U=`datum["upper_box_${y.field}"]`;const R=`(${U} - ${q})`;const I=`${q} - ${f} * ${R}`;const W=`${U} + ${f} * ${R}`;const H=`datum["${y.field}"]`;const G={joinaggregate:Uc(y.field),groupby:x};const Y={transform:[{filter:`(${I} <= ${H}) && (${H} <= ${W})`},{aggregate:[{op:"min",field:y.field,as:`lower_whisker_${y.field}`},{op:"max",field:y.field,as:`upper_whisker_${y.field}`},{op:"min",field:`lower_box_${y.field}`,as:`lower_box_${y.field}`},{op:"max",field:`upper_box_${y.field}`,as:`upper_box_${y.field}`},...w],groupby:x}],layer:M};const{tooltip:K}=C,V=zc(C,["tooltip"]);const{scale:Q,axis:X}=y;const J=Cc(y);const Z=O(X,["title"]);const ee=Ec(l,"outliers",n.boxplot,{transform:[{filter:`(${H} < ${I}) || (${H} > ${W})`}],mark:"point",encoding:Object.assign(Object.assign(Object.assign({[v]:Object.assign(Object.assign(Object.assign({field:y.field,type:y.type},J!==undefined?{title:J}:{}),Q!==undefined?{scale:Q}:{}),z(Z)?{}:{axis:Z})},V),A?{color:A}:{}),D?{tooltip:D}:{})})[0];let ne;const te=[...m,...h,G];if(ee){ne={transform:te,layer:[ee,Y]}}else{ne=Y;ne.transform.unshift(...te)}return Object.assign(Object.assign({},c),{layer:[ne,{transform:b,layer:L}]})}function Uc(e){return[{op:"q1",field:e,as:`lower_box_${e}`},{op:"q3",field:e,as:`upper_box_${e}`}]}function Rc(e,n,t){const i=_c(e,Nc);const{continuousAxisChannelDef:r,continuousAxis:s}=Bc(e,i,Nc);const o=r.field;const a=Lc(n);const u=[...Uc(o),{op:"median",field:o,as:`mid_box_${o}`},{op:"min",field:o,as:(a==="min-max"?"lower_whisker_":"min_")+o},{op:"max",field:o,as:(a==="min-max"?"upper_whisker_":"max_")+o}];const c=a==="min-max"||a==="tukey"?[]:[{calculate:`datum["upper_box_${o}"] - datum["lower_box_${o}"]`,as:`iqr_${o}`},{calculate:`min(datum["upper_box_${o}"] + datum["iqr_${o}"] * ${n}, datum["max_${o}"])`,as:`upper_whisker_${o}`},{calculate:`max(datum["lower_box_${o}"] - datum["iqr_${o}"] * ${n}, datum["min_${o}"])`,as:`lower_whisker_${o}`}];const l=e.encoding,f=s,d=l[f],p=zc(l,[typeof f==="symbol"?f:f+""]);const{customTooltipWithoutAggregatedField:g,filteredEncoding:m}=Ac(p);const{bins:h,timeUnits:b,aggregate:y,groupby:v,encoding:O}=yc(m,t);const x=i==="vertical"?"horizontal":"vertical";const w=i;const j=[...h,...b,{aggregate:[...y,...u],groupby:v},...c];return{bins:h,timeUnits:b,transform:j,groupby:v,aggregate:y,continuousAxisChannelDef:r,continuousAxis:s,encodingWithoutContinuousAxis:O,ticksOrient:x,boxOrient:w,customTooltipWithoutAggregatedField:g}}var Ic=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};const Wc="errorbar";const Hc=["ticks","rule"];const Gc=new dc(Wc,Yc);function Yc(e,{config:n}){e=Object.assign(Object.assign({},e),{encoding:xc(e.encoding,n)});const{transform:t,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,ticksOrient:o,markDef:a,outerSpec:u,tooltipEncoding:c}=Jc(e,Wc,n);delete s["size"];const l=Sc(a,r,i,s,n.errorbar);const f=a.thickness;const d=a.size;const p=Object.assign(Object.assign({type:"tick",orient:o,aria:false},f!==undefined?{thickness:f}:{}),d!==undefined?{size:d}:{});const g=[...l({partName:"ticks",mark:p,positionPrefix:"lower",extraEncoding:c}),...l({partName:"ticks",mark:p,positionPrefix:"upper",extraEncoding:c}),...l({partName:"rule",mark:Object.assign({type:"rule",ariaRoleDescription:"errorbar"},f!==undefined?{size:f}:{}),positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:c})];return Object.assign(Object.assign(Object.assign({},u),{transform:t}),g.length>1?{layer:g}:Object.assign({},g[0]))}function Kc(e,n){const{encoding:t}=e;if(Vc(t)){return{orient:_c(e,n),inputType:"raw"}}const i=Qc(t);const r=Xc(t);const s=t.x;const o=t.y;if(i){if(r){throw new Error(`${n} cannot be both type aggregated-upper-lower and aggregated-error`)}const e=t.x2;const i=t.y2;if(bu(e)&&bu(i)){throw new Error(`${n} cannot have both x2 and y2`)}else if(bu(e)){if(gu(s)){return{orient:"horizontal",inputType:"aggregated-upper-lower"}}else{throw new Error(`Both x and x2 have to be quantitative in ${n}`)}}else if(bu(i)){if(gu(o)){return{orient:"vertical",inputType:"aggregated-upper-lower"}}else{throw new Error(`Both y and y2 have to be quantitative in ${n}`)}}throw new Error("No ranged axis")}else{const e=t.xError;const i=t.xError2;const r=t.yError;const a=t.yError2;if(bu(i)&&!bu(e)){throw new Error(`${n} cannot have xError2 without xError`)}if(bu(a)&&!bu(r)){throw new Error(`${n} cannot have yError2 without yError`)}if(bu(e)&&bu(r)){throw new Error(`${n} cannot have both xError and yError with both are quantiative`)}else if(bu(e)){if(gu(s)){return{orient:"horizontal",inputType:"aggregated-error"}}else{throw new Error("All x, xError, and xError2 (if exist) have to be quantitative")}}else if(bu(r)){if(gu(o)){return{orient:"vertical",inputType:"aggregated-error"}}else{throw new Error("All y, yError, and yError2 (if exist) have to be quantitative")}}throw new Error("No ranged axis")}}function Vc(e){return(bu(e.x)||bu(e.y))&&!bu(e.x2)&&!bu(e.y2)&&!bu(e.xError)&&!bu(e.xError2)&&!bu(e.yError)&&!bu(e.yError2)}function Qc(e){return bu(e.x2)||bu(e.y2)}function Xc(e){return bu(e.xError)||bu(e.xError2)||bu(e.yError)||bu(e.yError2)}function Jc(e,n,t){var i;const{mark:r,encoding:s,params:o,projection:a}=e,u=Ic(e,["mark","encoding","params","projection"]);const c=ia(r)?r:{type:r};if(o){Vr(Oi(n))}const{orient:l,inputType:f}=Kc(e,n);const{continuousAxisChannelDef:d,continuousAxisChannelDef2:p,continuousAxisChannelDefError:g,continuousAxisChannelDefError2:m,continuousAxis:h}=Bc(e,l,n);const{errorBarSpecificAggregate:b,postAggregateCalculates:y,tooltipSummary:v,tooltipTitleWithFieldName:O}=Zc(c,d,p,g,m,f,n,t);const x=s,w=h,j=x[w],F=h==="x"?"x2":"y2",$=x[F],D=h==="x"?"xError":"yError",A=x[D],k=h==="x"?"xError2":"yError2",C=x[k],S=Ic(x,[typeof w==="symbol"?w:w+"",typeof F==="symbol"?F:F+"",typeof D==="symbol"?D:D+"",typeof k==="symbol"?k:k+""]);const{bins:E,timeUnits:B,aggregate:P,groupby:_,encoding:z}=yc(S,t);const N=[...P,...b];const T=f!=="raw"?[]:_;const M=kc(v,d,z,O);return{transform:[...(i=u.transform)!==null&&i!==void 0?i:[],...E,...B,...N.length===0?[]:[{aggregate:N,groupby:T}],...y],groupby:T,continuousAxisChannelDef:d,continuousAxis:h,encodingWithoutContinuousAxis:z,ticksOrient:l==="vertical"?"horizontal":"vertical",markDef:c,outerSpec:u,tooltipEncoding:M}}function Zc(e,n,t,i,r,s,o,a){let u=[];let c=[];const l=n.field;let f;let d=false;if(s==="raw"){const n=e.center?e.center:e.extent?e.extent==="iqr"?"median":"mean":a.errorbar.center;const t=e.extent?e.extent:n==="mean"?"stderr":"iqr";if(n==="median"!==(t==="iqr")){Vr(Pr(n,t,o))}if(t==="stderr"||t==="stdev"){u=[{op:t,field:l,as:`extent_${l}`},{op:n,field:l,as:`center_${l}`}];c=[{calculate:`datum["center_${l}"] + datum["extent_${l}"]`,as:`upper_${l}`},{calculate:`datum["center_${l}"] - datum["extent_${l}"]`,as:`lower_${l}`}];f=[{fieldPrefix:"center_",titlePrefix:I(n)},{fieldPrefix:"upper_",titlePrefix:el(n,t,"+")},{fieldPrefix:"lower_",titlePrefix:el(n,t,"-")}];d=true}else{let e;let n;let i;if(t==="ci"){e="mean";n="ci0";i="ci1"}else{e="median";n="q1";i="q3"}u=[{op:n,field:l,as:`lower_${l}`},{op:i,field:l,as:`upper_${l}`},{op:e,field:l,as:`center_${l}`}];f=[{fieldPrefix:"upper_",titlePrefix:Nu({field:l,aggregate:i,type:"quantitative"},a,{allowDisabling:false})},{fieldPrefix:"lower_",titlePrefix:Nu({field:l,aggregate:n,type:"quantitative"},a,{allowDisabling:false})},{fieldPrefix:"center_",titlePrefix:Nu({field:l,aggregate:e,type:"quantitative"},a,{allowDisabling:false})}]}}else{if(e.center||e.extent){Vr(Br(e.center,e.extent))}if(s==="aggregated-upper-lower"){f=[];c=[{calculate:`datum["${t.field}"]`,as:`upper_${l}`},{calculate:`datum["${l}"]`,as:`lower_${l}`}]}else if(s==="aggregated-error"){f=[{fieldPrefix:"",titlePrefix:l}];c=[{calculate:`datum["${l}"] + datum["${i.field}"]`,as:`upper_${l}`}];if(r){c.push({calculate:`datum["${l}"] + datum["${r.field}"]`,as:`lower_${l}`})}else{c.push({calculate:`datum["${l}"] - datum["${i.field}"]`,as:`lower_${l}`})}}for(const e of c){f.push({fieldPrefix:e.as.substring(0,6),titlePrefix:K(K(e.calculate,'datum["',""),'"]',"")})}}return{postAggregateCalculates:c,errorBarSpecificAggregate:u,tooltipSummary:f,tooltipTitleWithFieldName:d}}function el(e,n,t){return`${I(e)} ${t} ${n}`}const nl="errorband";const tl=["band","borders"];const il=new dc(nl,rl);function rl(e,{config:n}){e=Object.assign(Object.assign({},e),{encoding:xc(e.encoding,n)});const{transform:t,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,markDef:o,outerSpec:a,tooltipEncoding:u}=Jc(e,nl,n);const c=o;const l=Sc(c,r,i,s,n.errorband);const f=e.encoding.x!==undefined&&e.encoding.y!==undefined;let d={type:f?"area":"rect"};let p={type:f?"line":"rule"};const g=Object.assign(Object.assign({},c.interpolate?{interpolate:c.interpolate}:{}),c.tension&&c.interpolate?{tension:c.tension}:{});if(f){d=Object.assign(Object.assign(Object.assign({},d),g),{ariaRoleDescription:"errorband"});p=Object.assign(Object.assign(Object.assign({},p),g),{aria:false})}else if(c.interpolate){Vr(zr("interpolate"))}else if(c.tension){Vr(zr("tension"))}return Object.assign(Object.assign({},a),{transform:t,layer:[...l({partName:"band",mark:d,positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:u}),...l({partName:"borders",mark:p,positionPrefix:"lower",extraEncoding:u}),...l({partName:"borders",mark:p,positionPrefix:"upper",extraEncoding:u})]})}const sl={};function ol(e,n,t){const i=new dc(e,n);sl[e]={normalizer:i,parts:t}}function al(e){delete sl[e]}function ul(){return N(sl)}ol(Nc,qc,Tc);ol(Wc,Yc,Hc);ol(nl,rl,tl);const cl=["gradientHorizontalMaxLength","gradientHorizontalMinLength","gradientVerticalMaxLength","gradientVerticalMinLength","unselectedOpacity"];const ll={titleAlign:"align",titleAnchor:"anchor",titleAngle:"angle",titleBaseline:"baseline",titleColor:"color",titleFont:"font",titleFontSize:"fontSize",titleFontStyle:"fontStyle",titleFontWeight:"fontWeight",titleLimit:"limit",titleLineHeight:"lineHeight",titleOrient:"orient",titlePadding:"offset"};const fl={labelAlign:"align",labelAnchor:"anchor",labelAngle:"angle",labelBaseline:"baseline",labelColor:"color",labelFont:"font",labelFontSize:"fontSize",labelFontStyle:"fontStyle",labelFontWeight:"fontWeight",labelLimit:"limit",labelLineHeight:"lineHeight",labelOrient:"orient",labelPadding:"offset"};const dl=N(ll);const pl=N(fl);const gl={header:1,headerRow:1,headerColumn:1,headerFacet:1};const ml=N(gl);const hl=["size","shape","fill","stroke","strokeDash","strokeWidth","opacity"];const bl={gradientHorizontalMaxLength:200,gradientHorizontalMinLength:100,gradientVerticalMaxLength:200,gradientVerticalMinLength:64,unselectedOpacity:.35};const yl={aria:1,clipHeight:1,columnPadding:1,columns:1,cornerRadius:1,description:1,direction:1,fillColor:1,format:1,formatType:1,gradientLength:1,gradientOpacity:1,gradientStrokeColor:1,gradientStrokeWidth:1,gradientThickness:1,gridAlign:1,labelAlign:1,labelBaseline:1,labelColor:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labelSeparation:1,legendX:1,legendY:1,offset:1,orient:1,padding:1,rowPadding:1,strokeColor:1,symbolDash:1,symbolDashOffset:1,symbolFillColor:1,symbolLimit:1,symbolOffset:1,symbolOpacity:1,symbolSize:1,symbolStrokeColor:1,symbolStrokeWidth:1,symbolType:1,tickCount:1,tickMinStep:1,title:1,titleAlign:1,titleAnchor:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titleOrient:1,titlePadding:1,type:1,values:1,zindex:1};const vl=N(yl);const Ol="_vgsid_";const xl={point:{on:"click",fields:[Ol],toggle:"event.shiftKey",resolve:"global",clear:"dblclick"},interval:{on:"[mousedown, window:mouseup] > window:mousemove!",encodings:["x","y"],translate:"[mousedown, window:mouseup] > window:mousemove!",zoom:"wheel!",mark:{fill:"#333",fillOpacity:.125,stroke:"white"},resolve:"global",clear:"dblclick"}};function wl(e){return e==="legend"||!!(e===null||e===void 0?void 0:e.legend)}function jl(e){return wl(e)&&(0,r.Gv)(e)}function Fl(e){return!!(e===null||e===void 0?void 0:e["select"])}var $l=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function Dl(e){const n=[];for(const t of e||[]){if(Fl(t))continue;const{expr:e,bind:i}=t,r=$l(t,["expr","bind"]);if(i&&e){const t=Object.assign(Object.assign({},r),{bind:i,init:e});n.push(t)}else{const t=Object.assign(Object.assign(Object.assign({},r),e?{update:e}:{}),i?{bind:i}:{});n.push(t)}}return n}function Al(e){return Cl(e)||Sl(e)||kl(e)}function kl(e){return"concat"in e}function Cl(e){return"vconcat"in e}function Sl(e){return"hconcat"in e}function El({step:e,offsetIsDiscrete:n}){var t;if(n){return(t=e.for)!==null&&t!==void 0?t:"offset"}else{return"position"}}function Bl(e){return(0,r.Gv)(e)&&e["step"]!==undefined}function Pl(e){return e["view"]||e["width"]||e["height"]}const _l=20;const zl={align:1,bounds:1,center:1,columns:1,spacing:1};const Nl=N(zl);function Tl(e,n,t){var i,s;const o=t[n];const a={};const{spacing:u,columns:c}=o;if(u!==undefined){a.spacing=u}if(c!==undefined){if(Ja(e)&&!Qa(e.facet)||kl(e)){a.columns=c}}if(Cl(e)){a.columns=1}for(const l of Nl){if(e[l]!==undefined){if(l==="spacing"){const n=e[l];a[l]=(0,r.Et)(n)?n:{row:(i=n.row)!==null&&i!==void 0?i:u,column:(s=n.column)!==null&&s!==void 0?s:u}}else{a[l]=e[l]}}}return a}var Ml=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function Ll(e,n){var t;return(t=e[n])!==null&&t!==void 0?t:e[n==="width"?"continuousWidth":"continuousHeight"]}function ql(e,n){const t=Ul(e,n);return Bl(t)?t.step:Rl}function Ul(e,n){var t;const i=(t=e[n])!==null&&t!==void 0?t:e[n==="width"?"discreteWidth":"discreteHeight"];return X(i,{step:e.step})}const Rl=20;const Il={continuousWidth:200,continuousHeight:200,step:Rl};function Wl(e){return e&&!!e["scheme"]}const Hl={background:"white",padding:5,timeFormat:"%b %d, %Y",countTitle:"Count of Records",view:Il,mark:fa,arc:{},area:{},bar:ba,circle:{},geoshape:{},image:{},line:{},point:{},rect:ya,rule:{color:"black"},square:{},text:{color:"black"},tick:va,trail:{},boxplot:{size:14,extent:1.5,box:{},median:{color:"white"},outliers:{},rule:{},ticks:null},errorbar:{center:"mean",rule:true,ticks:false},errorband:{band:{opacity:.3},borders:false},scale:vo,projection:{},legend:bl,header:{titlePadding:10,labelPadding:10},headerColumn:{},headerRow:{},headerFacet:{},selection:xl,style:{},title:{},facet:{spacing:_l},concat:{spacing:_l},normalizedNumberFormat:".0%"};const Gl=["#4c78a8","#f58518","#e45756","#72b7b2","#54a24b","#eeca3b","#b279a2","#ff9da6","#9d755d","#bab0ac"];const Yl={text:11,guideLabel:10,guideTitle:11,groupTitle:13,groupSubtitle:12};const Kl={blue:Gl[0],orange:Gl[1],red:Gl[2],teal:Gl[3],green:Gl[4],yellow:Gl[5],purple:Gl[6],pink:Gl[7],brown:Gl[8],gray0:"#000",gray1:"#111",gray2:"#222",gray3:"#333",gray4:"#444",gray5:"#555",gray6:"#666",gray7:"#777",gray8:"#888",gray9:"#999",gray10:"#aaa",gray11:"#bbb",gray12:"#ccc",gray13:"#ddd",gray14:"#eee",gray15:"#fff"};function Vl(e={}){return{signals:[{name:"color",value:(0,r.Gv)(e)?Object.assign(Object.assign({},Kl),e):Kl}],mark:{color:{signal:"color.blue"}},rule:{color:{signal:"color.gray0"}},text:{color:{signal:"color.gray0"}},style:{"guide-label":{fill:{signal:"color.gray0"}},"guide-title":{fill:{signal:"color.gray0"}},"group-title":{fill:{signal:"color.gray0"}},"group-subtitle":{fill:{signal:"color.gray0"}},cell:{stroke:{signal:"color.gray8"}}},axis:{domainColor:{signal:"color.gray13"},gridColor:{signal:"color.gray8"},tickColor:{signal:"color.gray13"}},range:{category:[{signal:"color.blue"},{signal:"color.orange"},{signal:"color.red"},{signal:"color.teal"},{signal:"color.green"},{signal:"color.yellow"},{signal:"color.purple"},{signal:"color.pink"},{signal:"color.brown"},{signal:"color.grey8"}]}}}function Ql(e){return{signals:[{name:"fontSize",value:(0,r.Gv)(e)?Object.assign(Object.assign({},Yl),e):Yl}],text:{fontSize:{signal:"fontSize.text"}},style:{"guide-label":{fontSize:{signal:"fontSize.guideLabel"}},"guide-title":{fontSize:{signal:"fontSize.guideTitle"}},"group-title":{fontSize:{signal:"fontSize.groupTitle"}},"group-subtitle":{fontSize:{signal:"fontSize.groupSubtitle"}}}}}function Xl(e){return{text:{font:e},style:{"guide-label":{font:e},"guide-title":{font:e},"group-title":{font:e},"group-subtitle":{font:e}}}}function Jl(e){const n=N(e||{});const t={};for(const i of n){const n=e[i];t[i]=tc(n)?Kt(n):Vt(n)}return t}function Zl(e){const n=N(e);const t={};for(const i of n){t[i]=Jl(e[i])}return t}const ef=[...pa,...lc,...ml,"background","padding","legend","lineBreak","scale","style","title","view"];function nf(e={}){const{color:n,font:t,fontSize:i,selection:s}=e,o=Ml(e,["color","font","fontSize","selection"]);const a=(0,r.io)({},b(Hl),t?Xl(t):{},n?Vl(n):{},i?Ql(i):{},o||{});if(s){(0,Rs.writeConfig)(a,"selection",s,true)}const u=O(a,ef);for(const r of["background","lineBreak","padding"]){if(a[r]){u[r]=Vt(a[r])}}for(const r of pa){if(a[r]){u[r]=Pt(a[r])}}for(const r of lc){if(a[r]){u[r]=Jl(a[r])}}for(const r of ml){if(a[r]){u[r]=Pt(a[r])}}if(a.legend){u.legend=Pt(a.legend)}if(a.scale){u.scale=Pt(a.scale)}if(a.style){u.style=Zl(a.style)}if(a.title){u.title=Pt(a.title)}if(a.view){u.view=Pt(a.view)}return u}const tf=new Set(["view",...ta]);const rf=["color","fontSize","background","padding","facet","concat","numberFormat","numberFormatType","normalizedNumberFormat","normalizedNumberFormatType","timeFormat","countTitle","header","axisQuantitative","axisTemporal","axisDiscrete","axisPoint","axisXBand","axisXPoint","axisXDiscrete","axisXQuantitative","axisXTemporal","axisYBand","axisYPoint","axisYDiscrete","axisYQuantitative","axisYTemporal","scale","selection","overlay"];const sf=Object.assign({view:["continuousWidth","continuousHeight","discreteWidth","discreteHeight","step"]},la);function of(e){e=b(e);for(const n of rf){delete e[n]}if(e.axis){for(const n in e.axis){if(tc(e.axis[n])){delete e.axis[n]}}}if(e.legend){for(const n of cl){delete e.legend[n]}}if(e.mark){for(const n of ca){delete e.mark[n]}if(e.mark.tooltip&&(0,r.Gv)(e.mark.tooltip)){delete e.mark.tooltip}}if(e.params){e.signals=(e.signals||[]).concat(Dl(e.params));delete e.params}for(const n of tf){for(const i of ca){delete e[n][i]}const t=sf[n];if(t){for(const i of t){delete e[n][i]}}uf(e,n)}for(const n of ul()){delete e[n]}af(e);for(const n in e){if((0,r.Gv)(e[n])&&z(e[n])){delete e[n]}}return z(e)?undefined:e}function af(e){const{titleMarkConfig:n,subtitleMarkConfig:t,subtitle:i}=zt(e.title);if(!z(n)){e.style["group-title"]=Object.assign(Object.assign({},e.style["group-title"]),n)}if(!z(t)){e.style["group-subtitle"]=Object.assign(Object.assign({},e.style["group-subtitle"]),t)}if(!z(i)){e.title=i}else{delete e.title}}function uf(e,n,t,i){const r=i?e[n][i]:e[n];if(n==="view"){t="cell"}const s=Object.assign(Object.assign({},r),e.style[t!==null&&t!==void 0?t:n]);if(!z(s)){e.style[t!==null&&t!==void 0?t:n]=s}if(!i){delete e[n]}}function cf(e){return"layer"in e}function lf(e){return"repeat"in e}function ff(e){return!(0,r.cy)(e.repeat)&&e.repeat["layer"]}var df=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};class pf{map(e,n){if(Ja(e)){return this.mapFacet(e,n)}else if(lf(e)){return this.mapRepeat(e,n)}else if(Sl(e)){return this.mapHConcat(e,n)}else if(Cl(e)){return this.mapVConcat(e,n)}else if(kl(e)){return this.mapConcat(e,n)}else{return this.mapLayerOrUnit(e,n)}}mapLayerOrUnit(e,n){if(cf(e)){return this.mapLayer(e,n)}else if(fc(e)){return this.mapUnit(e,n)}throw new Error(fi(e))}mapLayer(e,n){return Object.assign(Object.assign({},e),{layer:e.layer.map((e=>this.mapLayerOrUnit(e,n)))})}mapHConcat(e,n){return Object.assign(Object.assign({},e),{hconcat:e.hconcat.map((e=>this.map(e,n)))})}mapVConcat(e,n){return Object.assign(Object.assign({},e),{vconcat:e.vconcat.map((e=>this.map(e,n)))})}mapConcat(e,n){const{concat:t}=e,i=df(e,["concat"]);return Object.assign(Object.assign({},i),{concat:t.map((e=>this.map(e,n)))})}mapFacet(e,n){return Object.assign(Object.assign({},e),{spec:this.map(e.spec,n)})}mapRepeat(e,n){return Object.assign(Object.assign({},e),{spec:this.map(e.spec,n)})}}const gf={zero:1,center:1,normalize:1};function mf(e){return e in gf}const hf=new Set([Lo,Uo,qo,Go,Wo,Qo,Xo,Io,Yo,Ko]);const bf=new Set([Uo,qo,Lo]);function yf(e){return fu(e)&&du(e)==="quantitative"&&!e.bin}function vf(e,n){var t,i;const r=n==="x"?"y":"radius";const s=e[n];const o=e[r];if(fu(s)&&fu(o)){if(yf(s)&&yf(o)){if(s.stack){return n}else if(o.stack){return r}const e=fu(s)&&!!s.aggregate;const a=fu(o)&&!!o.aggregate;if(e!==a){return e?n:r}else{const e=(t=s.scale)===null||t===void 0?void 0:t.type;const a=(i=o.scale)===null||i===void 0?void 0:i.type;if(e&&e!=="linear"){return r}else if(a&&a!=="linear"){return n}}}else if(yf(s)){return n}else if(yf(o)){return r}}else if(yf(s)){return n}else if(yf(o)){return r}return undefined}function Of(e){switch(e){case"x":return"y";case"y":return"x";case"theta":return"radius";case"radius":return"theta"}}function xf(e,n){var t,i;const s=ia(e)?e.type:e;if(!hf.has(s)){return null}const o=vf(n,"x")||vf(n,"theta");if(!o){return null}const a=n[o];const u=fu(a)?Du(a,{}):undefined;const c=Of(o);const l=[];const f=new Set;if(n[c]){const e=n[c];const t=fu(e)?Du(e,{}):undefined;if(t&&t!==u){l.push(c);f.add(t)}const i=c==="x"?"xOffset":"yOffset";const r=n[i];const s=fu(r)?Du(r,{}):undefined;if(s&&s!==u){l.push(i);f.add(s)}}const d=Ln.reduce(((e,t)=>{if(t!=="tooltip"&&gc(n,t)){const i=n[t];for(const n of(0,r.YO)(i)){const i=Uu(n);if(i.aggregate){continue}const r=Du(i,{});if(!r||!f.has(r)){e.push({channel:t,fieldDef:i})}}}return e}),[]);let p;if(a.stack!==undefined){if((0,r.Lm)(a.stack)){p=a.stack?"zero":null}else{p=a.stack}}else if(bf.has(s)){p="zero"}if(!p||!mf(p)){return null}if(bc(n)&&d.length===0){return null}if(((t=a===null||a===void 0?void 0:a.scale)===null||t===void 0?void 0:t.type)&&((i=a===null||a===void 0?void 0:a.scale)===null||i===void 0?void 0:i.type)!==no.LINEAR){Vr(kr(a.scale.type));return null}if(bu(n[yn(o)])){if(a.stack!==undefined){Vr(Ar(o))}return null}if(fu(a)&&a.aggregate&&!Ft.has(a.aggregate)){Vr(Cr(a.aggregate))}return{groupbyChannels:l,groupbyFields:f,fieldChannel:o,impute:a.impute===null?false:ea(s),stackBy:d,offset:p}}var wf=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function jf(e){const{point:n,line:t}=e,i=wf(e,["point","line"]);return N(i).length>1?i:i.type}function Ff(e){for(const n of["line","area","rule","trail"]){if(e[n]){e=Object.assign(Object.assign({},e),{[n]:O(e[n],["point","line"])})}}return e}function $f(e,n={},t){if(e.point==="transparent"){return{opacity:0}}else if(e.point){return(0,r.Gv)(e.point)?e.point:{}}else if(e.point!==undefined){return null}else{if(n.point||t.shape){return(0,r.Gv)(n.point)?n.point:{}}return undefined}}function Df(e,n={}){if(e.line){return e.line===true?{}:e.line}else if(e.line!==undefined){return null}else{if(n.line){return n.line===true?{}:n.line}return undefined}}class Af{constructor(){this.name="path-overlay"}hasMatchingType(e,n){if(fc(e)){const{mark:t,encoding:i}=e;const r=ia(t)?t:{type:t};switch(r.type){case"line":case"rule":case"trail":return!!$f(r,n[r.type],i);case"area":return!!$f(r,n[r.type],i)||!!Df(r,n[r.type])}}return false}run(e,n,t){const{config:i}=n;const{params:r,projection:s,mark:o,encoding:a}=e,u=wf(e,["params","projection","mark","encoding"]);const c=xc(a,i);const l=ia(o)?o:{type:o};const f=$f(l,i[l.type],c);const d=l.type==="area"&&Df(l,i[l.type]);const p=[Object.assign(Object.assign({},r?{params:r}:{}),{mark:jf(Object.assign(Object.assign({},l.type==="area"&&l.opacity===undefined&&l.fillOpacity===undefined?{opacity:.7}:{}),l)),encoding:O(c,["shape"])})];const g=xf(l,c);let m=c;if(g){const{fieldChannel:e,offset:n}=g;m=Object.assign(Object.assign({},c),{[e]:Object.assign(Object.assign({},c[e]),n?{stack:n}:{})})}m=O(m,["y2","x2"]);if(d){p.push(Object.assign(Object.assign({},s?{projection:s}:{}),{mark:Object.assign(Object.assign({type:"line"},v(l,["clip","interpolate","tension","tooltip"])),d),encoding:m}))}if(f){p.push(Object.assign(Object.assign({},s?{projection:s}:{}),{mark:Object.assign(Object.assign({type:"point",opacity:1,filled:true},v(l,["clip","tooltip"])),f),encoding:m}))}return t(Object.assign(Object.assign({},u),{layer:p}),Object.assign(Object.assign({},n),{config:Ff(i)}))}}var kf=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function Cf(e,n){if(!n){return e}if(Qa(e)){return zf(e,n)}return Bf(e,n)}function Sf(e,n){if(!n){return e}return zf(e,n)}function Ef(e,n,t){const i=n[e];if(nu(i)){if(i.repeat in t){return Object.assign(Object.assign({},n),{[e]:t[i.repeat]})}else{Vr(ki(i.repeat));return undefined}}return n}function Bf(e,n){e=Ef("field",e,n);if(e===undefined){return undefined}else if(e===null){return null}if(iu(e)&&Ka(e.sort)){const t=Ef("field",e.sort,n);e=Object.assign(Object.assign({},e),t?{sort:t}:{})}return e}function Pf(e,n){if(fu(e)){return Bf(e,n)}else{const t=Ef("datum",e,n);if(t!==e&&!t.type){t.type="nominal"}return t}}function _f(e,n){if(bu(e)){const t=Pf(e,n);if(t){return t}else if(au(e)){return{condition:e.condition}}}else{if(cu(e)){const t=Pf(e.condition,n);if(t){return Object.assign(Object.assign({},e),{condition:t})}else{const{condition:n}=e,t=kf(e,["condition"]);return t}}return e}return undefined}function zf(e,n){const t={};for(const i in e){if((0,r.mQ)(e,i)){const s=e[i];if((0,r.cy)(s)){t[i]=s.map((e=>_f(e,n))).filter((e=>e))}else{const e=_f(s,n);if(e!==undefined){t[i]=e}}}}return t}class Nf{constructor(){this.name="RuleForRangedLine"}hasMatchingType(e){if(fc(e)){const{encoding:n,mark:t}=e;if(t==="line"||ia(t)&&t.type==="line"){for(const e of gn){const t=hn(e);const i=n[t];if(n[e]){if(fu(i)&&!kt(i.bin)||pu(i)){return true}}}}}return false}run(e,n,t){const{encoding:i,mark:s}=e;Vr(rr(!!i.x2,!!i.y2));return t(Object.assign(Object.assign({},e),{mark:(0,r.Gv)(s)?Object.assign(Object.assign({},s),{type:"rule"}):"rule"}),n)}}var Tf=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};class Mf extends pf{constructor(){super(...arguments);this.nonFacetUnitNormalizers=[Mc,Gc,il,new Af,new Nf]}map(e,n){if(fc(e)){const t=gc(e.encoding,oe);const i=gc(e.encoding,ae);const r=gc(e.encoding,ue);if(t||i||r){return this.mapFacetedUnit(e,n)}}return super.map(e,n)}mapUnit(e,n){const{parentEncoding:t,parentProjection:i}=n;const r=Sf(e.encoding,n.repeater);const s=Object.assign(Object.assign({},e),r?{encoding:r}:{});if(t||i){return this.mapUnitWithParentEncodingOrProjection(s,n)}const o=this.mapLayerOrUnit.bind(this);for(const a of this.nonFacetUnitNormalizers){if(a.hasMatchingType(s,n.config)){return a.run(s,n,o)}}return s}mapRepeat(e,n){if(ff(e)){return this.mapLayerRepeat(e,n)}else{return this.mapNonLayerRepeat(e,n)}}mapLayerRepeat(e,n){const{repeat:t,spec:i}=e,r=Tf(e,["repeat","spec"]);const{row:s,column:o,layer:a}=t;const{repeater:u={},repeaterPrefix:c=""}=n;if(s||o){return this.mapRepeat(Object.assign(Object.assign({},e),{repeat:Object.assign(Object.assign({},s?{row:s}:{}),o?{column:o}:{}),spec:{repeat:{layer:a},spec:i}}),n)}else{return Object.assign(Object.assign({},r),{layer:a.map((e=>{const t=Object.assign(Object.assign({},u),{layer:e});const r=`${(i.name||"")+c}child__layer_${q(e)}`;const s=this.mapLayerOrUnit(i,Object.assign(Object.assign({},n),{repeater:t,repeaterPrefix:r}));s.name=r;return s}))})}}mapNonLayerRepeat(e,n){var t;const{repeat:i,spec:s,data:o}=e,a=Tf(e,["repeat","spec","data"]);if(!(0,r.cy)(i)&&e.columns){e=O(e,["columns"]);Vr(Ci("repeat"))}const u=[];const{repeater:c={},repeaterPrefix:l=""}=n;const f=!(0,r.cy)(i)&&i.row||[c?c.row:null];const d=!(0,r.cy)(i)&&i.column||[c?c.column:null];const p=(0,r.cy)(i)&&i||[c?c.repeat:null];for(const m of p){for(const e of f){for(const t of d){const o={repeat:m,row:e,column:t,layer:c.layer};const a=(s.name||"")+l+"child__"+((0,r.cy)(i)?`${q(m)}`:(i.row?`row_${q(e)}`:"")+(i.column?`column_${q(t)}`:""));const f=this.map(s,Object.assign(Object.assign({},n),{repeater:o,repeaterPrefix:a}));f.name=a;u.push(O(f,["data"]))}}}const g=(0,r.cy)(i)?e.columns:i.column?i.column.length:1;return Object.assign(Object.assign({data:(t=s.data)!==null&&t!==void 0?t:o,align:"all"},a),{columns:g,concat:u})}mapFacet(e,n){const{facet:t}=e;if(Qa(t)&&e.columns){e=O(e,["columns"]);Vr(Ci("facet"))}return super.mapFacet(e,n)}mapUnitWithParentEncodingOrProjection(e,n){const{encoding:t,projection:i}=e;const{parentEncoding:r,parentProjection:s,config:o}=n;const a=qf({parentProjection:s,projection:i});const u=Lf({parentEncoding:r,encoding:Sf(t,n.repeater)});return this.mapUnit(Object.assign(Object.assign(Object.assign({},e),a?{projection:a}:{}),u?{encoding:u}:{}),{config:o})}mapFacetedUnit(e,n){const t=e.encoding,{row:i,column:r,facet:s}=t,o=Tf(t,["row","column","facet"]);const{mark:a,width:u,projection:c,height:l,view:f,params:d,encoding:p}=e,g=Tf(e,["mark","width","projection","height","view","params","encoding"]);const{facetMapping:m,layout:h}=this.getFacetMappingAndLayout({row:i,column:r,facet:s},n);const b=Sf(o,n.repeater);return this.mapFacet(Object.assign(Object.assign(Object.assign({},g),h),{facet:m,spec:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},u?{width:u}:{}),l?{height:l}:{}),f?{view:f}:{}),c?{projection:c}:{}),{mark:a,encoding:b}),d?{params:d}:{})}),n)}getFacetMappingAndLayout(e,n){var t;const{row:i,column:r,facet:s}=e;if(i||r){if(s){Vr(nr([...i?[oe]:[],...r?[ae]:[]]))}const n={};const o={};for(const i of[oe,ae]){const r=e[i];if(r){const{align:e,center:s,spacing:a,columns:u}=r,c=Tf(r,["align","center","spacing","columns"]);n[i]=c;for(const n of["align","center","spacing"]){if(r[n]!==undefined){(t=o[n])!==null&&t!==void 0?t:o[n]={};o[n][i]=r[n]}}}}return{facetMapping:n,layout:o}}else{const{align:e,center:t,spacing:i,columns:r}=s,o=Tf(s,["align","center","spacing","columns"]);return{facetMapping:Cf(o,n.repeater),layout:Object.assign(Object.assign(Object.assign(Object.assign({},e?{align:e}:{}),t?{center:t}:{}),i?{spacing:i}:{}),r?{columns:r}:{})}}}mapLayer(e,n){var{parentEncoding:t,parentProjection:i}=n,r=Tf(n,["parentEncoding","parentProjection"]);const{encoding:s,projection:o}=e,a=Tf(e,["encoding","projection"]);const u=Object.assign(Object.assign({},r),{parentEncoding:Lf({parentEncoding:t,encoding:s,layer:true}),parentProjection:qf({parentProjection:i,projection:o})});return super.mapLayer(a,u)}}function Lf({parentEncoding:e,encoding:n={},layer:t}){let i={};if(e){const s=new Set([...N(e),...N(n)]);for(const o of s){const s=n[o];const a=e[o];if(bu(s)){const e=Object.assign(Object.assign({},a),s);i[o]=e}else if(cu(s)){i[o]=Object.assign(Object.assign({},s),{condition:Object.assign(Object.assign({},a),s.condition)})}else if(s||s===null){i[o]=s}else if(t||vu(a)||Tt(a)||bu(a)||(0,r.cy)(a)){i[o]=a}}}else{i=n}return!i||z(i)?undefined:i}function qf(e){const{parentProjection:n,projection:t}=e;if(n&&t){Vr(Ti({parentProjection:n,projection:t}))}return t!==null&&t!==void 0?t:n}function Uf(e){return"filter"in e}function Rf(e){return(e===null||e===void 0?void 0:e["stop"])!==undefined}function If(e){return"lookup"in e}function Wf(e){return"data"in e}function Hf(e){return"param"in e}function Gf(e){return"pivot"in e}function Yf(e){return"density"in e}function Kf(e){return"quantile"in e}function Vf(e){return"regression"in e}function Qf(e){return"loess"in e}function Xf(e){return"sample"in e}function Jf(e){return"window"in e}function Zf(e){return"joinaggregate"in e}function ed(e){return"flatten"in e}function nd(e){return"calculate"in e}function td(e){return"bin"in e}function id(e){return"impute"in e}function rd(e){return"timeUnit"in e}function sd(e){return"aggregate"in e}function od(e){return"stack"in e}function ad(e){return"fold"in e}function ud(e){return e.map((e=>{if(Uf(e)){return{filter:m(e.filter,Us)}}return e}))}var cd=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};class ld extends pf{map(e,n){var t,i;(t=n.emptySelections)!==null&&t!==void 0?t:n.emptySelections={};(i=n.selectionPredicates)!==null&&i!==void 0?i:n.selectionPredicates={};e=fd(e,n);return super.map(e,n)}mapLayerOrUnit(e,n){e=fd(e,n);if(e.encoding){const t={};for(const[i,r]of M(e.encoding)){t[i]=dd(r,n)}e=Object.assign(Object.assign({},e),{encoding:t})}return super.mapLayerOrUnit(e,n)}mapUnit(e,n){const t=e,{selection:i}=t,r=cd(t,["selection"]);if(i){return Object.assign(Object.assign({},r),{params:M(i).map((([e,t])=>{var i;const r=t,{init:s,bind:o,empty:a}=r,u=cd(r,["init","bind","empty"]);if(u.type==="single"){u.type="point";u.toggle=false}else if(u.type==="multi"){u.type="point"}n.emptySelections[e]=a!=="none";for(const c of T((i=n.selectionPredicates[e])!==null&&i!==void 0?i:{})){c.empty=a!=="none"}return{name:e,value:s,select:u,bind:o}}))})}return e}}function fd(e,n){const{transform:t}=e,i=cd(e,["transform"]);if(t){const e=t.map((e=>{if(Uf(e)){return{filter:gd(e,n)}}else if(td(e)&&Ct(e.bin)){return Object.assign(Object.assign({},e),{bin:pd(e.bin)})}else if(If(e)){const n=e.from,{selection:t}=n,i=cd(n,["selection"]);return t?Object.assign(Object.assign({},e),{from:Object.assign({param:t},i)}):e}return e}));return Object.assign(Object.assign({},i),{transform:e})}return e}function dd(e,n){var t,i;const r=b(e);if(fu(r)&&Ct(r.bin)){r.bin=pd(r.bin)}if(Ou(r)&&((i=(t=r.scale)===null||t===void 0?void 0:t.domain)===null||i===void 0?void 0:i.selection)){const e=r.scale.domain,{selection:n}=e,t=cd(e,["selection"]);r.scale.domain=Object.assign(Object.assign({},t),n?{param:n}:{})}if(au(r)){if((0,Rs.isArray)(r.condition)){r.condition=r.condition.map((e=>{const{selection:t,param:i,test:r}=e,s=cd(e,["selection","param","test"]);return i?e:Object.assign(Object.assign({},s),{test:gd(e,n)})}))}else{const e=dd(r.condition,n),{selection:t,param:i,test:s}=e,o=cd(e,["selection","param","test"]);r.condition=i?r.condition:Object.assign(Object.assign({},o),{test:gd(r.condition,n)})}}return r}function pd(e){const n=e.extent;if(n===null||n===void 0?void 0:n.selection){const{selection:t}=n,i=cd(n,["selection"]);return Object.assign(Object.assign({},e),{extent:Object.assign(Object.assign({},i),{param:t})})}return e}function gd(e,n){const t=e=>m(e,(e=>{var t,i;var r;const s=(t=n.emptySelections[e])!==null&&t!==void 0?t:true;const o={param:e,empty:s};(i=(r=n.selectionPredicates)[e])!==null&&i!==void 0?i:r[e]=[];n.selectionPredicates[e].push(o);return o}));return e.selection?t(e.selection):m(e.test||e.filter,(e=>e.selection?t(e.selection):e))}class md extends pf{map(e,n){var t;const i=(t=n.selections)!==null&&t!==void 0?t:[];if(e.params&&!fc(e)){const n=[];for(const t of e.params){if(Fl(t)){i.push(t)}else{n.push(t)}}e.params=n}n.selections=i;return super.map(e,hd(e,n))}mapUnit(e,n){var t;const i=n.selections;if(!i||!i.length)return e;const r=((t=n.path)!==null&&t!==void 0?t:[]).concat(e.name);const s=[];for(const o of i){if(!o.views||!o.views.length){s.push(o)}else{for(const n of o.views){if((0,Rs.isString)(n)&&(n===e.name||r.indexOf(n)>=0)||(0,Rs.isArray)(n)&&n.map((e=>r.indexOf(e))).every(((e,n,t)=>e!==-1&&(n===0||e>t[n-1])))){s.push(o)}}}}if(s.length)e.params=s;return e}}for(const Yw of["mapFacet","mapRepeat","mapHConcat","mapVConcat","mapLayer"]){const e=md.prototype[Yw];md.prototype[Yw]=function(n,t){return e.call(this,n,hd(n,t))}}function hd(e,n){var t;return e.name?Object.assign(Object.assign({},n),{path:((t=n.path)!==null&&t!==void 0?t:[]).concat(e.name)}):n}function bd(e,n){if(n===undefined){n=nf(e.config)}const t=xd(e,n);const{width:i,height:r}=e;const s=jd(t,{width:i,height:r,autosize:e.autosize},n);return Object.assign(Object.assign({},t),s?{autosize:s}:{})}const yd=new Mf;const vd=new ld;const Od=new md;function xd(e,n={}){const t={config:n};return Od.map(yd.map(vd.map(e,t),t),t)}function wd(e){return(0,r.Kg)(e)?{type:e}:e!==null&&e!==void 0?e:{}}function jd(e,n,t){let{width:i,height:r}=n;const s=fc(e)||cf(e);const o={};if(!s){if(i=="container"){Vr(pi("width"));i=undefined}if(r=="container"){Vr(pi("height"));r=undefined}}else{if(i=="container"&&r=="container"){o.type="fit";o.contains="padding"}else if(i=="container"){o.type="fit-x";o.contains="padding"}else if(r=="container"){o.type="fit-y";o.contains="padding"}}const a=Object.assign(Object.assign(Object.assign({type:"pad"},o),t?wd(t.autosize):{}),wd(e.autosize));if(a.type==="fit"&&!s){Vr(di);a.type="pad"}if(i=="container"&&!(a.type=="fit"||a.type=="fit-x")){Vr(gi("width"))}if(r=="container"&&!(a.type=="fit"||a.type=="fit-y")){Vr(gi("height"))}if(h(a,{type:"pad"})){return undefined}return a}function Fd(e){return e==="fit"||e==="fit-x"||e==="fit-y"}function $d(e){return e?`fit-${Hn(e)}`:"fit"}const Dd=["background","padding"];function Ad(e,n){const t={};for(const i of Dd){if(e&&e[i]!==undefined){t[i]=Vt(e[i])}}if(n){t.params=e.params}return t}class kd{constructor(e={},n={}){this.explicit=e;this.implicit=n}clone(){return new kd(b(this.explicit),b(this.implicit))}combine(){return Object.assign(Object.assign({},this.explicit),this.implicit)}get(e){return X(this.explicit[e],this.implicit[e])}getWithExplicit(e){if(this.explicit[e]!==undefined){return{explicit:true,value:this.explicit[e]}}else if(this.implicit[e]!==undefined){return{explicit:false,value:this.implicit[e]}}return{explicit:false,value:undefined}}setWithExplicit(e,{value:n,explicit:t}){if(n!==undefined){this.set(e,n,t)}}set(e,n,t){delete this[t?"implicit":"explicit"][e];this[t?"explicit":"implicit"][e]=n;return this}copyKeyFromSplit(e,{explicit:n,implicit:t}){if(n[e]!==undefined){this.set(e,n[e],true)}else if(t[e]!==undefined){this.set(e,t[e],false)}}copyKeyFromObject(e,n){if(n[e]!==undefined){this.set(e,n[e],true)}}copyAll(e){for(const n of N(e.combine())){const t=e.getWithExplicit(n);this.setWithExplicit(n,t)}}}function Cd(e){return{explicit:true,value:e}}function Sd(e){return{explicit:false,value:e}}function Ed(e){return(n,t,i,r)=>{const s=e(n.value,t.value);if(s>0){return n}else if(s<0){return t}return Bd(n,t,i,r)}}function Bd(e,n,t,i){if(e.explicit&&n.explicit){Vr(yr(t,i,e.value,n.value))}return e}function Pd(e,n,t,i,r=Bd){if(e===undefined||e.value===undefined){return n}if(e.explicit&&!n.explicit){return e}else if(n.explicit&&!e.explicit){return n}else if(h(e.value,n.value)){return e}else{return r(e,n,t,i)}}class _d extends kd{constructor(e={},n={},t=false){super(e,n);this.explicit=e;this.implicit=n;this.parseNothing=t}clone(){const e=super.clone();e.parseNothing=this.parseNothing;return e}}function zd(e){return"url"in e}function Nd(e){return"values"in e}function Td(e){return"name"in e&&!zd(e)&&!Nd(e)&&!Md(e)}function Md(e){return e&&(Ld(e)||qd(e)||Ud(e))}function Ld(e){return"sequence"in e}function qd(e){return"sphere"in e}function Ud(e){return"graticule"in e}var Rd;(function(e){e[e["Raw"]=0]="Raw";e[e["Main"]=1]="Main";e[e["Row"]=2]="Row";e[e["Column"]=3]="Column";e[e["Lookup"]=4]="Lookup"})(Rd||(Rd={}));var Id=t(45948);var Wd=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function Hd(e,n=true,t=r.D_){if((0,r.cy)(e)){const i=e.map((e=>Hd(e,n,t)));return n?`[${i.join(", ")}]`:i}else if(Jr(e)){if(n){return t(as(e))}else{return t(cs(e))}}return n?t(x(e)):e}function Gd(e,n){var t;for(const i of T((t=e.component.selection)!==null&&t!==void 0?t:{})){const t=i.name;let s=`${t}${Sg}, ${i.resolve==="global"?"true":`{unit: ${Ng(e)}}`}`;for(const r of _g){if(!r.defined(i))continue;if(r.signals)n=r.signals(e,i,n);if(r.modifyExpr)s=r.modifyExpr(e,i,s)}n.push({name:t+Eg,on:[{events:{signal:i.name+Sg},update:`modify(${(0,r.r$)(i.name+Cg)}, ${s})`}]})}return Zd(n)}function Yd(e,n){if(e.component.selection&&N(e.component.selection).length){const t=(0,r.r$)(e.getName("cell"));n.unshift({name:"facet",value:{},on:[{events:(0,Id.P)("mousemove","scope"),update:`isTuple(facet) ? facet : group(${t}).datum`}]})}return Zd(n)}function Kd(e,n){var t;let i=false;for(const s of T((t=e.component.selection)!==null&&t!==void 0?t:{})){const t=s.name;const o=(0,r.r$)(t+Cg);const a=n.filter((e=>e.name===t));if(a.length===0){const e=s.resolve==="global"?"union":s.resolve;const t=s.type==="point"?", true, true)":")";n.push({name:s.name,update:`${Pg}(${o}, ${(0,r.r$)(e)}${t}`})}i=true;for(const i of _g){if(i.defined(s)&&i.topLevelSignals){n=i.topLevelSignals(e,s,n)}}}if(i){const e=n.filter((e=>e.name==="unit"));if(e.length===0){n.unshift({name:"unit",value:{},on:[{events:"mousemove",update:"isTuple(group()) ? group() : unit"}]})}}return Zd(n)}function Vd(e,n){var t;const i=[...n];const r=Ng(e,{escape:false});for(const s of T((t=e.component.selection)!==null&&t!==void 0?t:{})){const e={name:s.name+Cg};if(s.project.hasSelectionId){e.transform=[{type:"collect",sort:{field:Ol}}]}if(s.init){const n=s.project.items.map((e=>{const{signals:n}=e,t=Wd(e,["signals"]);return t}));e.values=s.project.hasSelectionId?s.init.map((e=>({unit:r,[Ol]:Hd(e,false)[0]}))):s.init.map((e=>({unit:r,fields:n,values:Hd(e,false)})))}const n=i.filter((e=>e.name===s.name+Cg));if(!n.length){i.push(e)}}return i}function Qd(e,n){var t;for(const i of T((t=e.component.selection)!==null&&t!==void 0?t:{})){for(const t of _g){if(t.defined(i)&&t.marks){n=t.marks(e,i,n)}}}return n}function Xd(e,n){for(const t of e.children){if(ZO(t)){n=Qd(t,n)}}return n}function Jd(e,n,t,i){const s=tb(e,n.param,n);return{signal:ho(t.get("type"))&&(0,r.cy)(i)&&i[0]>i[1]?`isValid(${s}) && reverse(${s})`:s}}function Zd(e){return e.map((e=>{if(e.on&&!e.on.length)delete e.on;return e}))}class ep{constructor(e,n){this.debugName=n;this._children=[];this._parent=null;if(e){this.parent=e}}clone(){throw new Error("Cannot clone node")}get parent(){return this._parent}set parent(e){this._parent=e;if(e){e.addChild(this)}}get children(){return this._children}numChildren(){return this._children.length}addChild(e,n){if(this._children.includes(e)){Vr(Pi);return}if(n!==undefined){this._children.splice(n,0,e)}else{this._children.push(e)}}removeChild(e){const n=this._children.indexOf(e);this._children.splice(n,1);return n}remove(){let e=this._parent.removeChild(this);for(const n of this._children){n._parent=this._parent;this._parent.addChild(n,e++)}}insertAsParentOf(e){const n=e.parent;n.removeChild(this);this.parent=n;e.parent=this}swapWithParent(){const e=this._parent;const n=e.parent;for(const i of this._children){i.parent=e}this._children=[];e.removeChild(this);const t=e.parent.removeChild(e);this._parent=n;n.addChild(this,t);e.parent=this}}class np extends ep{clone(){const e=new this.constructor;e.debugName=`clone_${this.debugName}`;e._source=this._source;e._name=`clone_${this._name}`;e.type=this.type;e.refCounts=this.refCounts;e.refCounts[e._name]=0;return e}constructor(e,n,t,i){super(e,n);this.type=t;this.refCounts=i;this._source=this._name=n;if(this.refCounts&&!(this._name in this.refCounts)){this.refCounts[this._name]=0}}dependentFields(){return new Set}producedFields(){return new Set}hash(){if(this._hash===undefined){this._hash=`Output ${Z()}`}return this._hash}getSource(){this.refCounts[this._name]++;return this._source}isRequired(){return!!this.refCounts[this._name]}setSource(e){this._source=e}}var tp=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};class ip extends ep{clone(){return new ip(null,b(this.formula))}constructor(e,n){super(e);this.formula=n}static makeFromEncoding(e,n){const t=n.reduceFieldDef(((e,n)=>{const{field:t,timeUnit:i}=n;if(i){const r=Du(n,{forAs:true});e[w({as:r,field:t,timeUnit:i})]={as:r,field:t,timeUnit:i}}return e}),{});if(z(t)){return null}return new ip(e,t)}static makeFromTransform(e,n){const t=Object.assign({},n),{timeUnit:i}=t,r=tp(t,["timeUnit"]);const s=$s(i);const o=Object.assign(Object.assign({},r),{timeUnit:s});return new ip(e,{[w(o)]:o})}merge(e){this.formula=Object.assign({},this.formula);for(const n in e.formula){if(!this.formula[n]){this.formula[n]=e.formula[n]}}for(const n of e.children){e.removeChild(n);n.parent=this}e.remove()}removeFormulas(e){const n={};for(const[t,i]of M(this.formula)){if(!e.has(i.as)){n[t]=i}}this.formula=n}producedFields(){return new Set(T(this.formula).map((e=>e.as)))}dependentFields(){return new Set(T(this.formula).map((e=>e.field)))}hash(){return`TimeUnit ${w(this.formula)}`}assemble(){const e=[];for(const n of T(this.formula)){const{field:t,as:i,timeUnit:r}=n;const s=$s(r),{unit:o,utc:a}=s,u=tp(s,["unit","utc"]);e.push(Object.assign(Object.assign(Object.assign(Object.assign({field:Y(t),type:"timeunit"},o?{units:Os(o)}:{}),a?{timezone:"utc"}:{}),u),{as:[i,`${i}_end`]}))}return e}}var rp=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};const sp="_tuple_fields";class op{constructor(...e){this.items=e;this.hasChannel={};this.hasField={};this.hasSelectionId=false}}const ap={defined:()=>true,parse:(e,n,t)=>{var i;const s=n.name;const o=(i=n.project)!==null&&i!==void 0?i:n.project=new op;const a={};const u={};const c=new Set;const l=(e,n)=>{const t=n==="visual"?e.channel:e.field;let i=q(`${s}_${t}`);for(let r=1;c.has(i);r++){i=q(`${s}_${t}_${r}`)}c.add(i);return{[n]:i}};const f=n.type;const d=e.config.selection[f];const p=t.value!==undefined?(0,r.YO)(t.value):null;let{fields:g,encodings:m}=(0,r.Gv)(t.select)?t.select:{};if(!g&&!m&&p){for(const e of p){if(!(0,r.Gv)(e)){continue}for(const n of N(e)){if(dn(n)){(m||(m=[])).push(n)}else{if(f==="interval"){Vr(Ai);m=d.encodings}else{(g||(g=[])).push(n)}}}}}if(!g&&!m){m=d.encodings;if("fields"in d){g=d.fields}}for(const r of m!==null&&m!==void 0?m:[]){const n=e.fieldDef(r);if(n){let t=n.field;if(n.aggregate){Vr(yi(r,n.aggregate));continue}else if(!t){Vr(bi(r));continue}if(n.timeUnit){t=e.vgField(r);const i={timeUnit:n.timeUnit,as:t,field:n.field};u[w(i)]=i}if(!a[t]){let i="E";if(f==="interval"){const n=e.getScaleComponent(r).get("type");if(ho(n)){i="R"}}else if(n.bin){i="R-RE"}const s={field:t,channel:r,type:i};s.signals=Object.assign(Object.assign({},l(s,"data")),l(s,"visual"));o.items.push(a[t]=s);o.hasField[t]=o.hasChannel[r]=a[t];o.hasSelectionId=o.hasSelectionId||t===Ol}}else{Vr(bi(r))}}for(const r of g!==null&&g!==void 0?g:[]){if(o.hasField[r])continue;const e={type:"E",field:r};e.signals=Object.assign({},l(e,"data"));o.items.push(e);o.hasField[r]=e;o.hasSelectionId=o.hasSelectionId||r===Ol}if(p){n.init=p.map((e=>o.items.map((n=>(0,r.Gv)(e)?e[n.channel]!==undefined?e[n.channel]:e[n.field]:e))))}if(!z(u)){o.timeUnit=new ip(null,u)}},signals:(e,n,t)=>{const i=n.name+sp;const r=t.filter((e=>e.name===i));return r.length>0||n.project.hasSelectionId?t:t.concat({name:i,value:n.project.items.map((e=>{const{signals:n,hasLegend:t}=e,i=rp(e,["signals","hasLegend"]);i.field=Y(i.field);return i}))})}};const up=ap;const cp={defined:e=>e.type==="interval"&&e.resolve==="global"&&e.bind&&e.bind==="scales",parse:(e,n)=>{const t=n.scales=[];for(const i of n.project.items){const r=i.channel;if(!ct(r)){continue}const s=e.getScaleComponent(r);const o=s?s.get("type"):undefined;if(!s||!ho(o)){Vr(wi);continue}s.set("selectionExtent",{param:n.name,field:i.field},true);t.push(i)}},topLevelSignals:(e,n,t)=>{const i=n.scales.filter((e=>t.filter((n=>n.name===e.signals.data)).length===0));if(!e.parent||dp(e)||i.length===0){return t}const s=t.filter((e=>e.name===n.name))[0];let o=s.update;if(o.indexOf(Pg)>=0){s.update=`{${i.map((e=>`${(0,r.r$)(Y(e.field))}: ${e.signals.data}`)).join(", ")}}`}else{for(const e of i){const n=`${(0,r.r$)(Y(e.field))}: ${e.signals.data}`;if(!o.includes(n)){o=`${o.substring(0,o.length-1)}, ${n}}`}}s.update=o}return t.concat(i.map((e=>({name:e.signals.data}))))},signals:(e,n,t)=>{if(e.parent&&!dp(e)){for(const e of n.scales){const n=t.filter((n=>n.name===e.signals.data))[0];n.push="outer";delete n.value;delete n.update}}return t}};const lp=cp;function fp(e,n){const t=(0,r.r$)(e.scaleName(n));return`domain(${t})`}function dp(e){var n;return e.parent&&tx(e.parent)&&((n=!e.parent.parent)!==null&&n!==void 0?n:dp(e.parent.parent))}var pp=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};const gp="_brush";const mp="_scale_trigger";const hp={defined:e=>e.type==="interval",signals:(e,n,t)=>{const i=n.name;const s=i+sp;const o=lp.defined(n);const a=n.init?n.init[0]:null;const u=[];const c=[];if(n.translate&&!o){const e=`!event.item || event.item.mark.name !== ${(0,r.r$)(i+gp)}`;vp(n,((n,t)=>{var i;var s;const o=(0,r.YO)((i=(s=t.between[0]).filter)!==null&&i!==void 0?i:s.filter=[]);if(!o.includes(e)){o.push(e)}return n}))}n.project.items.forEach(((i,s)=>{const o=i.channel;if(o!==ce&&o!==le){Vr("Interval selections only support x and y encoding channels.");return}const l=a?a[s]:null;const f=yp(e,n,i,l);const d=i.signals.data;const p=i.signals.visual;const g=(0,r.r$)(e.scaleName(o));const m=e.getScaleComponent(o).get("type");const h=ho(m)?"+":"";t.push(...f);u.push(d);c.push({scaleName:e.scaleName(o),expr:`(!isArray(${d}) || `+`(${h}invert(${g}, ${p})[0] === ${h}${d}[0] && `+`${h}invert(${g}, ${p})[1] === ${h}${d}[1]))`})}));if(!o&&c.length){t.push({name:i+mp,value:{},on:[{events:c.map((e=>({scale:e.scaleName}))),update:`${c.map((e=>e.expr)).join(" && ")} ? ${i+mp} : {}`}]})}const l=`unit: ${Ng(e)}, fields: ${s}, values`;return t.concat(Object.assign(Object.assign({name:i+Sg},a?{init:`{${l}: ${Hd(a)}}`}:{}),u.length?{on:[{events:[{signal:u.join(" || ")}],update:`${u.join(" && ")} ? {${l}: [${u}]} : null`}]}:{}))},marks:(e,n,t)=>{const i=n.name;const{x:s,y:o}=n.project.hasChannel;const a=s===null||s===void 0?void 0:s.signals.visual;const u=o===null||o===void 0?void 0:o.signals.visual;const c=`data(${(0,r.r$)(n.name+Cg)})`;if(lp.defined(n)||!s&&!o){return t}const l={x:s!==undefined?{signal:`${a}[0]`}:{value:0},y:o!==undefined?{signal:`${u}[0]`}:{value:0},x2:s!==undefined?{signal:`${a}[1]`}:{field:{group:"width"}},y2:o!==undefined?{signal:`${u}[1]`}:{field:{group:"height"}}};if(n.resolve==="global"){for(const n of N(l)){l[n]=[Object.assign({test:`${c}.length && ${c}[0].unit === ${Ng(e)}`},l[n]),{value:0}]}}const f=n.mark,{fill:d,fillOpacity:p,cursor:g}=f,m=pp(f,["fill","fillOpacity","cursor"]);const h=N(m).reduce(((e,n)=>{e[n]=[{test:[s!==undefined&&`${a}[0] !== ${a}[1]`,o!==undefined&&`${u}[0] !== ${u}[1]`].filter((e=>e)).join(" && "),value:m[n]},{value:null}];return e}),{});return[{name:`${i+gp}_bg`,type:"rect",clip:true,encode:{enter:{fill:{value:d},fillOpacity:{value:p}},update:l}},...t,{name:i+gp,type:"rect",clip:true,encode:{enter:Object.assign(Object.assign({},g?{cursor:{value:g}}:{}),{fill:{value:"transparent"}}),update:Object.assign(Object.assign({},l),h)}}]}};const bp=hp;function yp(e,n,t,i){const s=t.channel;const o=t.signals.visual;const a=t.signals.data;const u=lp.defined(n);const c=(0,r.r$)(e.scaleName(s));const l=e.getScaleComponent(s);const f=l?l.get("type"):undefined;const d=e=>`scale(${c}, ${e})`;const p=e.getSizeSignalRef(s===ce?"width":"height").signal;const g=`${s}(unit)`;const m=vp(n,((e,n)=>[...e,{events:n.between[0],update:`[${g}, ${g}]`},{events:n,update:`[${o}[0], clamp(${g}, 0, ${p})]`}]));m.push({events:{signal:n.name+mp},update:ho(f)?`[${d(`${a}[0]`)}, ${d(`${a}[1]`)}]`:`[0, 0]`});return u?[{name:a,on:[]}]:[Object.assign(Object.assign({name:o},i?{init:Hd(i,true,d)}:{value:[]}),{on:m}),Object.assign(Object.assign({name:a},i?{init:Hd(i)}:{}),{on:[{events:{signal:o},update:`${o}[0] === ${o}[1] ? null : invert(${c}, ${o})`}]})]}function vp(e,n){return e.events.reduce(((e,t)=>{if(!t.between){Vr(`${t} is not an ordered event stream for interval selections.`);return e}return n(e,t)}),[])}const Op={defined:e=>e.type==="point",signals:(e,n,t)=>{var i;const s=n.name;const o=s+sp;const a=n.project;const u="(item().isVoronoi ? datum.datum : datum)";const c=T((i=e.component.selection)!==null&&i!==void 0?i:{}).reduce(((e,n)=>n.type==="interval"?e.concat(n.name+gp):e),[]).map((e=>`indexof(item().mark.name, '${e}') < 0`)).join(" && ");const l=`datum && item().mark.marktype !== 'group' && indexof(item().mark.role, 'legend') < 0${c?` && ${c}`:""}`;let f=`unit: ${Ng(e)}, `;if(n.project.hasSelectionId){f+=`${Ol}: ${u}[${(0,r.r$)(Ol)}]`}else{const n=a.items.map((n=>{const t=e.fieldDef(n.channel);return(t===null||t===void 0?void 0:t.bin)?`[${u}[${(0,r.r$)(e.vgField(n.channel,{}))}], `+`${u}[${(0,r.r$)(e.vgField(n.channel,{binSuffix:"end"}))}]]`:`${u}[${(0,r.r$)(n.field)}]`})).join(", ");f+=`fields: ${o}, values: [${n}]`}const d=n.events;return t.concat([{name:s+Sg,on:d?[{events:d,update:`${l} ? {${f}} : null`,force:true}]:[]}])}};const xp=Op;function wp(e,n,t,i){const s=au(n)&&n.condition;const o=i(n);if(s){const n=(0,r.YO)(s);const a=n.map((n=>{const t=i(n);if(eu(n)){const{param:i,empty:r}=n;const s=nb(e,{param:i,empty:r});return Object.assign({test:s},t)}else{const i=rb(e,n.test);return Object.assign({test:i},t)}}));return{[t]:[...a,...o!==undefined?[o]:[]]}}else{return o!==undefined?{[t]:o}:{}}}function jp(e,n="text"){const t=e.encoding[n];return wp(e,t,n,(n=>Fp(n,e.config)))}function Fp(e,n,t="datum"){if(e){if(vu(e)){return Xt(e.value)}if(bu(e)){const{format:i,formatType:r}=Lu(e);return Pa({fieldOrDatumDef:e,format:i,formatType:r,expr:t,config:n})}}return undefined}function $p(e,n={}){const{encoding:t,markDef:i,config:s,stack:o}=e;const a=t.tooltip;if((0,r.cy)(a)){return{tooltip:Ap({tooltip:a},o,s,n)}}else{const u=n.reactiveGeom?"datum.datum":"datum";return wp(e,a,"tooltip",(e=>{const a=Fp(e,s,u);if(a){return a}if(e===null){return undefined}let c=ii("tooltip",i,s);if(c===true){c={content:"encoding"}}if((0,r.Kg)(c)){return{value:c}}else if((0,r.Gv)(c)){if(Tt(c)){return c}else if(c.content==="encoding"){return Ap(t,o,s,n)}else{return{signal:u}}}return undefined}))}}function Dp(e,n,t,{reactiveGeom:i}={}){const s={};const o=i?"datum.datum":"datum";const a=[];function u(i,u){const c=hn(u);const l=yu(i)?i:Object.assign(Object.assign({},i),{type:e[c].type});const f=l.title||Mu(l,t);const d=(0,r.YO)(f).join(", ");let p;if(Rn(u)){const n=u==="x"?"x2":"y2";const i=Uu(e[n]);if(kt(l.bin)&&i){const e=Du(l,{expr:o});const r=Du(i,{expr:o});const{format:a,formatType:u}=Lu(l);p=Ra(e,r,a,u,t);s[n]=true}}if((Rn(u)||u===be||u===me)&&n&&n.fieldChannel===u&&n.offset==="normalize"){const{format:e,formatType:n}=Lu(l);p=Pa({fieldOrDatumDef:l,format:e,formatType:n,expr:o,config:t,normalizeStack:true}).signal}p!==null&&p!==void 0?p:p=Fp(l,t,o).signal;a.push({channel:u,key:d,value:p})}jc(e,((e,n)=>{if(fu(e)){u(e,n)}else if(uu(e)){u(e.condition,n)}}));const c={};for(const{channel:r,key:l,value:f}of a){if(!s[r]&&!c[l]){c[l]=f}}return c}function Ap(e,n,t,{reactiveGeom:i}={}){const r=Dp(e,n,t,{reactiveGeom:i});const s=M(r).map((([e,n])=>`"${e}": ${n}`));return s.length>0?{signal:`{${s.join(", ")}}`}:undefined}function kp(e){const{markDef:n,config:t}=e;const i=ii("aria",n,t);if(i===false){return{}}return Object.assign(Object.assign(Object.assign({},i?{aria:i}:{}),Cp(e)),Sp(e))}function Cp(e){const{mark:n,markDef:t,config:i}=e;if(i.aria===false){return{}}const r=ii("ariaRoleDescription",t,i);if(r!=null){return{ariaRoleDescription:{value:r}}}return n in Wt?{}:{ariaRoleDescription:{value:n}}}function Sp(e){const{encoding:n,markDef:t,config:i,stack:r}=e;const s=n.description;if(s){return wp(e,s,"description",(n=>Fp(n,e.config)))}const o=ii("description",t,i);if(o!=null){return{description:Xt(o)}}if(i.aria===false){return{}}const a=Dp(n,r,i);if(z(a)){return undefined}return{description:{signal:M(a).map((([e,n],t)=>`"${t>0?"; ":""}${e}: " + (${n})`)).join(" + ")}}}function Ep(e,n,t={}){const{markDef:i,encoding:r,config:s}=n;const{vgChannel:o}=t;let{defaultRef:a,defaultValue:u}=t;if(a===undefined){u!==null&&u!==void 0?u:u=ii(e,i,s,{vgChannel:o,ignoreVgConfig:true});if(u!==undefined){a=Xt(u)}}const c=r[e];return wp(n,c,o!==null&&o!==void 0?o:e,(t=>ka({channel:e,channelDef:t,markDef:i,config:s,scaleName:n.scaleName(e),scale:n.getScaleComponent(e),stack:null,defaultRef:a})))}function Bp(e,n={filled:undefined}){var t,i,r,s;const{markDef:o,encoding:a,config:u}=e;const{type:c}=o;const l=(t=n.filled)!==null&&t!==void 0?t:ii("filled",o,u);const f=F(["bar","point","circle","square","geoshape"],c)?"transparent":undefined;const d=(r=(i=ii(l===true?"color":undefined,o,u,{vgChannel:"fill"}))!==null&&i!==void 0?i:u.mark[l===true&&"color"])!==null&&r!==void 0?r:f;const p=(s=ii(l===false?"color":undefined,o,u,{vgChannel:"stroke"}))!==null&&s!==void 0?s:u.mark[l===false&&"color"];const g=l?"fill":"stroke";const m=Object.assign(Object.assign({},d?{fill:Xt(d)}:{}),p?{stroke:Xt(p)}:{});if(o.color&&(l?o.fill:o.stroke)){Vr(Gi("property",{fill:"fill"in o,stroke:"stroke"in o}))}return Object.assign(Object.assign(Object.assign(Object.assign({},m),Ep("color",e,{vgChannel:g,defaultValue:l?d:p})),Ep("fill",e,{defaultValue:a.fill?d:undefined})),Ep("stroke",e,{defaultValue:a.stroke?p:undefined}))}function Pp(e){const{encoding:n,mark:t}=e;const i=n.order;if(!ea(t)&&vu(i)){return wp(e,i,"zindex",(e=>Xt(e.value)))}return{}}function _p({channel:e,markDef:n,encoding:t={},model:i,bandPosition:r}){const s=`${e}Offset`;const o=n[s];const a=t[s];if((s==="xOffset"||s==="yOffset")&&a){const e=ka({channel:s,channelDef:a,markDef:n,config:i===null||i===void 0?void 0:i.config,scaleName:i.scaleName(s),scale:i.getScaleComponent(s),stack:null,defaultRef:Xt(o),bandPosition:r});return{offsetType:"encoding",offset:e}}const u=n[s];if(u){return{offsetType:"visual",offset:u}}return{}}function zp(e,n,{defaultPos:t,vgChannel:i}){const{encoding:r,markDef:s,config:o,stack:a}=n;const u=r[e];const c=r[yn(e)];const l=n.scaleName(e);const f=n.getScaleComponent(e);const{offset:d,offsetType:p}=_p({channel:e,markDef:s,encoding:r,model:n,bandPosition:.5});const g=Tp({model:n,defaultPos:t,channel:e,scaleName:l,scale:f});const m=!u&&Rn(e)&&(r.latitude||r.longitude)?{field:n.getName(e)}:Np({channel:e,channelDef:u,channel2Def:c,markDef:s,config:o,scaleName:l,scale:f,stack:a,offset:d,defaultRef:g,bandPosition:p==="encoding"?0:undefined});return m?{[i||e]:m}:undefined}function Np(e){const{channel:n,channelDef:t,scaleName:i,stack:r,offset:s,markDef:o}=e;if(bu(t)&&r&&n===r.fieldChannel){if(fu(t)){let e=t.bandPosition;if(e===undefined&&o.type==="text"&&(n==="radius"||n==="theta")){e=.5}if(e!==undefined){return Aa({scaleName:i,fieldOrDatumDef:t,startSuffix:"start",bandPosition:e,offset:s})}}return Da(t,i,{suffix:"end"},{offset:s})}return xa(e)}function Tp({model:e,defaultPos:n,channel:t,scaleName:i,scale:r}){const{markDef:s,config:o}=e;return()=>{const a=hn(t);const u=bn(t);const c=ii(t,s,o,{vgChannel:u});if(c!==undefined){return Ca(t,c)}switch(n){case"zeroOrMin":case"zeroOrMax":if(i){const e=r.get("type");if(F([no.LOG,no.TIME,no.UTC],e)){}else{if(r.domainDefinitelyIncludesZero()){return{scale:i,value:0}}}}if(n==="zeroOrMin"){return a==="y"?{field:{group:"height"}}:{value:0}}else{switch(a){case"radius":return{signal:`min(${e.width.signal},${e.height.signal})/2`};case"theta":return{signal:"2*PI"};case"x":return{field:{group:"width"}};case"y":return{value:0}}}break;case"mid":{const n=e[vn(t)];return Object.assign(Object.assign({},n),{mult:.5})}}return undefined}}const Mp={left:"x",center:"xc",right:"x2"};const Lp={top:"y",middle:"yc",bottom:"y2"};function qp(e,n,t,i="middle"){if(e==="radius"||e==="theta"){return bn(e)}const r=e==="x"?"align":"baseline";const s=ii(r,n,t);let o;if(Tt(s)){Vr(ir(r));o=undefined}else{o=s}if(e==="x"){return Mp[o||(i==="top"?"left":"center")]}else{return Lp[o||i]}}function Up(e,n,{defaultPos:t,defaultPos2:i,range:r}){if(r){return Rp(e,n,{defaultPos:t,defaultPos2:i})}return zp(e,n,{defaultPos:t})}function Rp(e,n,{defaultPos:t,defaultPos2:i}){const{markDef:r,config:s}=n;const o=yn(e);const a=vn(e);const u=Ip(n,i,o);const c=u[a]?qp(e,r,s):bn(e);return Object.assign(Object.assign({},zp(e,n,{defaultPos:t,vgChannel:c})),u)}function Ip(e,n,t){const{encoding:i,mark:r,markDef:s,stack:o,config:a}=e;const u=hn(t);const c=vn(t);const l=bn(t);const f=i[u];const d=e.scaleName(u);const p=e.getScaleComponent(u);const{offset:g}=t in i||t in s?_p({channel:t,markDef:s,encoding:i,model:e}):_p({channel:u,markDef:s,encoding:i,model:e});if(!f&&(t==="x2"||t==="y2")&&(i.latitude||i.longitude)){const n=vn(t);const i=e.markDef[n];if(i!=null){return{[n]:{value:i}}}else{return{[l]:{field:e.getName(t)}}}}const m=Wp({channel:t,channelDef:f,channel2Def:i[t],markDef:s,config:a,scaleName:d,scale:p,stack:o,offset:g,defaultRef:undefined});if(m!==undefined){return{[l]:m}}return Hp(t,s)||Hp(t,{[t]:si(t,s,a.style),[c]:si(c,s,a.style)})||Hp(t,a[r])||Hp(t,a.mark)||{[l]:Tp({model:e,defaultPos:n,channel:t,scaleName:d,scale:p})()}}function Wp({channel:e,channelDef:n,channel2Def:t,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:u,defaultRef:c}){if(bu(n)&&a&&e.charAt(0)===a.fieldChannel.charAt(0)){return Da(n,s,{suffix:"start"},{offset:u})}return xa({channel:e,channelDef:t,scaleName:s,scale:o,stack:a,markDef:i,config:r,offset:u,defaultRef:c})}function Hp(e,n){const t=vn(e);const i=bn(e);if(n[i]!==undefined){return{[i]:Ca(e,n[i])}}else if(n[e]!==undefined){return{[i]:Ca(e,n[e])}}else if(n[t]){const i=n[t];if(ga(i)){Vr(Yi(t))}else{return{[t]:Ca(e,i)}}}return undefined}function Gp(e,n){var t,i;const{config:r,encoding:s,markDef:o}=e;const a=o.type;const u=yn(n);const c=vn(n);const l=s[n];const f=s[u];const d=e.getScaleComponent(n);const p=d?d.get("type"):undefined;const g=o.orient;const m=(i=(t=s[c])!==null&&t!==void 0?t:s.size)!==null&&i!==void 0?i:ii("size",o,r,{vgChannel:c});const h=a==="bar"&&(n==="x"?g==="vertical":g==="horizontal");if(fu(l)&&(At(l.bin)||kt(l.bin)||l.timeUnit&&!f)&&!(m&&!ga(m))&&!mo(p)){return Qp({fieldDef:l,fieldDef2:f,channel:n,model:e})}else if((bu(l)&&mo(p)||h)&&!f){return Kp(l,n,e)}else{return Rp(n,e,{defaultPos:"zeroOrMax",defaultPos2:"zeroOrMin"})}}function Yp(e,n,t,i,s){if(ga(s)){if(t){const e=t.get("type");if(e==="band"){let e=`bandwidth('${n}')`;if(s.band!==1){e=`${s.band} * ${e}`}return{signal:`max(0.25, ${e})`}}else if(s.band!==1){Vr(ur(e));s=undefined}}else{return{mult:s.band,field:{group:e}}}}else if(Tt(s)){return s}else if(s){return{value:s}}if(t){const e=t.get("range");if(Mt(e)&&(0,r.Et)(e.step)){return{value:e.step-2}}}const o=ql(i.view,e);return{value:o-2}}function Kp(e,n,t){const{markDef:i,encoding:s,config:o,stack:a}=t;const u=i.orient;const c=t.scaleName(n);const l=t.getScaleComponent(n);const f=vn(n);const d=yn(n);const p=On(n);const g=t.scaleName(p);const m=u==="horizontal"&&n==="y"||u==="vertical"&&n==="x";let h;if(s.size||i.size){if(m){h=Ep("size",t,{vgChannel:f,defaultRef:Xt(i.size)})}else{Vr(dr(i.type))}}const b=!!h;const y=su({channel:n,fieldDef:e,markDef:i,config:o,scaleType:l===null||l===void 0?void 0:l.get("type"),useVlSizeChannel:m});h=h||{[f]:Yp(f,g||c,l,o,y)};const v=(l===null||l===void 0?void 0:l.get("type"))==="band"&&ga(y)&&!b?"top":"middle";const O=qp(n,i,o,v);const x=O==="xc"||O==="yc";const{offset:w,offsetType:j}=_p({channel:n,markDef:i,encoding:s,model:t,bandPosition:x?.5:0});const F=xa({channel:n,channelDef:e,markDef:i,config:o,scaleName:c,scale:l,stack:a,offset:w,defaultRef:Tp({model:t,defaultPos:"mid",channel:n,scaleName:c,scale:l}),bandPosition:x?j==="encoding"?0:.5:Tt(y)?{signal:`(1-${y})/2`}:ga(y)?(1-y.band)/2:0});if(f){return Object.assign({[O]:F},h)}else{const e=bn(d);const n=h[f];const t=w?Object.assign(Object.assign({},n),{offset:w}):n;return{[O]:F,[e]:(0,r.cy)(F)?[F[0],Object.assign(Object.assign({},F[1]),{offset:t})]:Object.assign(Object.assign({},F),{offset:t})}}}function Vp(e,n,t,i,r){if(We(e)){return 0}const s=e==="x"||e==="y2"?-n/2:n/2;if(Tt(t)||Tt(r)||Tt(i)){const e=ei(t);const n=ei(r);const o=ei(i);const a=o?`${o} + `:"";const u=e?`(${e} ? -1 : 1) * `:"";const c=n?`(${n} + ${s})`:s;return{signal:a+u+c}}else{r=r||0;return i+(t?-r-s:+r+s)}}function Qp({fieldDef:e,fieldDef2:n,channel:t,model:i}){var r,s,o;const{config:a,markDef:u,encoding:c}=i;const l=i.getScaleComponent(t);const f=i.scaleName(t);const d=l?l.get("type"):undefined;const p=l.get("reverse");const g=su({channel:t,fieldDef:e,markDef:u,config:a,scaleType:d});const m=(r=i.component.axes[t])===null||r===void 0?void 0:r[0];const h=(s=m===null||m===void 0?void 0:m.get("translate"))!==null&&s!==void 0?s:.5;const b=Rn(t)?(o=ii("binSpacing",u,a))!==null&&o!==void 0?o:0:0;const y=yn(t);const v=bn(t);const O=bn(y);const{offset:x}=_p({channel:t,markDef:u,encoding:c,model:i,bandPosition:0});const w=Tt(g)?{signal:`(1-${g.signal})/2`}:ga(g)?(1-g.band)/2:.5;if(At(e.bin)||e.timeUnit){return{[O]:Xp({fieldDef:e,scaleName:f,bandPosition:w,offset:Vp(y,b,p,h,x)}),[v]:Xp({fieldDef:e,scaleName:f,bandPosition:Tt(w)?{signal:`1-${w.signal}`}:1-w,offset:Vp(t,b,p,h,x)})}}else if(kt(e.bin)){const i=Da(e,f,{},{offset:Vp(y,b,p,h,x)});if(fu(n)){return{[O]:i,[v]:Da(n,f,{},{offset:Vp(t,b,p,h,x)})}}else if(Ct(e.bin)&&e.bin.step){return{[O]:i,[v]:{signal:`scale("${f}", ${Du(e,{expr:"datum"})} + ${e.bin.step})`,offset:Vp(t,b,p,h,x)}}}}Vr(Nr(y));return undefined}function Xp({fieldDef:e,scaleName:n,bandPosition:t,offset:i}){return Aa({scaleName:n,fieldOrDatumDef:e,bandPosition:t,offset:i})}const Jp=new Set(["aria","width","height"]);function Zp(e,n){const{fill:t=undefined,stroke:i=undefined}=n.color==="include"?Bp(e):{};return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},ng(e.markDef,n)),eg(e,"fill",t)),eg(e,"stroke",i)),Ep("opacity",e)),Ep("fillOpacity",e)),Ep("strokeOpacity",e)),Ep("strokeWidth",e)),Ep("strokeDash",e)),Pp(e)),$p(e)),jp(e,"href")),kp(e))}function eg(e,n,t){const{config:i,mark:s,markDef:o}=e;const a=ii("invalid",o,i);if(a==="hide"&&t&&!ea(s)){const i=tg(e,{invalid:true,channels:ut});if(i){return{[n]:[{test:i,value:null},...(0,r.YO)(t)]}}}return t?{[n]:t}:{}}function ng(e,n){return It.reduce(((t,i)=>{if(!Jp.has(i)&&e[i]!==undefined&&n[i]!=="ignore"){t[i]=Xt(e[i])}return t}),{})}function tg(e,{invalid:n=false,channels:t}){const i=t.reduce(((n,t)=>{const i=e.getScaleComponent(t);if(i){const r=i.get("type");const s=e.vgField(t,{expr:"datum"});if(s&&ho(r)){n[s]=true}}return n}),{});const r=N(i);if(r.length>0){const e=n?"||":"&&";return r.map((e=>Fa(e,n))).join(` ${e} `)}return undefined}function ig(e){const{config:n,markDef:t}=e;const i=ii("invalid",t,n);if(i){const n=rg(e,{channels:Un});if(n){return{defined:{signal:n}}}}return{}}function rg(e,{invalid:n=false,channels:t}){const i=t.reduce(((n,t)=>{var i;const r=e.getScaleComponent(t);if(r){const s=r.get("type");const o=e.vgField(t,{expr:"datum",binSuffix:((i=e.stack)===null||i===void 0?void 0:i.impute)?"mid":undefined});if(o&&ho(s)){n[o]=true}}return n}),{});const r=N(i);if(r.length>0){const e=n?"||":"&&";return r.map((e=>Fa(e,n))).join(` ${e} `)}return undefined}function sg(e,n){if(n!==undefined){return{[e]:Xt(n)}}return undefined}const og="voronoi";const ag={defined:e=>e.type==="point"&&e.nearest,parse:(e,n)=>{if(n.events){for(const t of n.events){t.markname=e.getName(og)}}},marks:(e,n,t)=>{const{x:i,y:r}=n.project.hasChannel;const s=e.mark;if(ea(s)){Vr(vi(s));return t}const o={name:e.getName(og),type:"path",interactive:true,from:{data:e.getName("marks")},encode:{update:Object.assign({fill:{value:"transparent"},strokeWidth:{value:.35},stroke:{value:"transparent"},isVoronoi:{value:true}},$p(e,{reactiveGeom:true}))},transform:[{type:"voronoi",x:{expr:i||!r?"datum.datum.x || 0":"0"},y:{expr:r||!i?"datum.datum.y || 0":"0"},size:[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]}]};let a=0;let u=false;t.forEach(((n,t)=>{var i;const r=(i=n.name)!==null&&i!==void 0?i:"";if(r===e.component.mark[0].name){a=t}else if(r.indexOf(og)>=0){u=true}}));if(!u){t.splice(a+1,0,o)}return t}};const ug=ag;const cg={defined:e=>e.type==="point"&&e.resolve==="global"&&e.bind&&e.bind!=="scales"&&!wl(e.bind),parse:(e,n,t)=>Mg(n,t),topLevelSignals:(e,n,t)=>{const i=n.name;const s=n.project;const o=n.bind;const a=n.init&&n.init[0];const u=ug.defined(n)?"(item().isVoronoi ? datum.datum : datum)":"datum";s.items.forEach(((e,s)=>{var c,l;const f=q(`${i}_${e.field}`);const d=t.filter((e=>e.name===f));if(!d.length){t.unshift(Object.assign(Object.assign({name:f},a?{init:Hd(a[s])}:{value:null}),{on:n.events?[{events:n.events,update:`datum && item().mark.marktype !== 'group' ? ${u}[${(0,r.r$)(e.field)}] : null`}]:[],bind:(l=(c=o[e.field])!==null&&c!==void 0?c:o[e.channel])!==null&&l!==void 0?l:o}))}}));return t},signals:(e,n,t)=>{const i=n.name;const r=n.project;const s=t.filter((e=>e.name===i+Sg))[0];const o=i+sp;const a=r.items.map((e=>q(`${i}_${e.field}`)));const u=a.map((e=>`${e} !== null`)).join(" && ");if(a.length){s.update=`${u} ? {fields: ${o}, values: [${a.join(", ")}]} : null`}delete s.value;delete s.on;return t}};const lg=cg;const fg="_toggle";const dg={defined:e=>e.type==="point"&&!!e.toggle,signals:(e,n,t)=>t.concat({name:n.name+fg,value:false,on:[{events:n.events,update:n.toggle}]}),modifyExpr:(e,n)=>{const t=n.name+Sg;const i=n.name+fg;return`${i} ? null : ${t}, `+(n.resolve==="global"?`${i} ? null : true, `:`${i} ? null : {unit: ${Ng(e)}}, `)+`${i} ? ${t} : null`}};const pg=dg;const gg={defined:e=>e.clear!==undefined&&e.clear!==false,parse:(e,n)=>{if(n.clear){n.clear=(0,r.Kg)(n.clear)?(0,Id.P)(n.clear,"view"):n.clear}},topLevelSignals:(e,n,t)=>{if(lg.defined(n)){for(const e of n.project.items){const i=t.findIndex((t=>t.name===q(`${n.name}_${e.field}`)));if(i!==-1){t[i].on.push({events:n.clear,update:"null"})}}}return t},signals:(e,n,t)=>{function i(e,i){if(e!==-1&&t[e].on){t[e].on.push({events:n.clear,update:i})}}if(n.type==="interval"){for(const e of n.project.items){const n=t.findIndex((n=>n.name===e.signals.visual));i(n,"[0, 0]");if(n===-1){const n=t.findIndex((n=>n.name===e.signals.data));i(n,"null")}}}else{let e=t.findIndex((e=>e.name===n.name+Sg));i(e,"null");if(pg.defined(n)){e=t.findIndex((e=>e.name===n.name+fg));i(e,"false")}}return t}};const mg=gg;const hg={defined:e=>{const n=e.resolve==="global"&&e.bind&&wl(e.bind);const t=e.project.items.length===1&&e.project.items[0].field!==Ol;if(n&&!t){Vr(ji)}return n&&t},parse:(e,n,t)=>{var i;const s=b(t);s.select=(0,r.Kg)(s.select)?{type:s.select,toggle:n.toggle}:Object.assign(Object.assign({},s.select),{toggle:n.toggle});Mg(n,s);if((0,Rs.isObject)(t.select)&&(t.select.on||t.select.clear)){const e='event.item && indexof(event.item.mark.role, "legend") < 0';for(const t of n.events){t.filter=(0,r.YO)((i=t.filter)!==null&&i!==void 0?i:[]);if(!t.filter.includes(e)){t.filter.push(e)}}}const o=jl(n.bind)?n.bind.legend:"click";const a=(0,r.Kg)(o)?(0,Id.P)(o,"view"):(0,r.YO)(o);n.bind={legend:{merge:a}}},topLevelSignals:(e,n,t)=>{const i=n.name;const r=jl(n.bind)&&n.bind.legend;const s=e=>n=>{const t=b(n);t.markname=e;return t};for(const o of n.project.items){if(!o.hasLegend)continue;const e=`${q(o.field)}_legend`;const a=`${i}_${e}`;const u=t.filter((e=>e.name===a));if(u.length===0){const i=r.merge.map(s(`${e}_symbols`)).concat(r.merge.map(s(`${e}_labels`))).concat(r.merge.map(s(`${e}_entries`)));t.unshift(Object.assign(Object.assign({name:a},!n.init?{value:null}:{}),{on:[{events:i,update:"datum.value || item().items[0].items[0].datum.value",force:true},{events:r.merge,update:`!event.item || !datum ? null : ${a}`,force:true}]}))}}return t},signals:(e,n,t)=>{const i=n.name;const r=n.project;const s=t.find((e=>e.name===i+Sg));const o=i+sp;const a=r.items.filter((e=>e.hasLegend)).map((e=>q(`${i}_${q(e.field)}_legend`)));const u=a.map((e=>`${e} !== null`)).join(" && ");const c=`${u} ? {fields: ${o}, values: [${a.join(", ")}]} : null`;if(n.events&&a.length>0){s.on.push({events:a.map((e=>({signal:e}))),update:c})}else if(a.length>0){s.update=c;delete s.value;delete s.on}const l=t.find((e=>e.name===i+fg));const f=jl(n.bind)&&n.bind.legend;if(l){if(!n.events)l.on[0].events=f;else l.on.push(Object.assign(Object.assign({},l.on[0]),{events:f}))}return t}};const bg=hg;function yg(e,n,t){var i,r,s,o;const a=(i=e.fieldDef(n))===null||i===void 0?void 0:i.field;for(const u of T((r=e.component.selection)!==null&&r!==void 0?r:{})){const e=(s=u.project.hasField[a])!==null&&s!==void 0?s:u.project.hasChannel[n];if(e&&hg.defined(u)){const n=(o=t.get("selections"))!==null&&o!==void 0?o:[];n.push(u.name);t.set("selections",n,false);e.hasLegend=true}}}const vg="_translate_anchor";const Og="_translate_delta";const xg={defined:e=>e.type==="interval"&&e.translate,signals:(e,n,t)=>{const i=n.name;const r=lp.defined(n);const s=i+vg;const{x:o,y:a}=n.project.hasChannel;let u=(0,Id.P)(n.translate,"scope");if(!r){u=u.map((e=>(e.between[0].markname=i+gp,e)))}t.push({name:s,value:{},on:[{events:u.map((e=>e.between[0])),update:"{x: x(unit), y: y(unit)"+(o!==undefined?`, extent_x: ${r?fp(e,ce):`slice(${o.signals.visual})`}`:"")+(a!==undefined?`, extent_y: ${r?fp(e,le):`slice(${a.signals.visual})`}`:"")+"}"}]},{name:i+Og,value:{},on:[{events:u,update:`{x: ${s}.x - x(unit), y: ${s}.y - y(unit)}`}]});if(o!==undefined){jg(e,n,o,"width",t)}if(a!==undefined){jg(e,n,a,"height",t)}return t}};const wg=xg;function jg(e,n,t,i,r){var s,o;const a=n.name;const u=a+vg;const c=a+Og;const l=t.channel;const f=lp.defined(n);const d=r.filter((e=>e.name===t.signals[f?"data":"visual"]))[0];const p=e.getSizeSignalRef(i).signal;const g=e.getScaleComponent(l);const m=g.get("type");const h=g.get("reverse");const b=!f?"":l===ce?h?"":"-":h?"-":"";const y=`${u}.extent_${l}`;const v=`${b}${c}.${l} / ${f?`${p}`:`span(${y})`}`;const O=!f?"panLinear":m==="log"?"panLog":m==="symlog"?"panSymlog":m==="pow"?"panPow":"panLinear";const x=!f?"":m==="pow"?`, ${(s=g.get("exponent"))!==null&&s!==void 0?s:1}`:m==="symlog"?`, ${(o=g.get("constant"))!==null&&o!==void 0?o:1}`:"";const w=`${O}(${y}, ${v}${x})`;d.on.push({events:{signal:c},update:f?w:`clampRange(${w}, 0, ${p})`})}const Fg="_zoom_anchor";const $g="_zoom_delta";const Dg={defined:e=>e.type==="interval"&&e.zoom,signals:(e,n,t)=>{const i=n.name;const s=lp.defined(n);const o=i+$g;const{x:a,y:u}=n.project.hasChannel;const c=(0,r.r$)(e.scaleName(ce));const l=(0,r.r$)(e.scaleName(le));let f=(0,Id.P)(n.zoom,"scope");if(!s){f=f.map((e=>(e.markname=i+gp,e)))}t.push({name:i+Fg,on:[{events:f,update:!s?`{x: x(unit), y: y(unit)}`:"{"+[c?`x: invert(${c}, x(unit))`:"",l?`y: invert(${l}, y(unit))`:""].filter((e=>!!e)).join(", ")+"}"}]},{name:o,on:[{events:f,force:true,update:"pow(1.001, event.deltaY * pow(16, event.deltaMode))"}]});if(a!==undefined){kg(e,n,a,"width",t)}if(u!==undefined){kg(e,n,u,"height",t)}return t}};const Ag=Dg;function kg(e,n,t,i,r){var s,o;const a=n.name;const u=t.channel;const c=lp.defined(n);const l=r.filter((e=>e.name===t.signals[c?"data":"visual"]))[0];const f=e.getSizeSignalRef(i).signal;const d=e.getScaleComponent(u);const p=d.get("type");const g=c?fp(e,u):l.name;const m=a+$g;const h=`${a}${Fg}.${u}`;const b=!c?"zoomLinear":p==="log"?"zoomLog":p==="symlog"?"zoomSymlog":p==="pow"?"zoomPow":"zoomLinear";const y=!c?"":p==="pow"?`, ${(s=d.get("exponent"))!==null&&s!==void 0?s:1}`:p==="symlog"?`, ${(o=d.get("constant"))!==null&&o!==void 0?o:1}`:"";const v=`${b}(${g}, ${h}, ${m}${y})`;l.on.push({events:{signal:m},update:c?v:`clampRange(${v}, 0, ${f})`})}const Cg="_store";const Sg="_tuple";const Eg="_modify";const Bg="_selection_domain_";const Pg="vlSelectionResolve";const _g=[xp,bp,up,pg,lg,lp,bg,mg,wg,Ag,ug];function zg(e){let n=e.parent;while(n){if(ex(n))break;n=n.parent}return n}function Ng(e,{escape:n}={escape:true}){let t=n?(0,r.r$)(e.name):e.name;const i=zg(e);if(i){const{facet:e}=i;for(const n of Je){if(e[n]){t+=` + '__facet_${n}_' + (facet[${(0,r.r$)(i.vgField(n))}])`}}}return t}function Tg(e){var n;return T((n=e.component.selection)!==null&&n!==void 0?n:{}).reduce(((e,n)=>e||n.project.hasSelectionId),false)}function Mg(e,n){if((0,Rs.isString)(n.select)||!n.select.on)delete e.events;if((0,Rs.isString)(n.select)||!n.select.clear)delete e.clear;if((0,Rs.isString)(n.select)||!n.select.toggle)delete e.toggle}const Lg="RawCode";const qg="Literal";const Ug="Property";const Rg="Identifier";const Ig="ArrayExpression";const Wg="BinaryExpression";const Hg="CallExpression";const Gg="ConditionalExpression";const Yg="LogicalExpression";const Kg="MemberExpression";const Vg="ObjectExpression";const Qg="UnaryExpression";function Xg(e){this.type=e}Xg.prototype.visit=function(e){let n,t,i;if(e(this))return 1;for(n=Jg(this),t=0,i=n.length;t<i;++t){if(n[t].visit(e))return 1}};function Jg(e){switch(e.type){case Ig:return e.elements;case Wg:case Yg:return[e.left,e.right];case Hg:return[e.callee].concat(e.arguments);case Gg:return[e.test,e.consequent,e.alternate];case Kg:return[e.object,e.property];case Vg:return e.properties;case Ug:return[e.key,e.value];case Qg:return[e.argument];case Rg:case qg:case Lg:default:return[]}}var Zg,em,nm,tm,im;var rm=1,sm=2,om=3,am=4,um=5,cm=6,lm=7,fm=8,dm=9;Zg={};Zg[rm]="Boolean";Zg[sm]="<end>";Zg[om]="Identifier";Zg[am]="Keyword";Zg[um]="Null";Zg[cm]="Numeric";Zg[lm]="Punctuator";Zg[fm]="String";Zg[dm]="RegularExpression";var pm="ArrayExpression",gm="BinaryExpression",mm="CallExpression",hm="ConditionalExpression",bm="Identifier",ym="Literal",vm="LogicalExpression",Om="MemberExpression",xm="ObjectExpression",wm="Property",jm="UnaryExpression";var Fm="Unexpected token %0",$m="Unexpected number",Dm="Unexpected string",Am="Unexpected identifier",km="Unexpected reserved word",Cm="Unexpected end of input",Sm="Invalid regular expression",Em="Invalid regular expression: missing /",Bm="Octal literals are not allowed in strict mode.",Pm="Duplicate data property in object literal not allowed in strict mode";var _m="ILLEGAL",zm="Disabled.";var Nm=new RegExp("[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0-\\u08B2\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58\\u0C59\\u0C60\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D60\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19C1-\\u19C7\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA7AD\\uA7B0\\uA7B1\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB5F\\uAB64\\uAB65\\uABC0-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]"),Tm=new RegExp("[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u08A0-\\u08B2\\u08E4-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58\\u0C59\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C81-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D01-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D57\\u0D60-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19D9\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1CD0-\\u1CD2\\u1CD4-\\u1CF6\\u1CF8\\u1CF9\\u1D00-\\u1DF5\\u1DFC-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u200C\\u200D\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u2E2F\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA69D\\uA69F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA7AD\\uA7B0\\uA7B1\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C4\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB5F\\uAB64\\uAB65\\uABC0-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2D\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]");function Mm(e,n){if(!e){throw new Error("ASSERT: "+n)}}function Lm(e){return e>=48&&e<=57}function qm(e){return"0123456789abcdefABCDEF".indexOf(e)>=0}function Um(e){return"01234567".indexOf(e)>=0}function Rm(e){return e===32||e===9||e===11||e===12||e===160||e>=5760&&[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].indexOf(e)>=0}function Im(e){return e===10||e===13||e===8232||e===8233}function Wm(e){return e===36||e===95||e>=65&&e<=90||e>=97&&e<=122||e===92||e>=128&&Nm.test(String.fromCharCode(e))}function Hm(e){return e===36||e===95||e>=65&&e<=90||e>=97&&e<=122||e>=48&&e<=57||e===92||e>=128&&Tm.test(String.fromCharCode(e))}const Gm={if:1,in:1,do:1,var:1,for:1,new:1,try:1,let:1,this:1,else:1,case:1,void:1,with:1,enum:1,while:1,break:1,catch:1,throw:1,const:1,yield:1,class:1,super:1,return:1,typeof:1,delete:1,switch:1,export:1,import:1,public:1,static:1,default:1,finally:1,extends:1,package:1,private:1,function:1,continue:1,debugger:1,interface:1,protected:1,instanceof:1,implements:1};function Ym(){while(nm<tm){const e=em.charCodeAt(nm);if(Rm(e)||Im(e)){++nm}else{break}}}function Km(e){var n,t,i,r=0;t=e==="u"?4:2;for(n=0;n<t;++n){if(nm<tm&&qm(em[nm])){i=em[nm++];r=r*16+"0123456789abcdef".indexOf(i.toLowerCase())}else{wh({},Fm,_m)}}return String.fromCharCode(r)}function Vm(){var e,n,t,i;e=em[nm];n=0;if(e==="}"){wh({},Fm,_m)}while(nm<tm){e=em[nm++];if(!qm(e)){break}n=n*16+"0123456789abcdef".indexOf(e.toLowerCase())}if(n>1114111||e!=="}"){wh({},Fm,_m)}if(n<=65535){return String.fromCharCode(n)}t=(n-65536>>10)+55296;i=(n-65536&1023)+56320;return String.fromCharCode(t,i)}function Qm(){var e,n;e=em.charCodeAt(nm++);n=String.fromCharCode(e);if(e===92){if(em.charCodeAt(nm)!==117){wh({},Fm,_m)}++nm;e=Km("u");if(!e||e==="\\"||!Wm(e.charCodeAt(0))){wh({},Fm,_m)}n=e}while(nm<tm){e=em.charCodeAt(nm);if(!Hm(e)){break}++nm;n+=String.fromCharCode(e);if(e===92){n=n.substr(0,n.length-1);if(em.charCodeAt(nm)!==117){wh({},Fm,_m)}++nm;e=Km("u");if(!e||e==="\\"||!Hm(e.charCodeAt(0))){wh({},Fm,_m)}n+=e}}return n}function Xm(){var e,n;e=nm++;while(nm<tm){n=em.charCodeAt(nm);if(n===92){nm=e;return Qm()}if(Hm(n)){++nm}else{break}}return em.slice(e,nm)}function Jm(){var e,n,t;e=nm;n=em.charCodeAt(nm)===92?Qm():Xm();if(n.length===1){t=om}else if(Gm.hasOwnProperty(n)){t=am}else if(n==="null"){t=um}else if(n==="true"||n==="false"){t=rm}else{t=om}return{type:t,value:n,start:e,end:nm}}function Zm(){var e=nm,n=em.charCodeAt(nm),t,i=em[nm],r,s,o;switch(n){case 46:case 40:case 41:case 59:case 44:case 123:case 125:case 91:case 93:case 58:case 63:case 126:++nm;return{type:lm,value:String.fromCharCode(n),start:e,end:nm};default:t=em.charCodeAt(nm+1);if(t===61){switch(n){case 43:case 45:case 47:case 60:case 62:case 94:case 124:case 37:case 38:case 42:nm+=2;return{type:lm,value:String.fromCharCode(n)+String.fromCharCode(t),start:e,end:nm};case 33:case 61:nm+=2;if(em.charCodeAt(nm)===61){++nm}return{type:lm,value:em.slice(e,nm),start:e,end:nm}}}}o=em.substr(nm,4);if(o===">>>="){nm+=4;return{type:lm,value:o,start:e,end:nm}}s=o.substr(0,3);if(s===">>>"||s==="<<="||s===">>="){nm+=3;return{type:lm,value:s,start:e,end:nm}}r=s.substr(0,2);if(i===r[1]&&"+-<>&|".indexOf(i)>=0||r==="=>"){nm+=2;return{type:lm,value:r,start:e,end:nm}}if(r==="//"){wh({},Fm,_m)}if("<>=!+-*%&|^/".indexOf(i)>=0){++nm;return{type:lm,value:i,start:e,end:nm}}wh({},Fm,_m)}function eh(e){let n="";while(nm<tm){if(!qm(em[nm])){break}n+=em[nm++]}if(n.length===0){wh({},Fm,_m)}if(Wm(em.charCodeAt(nm))){wh({},Fm,_m)}return{type:cm,value:parseInt("0x"+n,16),start:e,end:nm}}function nh(e){let n="0"+em[nm++];while(nm<tm){if(!Um(em[nm])){break}n+=em[nm++]}if(Wm(em.charCodeAt(nm))||Lm(em.charCodeAt(nm))){wh({},Fm,_m)}return{type:cm,value:parseInt(n,8),octal:true,start:e,end:nm}}function th(){var e,n,t;t=em[nm];Mm(Lm(t.charCodeAt(0))||t===".","Numeric literal must start with a decimal digit or a decimal point");n=nm;e="";if(t!=="."){e=em[nm++];t=em[nm];if(e==="0"){if(t==="x"||t==="X"){++nm;return eh(n)}if(Um(t)){return nh(n)}if(t&&Lm(t.charCodeAt(0))){wh({},Fm,_m)}}while(Lm(em.charCodeAt(nm))){e+=em[nm++]}t=em[nm]}if(t==="."){e+=em[nm++];while(Lm(em.charCodeAt(nm))){e+=em[nm++]}t=em[nm]}if(t==="e"||t==="E"){e+=em[nm++];t=em[nm];if(t==="+"||t==="-"){e+=em[nm++]}if(Lm(em.charCodeAt(nm))){while(Lm(em.charCodeAt(nm))){e+=em[nm++]}}else{wh({},Fm,_m)}}if(Wm(em.charCodeAt(nm))){wh({},Fm,_m)}return{type:cm,value:parseFloat(e),start:n,end:nm}}function ih(){var e="",n,t,i,r,s=false;n=em[nm];Mm(n==="'"||n==='"',"String literal must starts with a quote");t=nm;++nm;while(nm<tm){i=em[nm++];if(i===n){n="";break}else if(i==="\\"){i=em[nm++];if(!i||!Im(i.charCodeAt(0))){switch(i){case"u":case"x":if(em[nm]==="{"){++nm;e+=Vm()}else{e+=Km(i)}break;case"n":e+="\n";break;case"r":e+="\r";break;case"t":e+="\t";break;case"b":e+="\b";break;case"f":e+="\f";break;case"v":e+="\v";break;default:if(Um(i)){r="01234567".indexOf(i);if(r!==0){s=true}if(nm<tm&&Um(em[nm])){s=true;r=r*8+"01234567".indexOf(em[nm++]);if("0123".indexOf(i)>=0&&nm<tm&&Um(em[nm])){r=r*8+"01234567".indexOf(em[nm++])}}e+=String.fromCharCode(r)}else{e+=i}break}}else{if(i==="\r"&&em[nm]==="\n"){++nm}}}else if(Im(i.charCodeAt(0))){break}else{e+=i}}if(n!==""){wh({},Fm,_m)}return{type:fm,value:e,octal:s,start:t,end:nm}}function rh(e,n){let t=e;if(n.indexOf("u")>=0){t=t.replace(/\\u\{([0-9a-fA-F]+)\}/g,((e,n)=>{if(parseInt(n,16)<=1114111){return"x"}wh({},Sm)})).replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"x")}try{new RegExp(t)}catch(i){wh({},Sm)}try{return new RegExp(e,n)}catch(r){return null}}function sh(){var e,n,t,i,r;e=em[nm];Mm(e==="/","Regular expression literal must start with a slash");n=em[nm++];t=false;i=false;while(nm<tm){e=em[nm++];n+=e;if(e==="\\"){e=em[nm++];if(Im(e.charCodeAt(0))){wh({},Em)}n+=e}else if(Im(e.charCodeAt(0))){wh({},Em)}else if(t){if(e==="]"){t=false}}else{if(e==="/"){i=true;break}else if(e==="["){t=true}}}if(!i){wh({},Em)}r=n.substr(1,n.length-2);return{value:r,literal:n}}function oh(){var e,n,t;n="";t="";while(nm<tm){e=em[nm];if(!Hm(e.charCodeAt(0))){break}++nm;if(e==="\\"&&nm<tm){wh({},Fm,_m)}else{t+=e;n+=e}}if(t.search(/[^gimuy]/g)>=0){wh({},Sm,t)}return{value:t,literal:n}}function ah(){var e,n,t,i;im=null;Ym();e=nm;n=sh();t=oh();i=rh(n.value,t.value);return{literal:n.literal+t.literal,value:i,regex:{pattern:n.value,flags:t.value},start:e,end:nm}}function uh(e){return e.type===om||e.type===am||e.type===rm||e.type===um}function ch(){Ym();if(nm>=tm){return{type:sm,start:nm,end:nm}}const e=em.charCodeAt(nm);if(Wm(e)){return Jm()}if(e===40||e===41||e===59){return Zm()}if(e===39||e===34){return ih()}if(e===46){if(Lm(em.charCodeAt(nm+1))){return th()}return Zm()}if(Lm(e)){return th()}return Zm()}function lh(){const e=im;nm=e.end;im=ch();nm=e.end;return e}function fh(){const e=nm;im=ch();nm=e}function dh(e){const n=new Xg(pm);n.elements=e;return n}function ph(e,n,t){const i=new Xg(e==="||"||e==="&&"?vm:gm);i.operator=e;i.left=n;i.right=t;return i}function gh(e,n){const t=new Xg(mm);t.callee=e;t.arguments=n;return t}function mh(e,n,t){const i=new Xg(hm);i.test=e;i.consequent=n;i.alternate=t;return i}function hh(e){const n=new Xg(bm);n.name=e;return n}function bh(e){const n=new Xg(ym);n.value=e.value;n.raw=em.slice(e.start,e.end);if(e.regex){if(n.raw==="//"){n.raw="/(?:)/"}n.regex=e.regex}return n}function yh(e,n,t){const i=new Xg(Om);i.computed=e==="[";i.object=n;i.property=t;if(!i.computed)t.member=true;return i}function vh(e){const n=new Xg(xm);n.properties=e;return n}function Oh(e,n,t){const i=new Xg(wm);i.key=n;i.value=t;i.kind=e;return i}function xh(e,n){const t=new Xg(jm);t.operator=e;t.argument=n;t.prefix=true;return t}function wh(e,n){var t,i=Array.prototype.slice.call(arguments,2),r=n.replace(/%(\d)/g,((e,n)=>{Mm(n<i.length,"Message reference must be in range");return i[n]}));t=new Error(r);t.index=nm;t.description=r;throw t}function jh(e){if(e.type===sm){wh(e,Cm)}if(e.type===cm){wh(e,$m)}if(e.type===fm){wh(e,Dm)}if(e.type===om){wh(e,Am)}if(e.type===am){wh(e,km)}wh(e,Fm,e.value)}function Fh(e){const n=lh();if(n.type!==lm||n.value!==e){jh(n)}}function $h(e){return im.type===lm&&im.value===e}function Dh(e){return im.type===am&&im.value===e}function Ah(){const e=[];nm=im.start;Fh("[");while(!$h("]")){if($h(",")){lh();e.push(null)}else{e.push(Ih());if(!$h("]")){Fh(",")}}}lh();return dh(e)}function kh(){nm=im.start;const e=lh();if(e.type===fm||e.type===cm){if(e.octal){wh(e,Bm)}return bh(e)}return hh(e.value)}function Ch(){var e,n,t,i;nm=im.start;e=im;if(e.type===om){t=kh();Fh(":");i=Ih();return Oh("init",t,i)}if(e.type===sm||e.type===lm){jh(e)}else{n=kh();Fh(":");i=Ih();return Oh("init",n,i)}}function Sh(){var e=[],n,t,i,r={},s=String;nm=im.start;Fh("{");while(!$h("}")){n=Ch();if(n.key.type===bm){t=n.key.name}else{t=s(n.key.value)}i="$"+t;if(Object.prototype.hasOwnProperty.call(r,i)){wh({},Pm)}else{r[i]=true}e.push(n);if(!$h("}")){Fh(",")}}Fh("}");return vh(e)}function Eh(){Fh("(");const e=Wh();Fh(")");return e}const Bh={if:1};function Ph(){var e,n,t;if($h("(")){return Eh()}if($h("[")){return Ah()}if($h("{")){return Sh()}e=im.type;nm=im.start;if(e===om||Bh[im.value]){t=hh(lh().value)}else if(e===fm||e===cm){if(im.octal){wh(im,Bm)}t=bh(lh())}else if(e===am){throw new Error(zm)}else if(e===rm){n=lh();n.value=n.value==="true";t=bh(n)}else if(e===um){n=lh();n.value=null;t=bh(n)}else if($h("/")||$h("/=")){t=bh(ah());fh()}else{jh(lh())}return t}function _h(){const e=[];Fh("(");if(!$h(")")){while(nm<tm){e.push(Ih());if($h(")")){break}Fh(",")}}Fh(")");return e}function zh(){nm=im.start;const e=lh();if(!uh(e)){jh(e)}return hh(e.value)}function Nh(){Fh(".");return zh()}function Th(){Fh("[");const e=Wh();Fh("]");return e}function Mh(){var e,n,t;e=Ph();for(;;){if($h(".")){t=Nh();e=yh(".",e,t)}else if($h("(")){n=_h();e=gh(e,n)}else if($h("[")){t=Th();e=yh("[",e,t)}else{break}}return e}function Lh(){const e=Mh();if(im.type===lm){if($h("++")||$h("--")){throw new Error(zm)}}return e}function qh(){var e,n;if(im.type!==lm&&im.type!==am){n=Lh()}else if($h("++")||$h("--")){throw new Error(zm)}else if($h("+")||$h("-")||$h("~")||$h("!")){e=lh();n=qh();n=xh(e.value,n)}else if(Dh("delete")||Dh("void")||Dh("typeof")){throw new Error(zm)}else{n=Lh()}return n}function Uh(e){let n=0;if(e.type!==lm&&e.type!==am){return 0}switch(e.value){case"||":n=1;break;case"&&":n=2;break;case"|":n=3;break;case"^":n=4;break;case"&":n=5;break;case"==":case"!=":case"===":case"!==":n=6;break;case"<":case">":case"<=":case">=":case"instanceof":case"in":n=7;break;case"<<":case">>":case">>>":n=8;break;case"+":case"-":n=9;break;case"*":case"/":case"%":n=11;break}return n}function Rh(){var e,n,t,i,r,s,o,a,u,c;e=im;u=qh();i=im;r=Uh(i);if(r===0){return u}i.prec=r;lh();n=[e,im];o=qh();s=[u,i,o];while((r=Uh(im))>0){while(s.length>2&&r<=s[s.length-2].prec){o=s.pop();a=s.pop().value;u=s.pop();n.pop();t=ph(a,u,o);s.push(t)}i=lh();i.prec=r;s.push(i);n.push(im);t=qh();s.push(t)}c=s.length-1;t=s[c];n.pop();while(c>1){n.pop();t=ph(s[c-1].value,s[c-2],t);c-=2}return t}function Ih(){var e,n,t;e=Rh();if($h("?")){lh();n=Ih();Fh(":");t=Ih();e=mh(e,n,t)}return e}function Wh(){const e=Ih();if($h(",")){throw new Error(zm)}return e}function Hh(e){em=e;nm=0;tm=em.length;im=null;fh();const n=Wh();if(im.type!==sm){throw new Error("Unexpect token after expression.")}return n}var Gh={NaN:"NaN",E:"Math.E",LN2:"Math.LN2",LN10:"Math.LN10",LOG2E:"Math.LOG2E",LOG10E:"Math.LOG10E",PI:"Math.PI",SQRT1_2:"Math.SQRT1_2",SQRT2:"Math.SQRT2",MIN_VALUE:"Number.MIN_VALUE",MAX_VALUE:"Number.MAX_VALUE"};function Yh(e){function n(n,t,i,r){let s=e(t[0]);if(i){s=i+"("+s+")";if(i.lastIndexOf("new ",0)===0)s="("+s+")"}return s+"."+n+(r<0?"":r===0?"()":"("+t.slice(1).map(e).join(",")+")")}function t(e,t,i){return r=>n(e,r,t,i)}const i="new Date",r="String",s="RegExp";return{isNaN:"Number.isNaN",isFinite:"Number.isFinite",abs:"Math.abs",acos:"Math.acos",asin:"Math.asin",atan:"Math.atan",atan2:"Math.atan2",ceil:"Math.ceil",cos:"Math.cos",exp:"Math.exp",floor:"Math.floor",log:"Math.log",max:"Math.max",min:"Math.min",pow:"Math.pow",random:"Math.random",round:"Math.round",sin:"Math.sin",sqrt:"Math.sqrt",tan:"Math.tan",clamp:function(n){if(n.length<3)error("Missing arguments to clamp function.");if(n.length>3)error("Too many arguments to clamp function.");const t=n.map(e);return"Math.max("+t[1]+", Math.min("+t[2]+","+t[0]+"))"},now:"Date.now",utc:"Date.UTC",datetime:i,date:t("getDate",i,0),day:t("getDay",i,0),year:t("getFullYear",i,0),month:t("getMonth",i,0),hours:t("getHours",i,0),minutes:t("getMinutes",i,0),seconds:t("getSeconds",i,0),milliseconds:t("getMilliseconds",i,0),time:t("getTime",i,0),timezoneoffset:t("getTimezoneOffset",i,0),utcdate:t("getUTCDate",i,0),utcday:t("getUTCDay",i,0),utcyear:t("getUTCFullYear",i,0),utcmonth:t("getUTCMonth",i,0),utchours:t("getUTCHours",i,0),utcminutes:t("getUTCMinutes",i,0),utcseconds:t("getUTCSeconds",i,0),utcmilliseconds:t("getUTCMilliseconds",i,0),length:t("length",null,-1),parseFloat:"parseFloat",parseInt:"parseInt",upper:t("toUpperCase",r,0),lower:t("toLowerCase",r,0),substring:t("substring",r),split:t("split",r),trim:t("trim",r,0),regexp:s,test:t("test",s),if:function(n){if(n.length<3)error("Missing arguments to if function.");if(n.length>3)error("Too many arguments to if function.");const t=n.map(e);return"("+t[0]+"?"+t[1]+":"+t[2]+")"}}}function Kh(e){const n=e&&e.length-1;return n&&(e[0]==='"'&&e[n]==='"'||e[0]==="'"&&e[n]==="'")?e.slice(1,-1):e}function Vh(e){e=e||{};const n=e.allowed?toSet(e.allowed):{},t=e.forbidden?toSet(e.forbidden):{},i=e.constants||Gh,r=(e.functions||Yh)(f),s=e.globalvar,o=e.fieldvar,a=isFunction(s)?s:e=>`${s}["${e}"]`;let u={},c={},l=0;function f(e){if(isString(e))return e;const n=d[e.type];if(n==null)error("Unsupported type: "+e.type);return n(e)}const d={Literal:e=>e.raw,Identifier:e=>{const r=e.name;if(l>0){return r}else if(hasOwnProperty(t,r)){return error("Illegal identifier: "+r)}else if(hasOwnProperty(i,r)){return i[r]}else if(hasOwnProperty(n,r)){return r}else{u[r]=1;return a(r)}},MemberExpression:e=>{const n=!e.computed,t=f(e.object);if(n)l+=1;const i=f(e.property);if(t===o){c[Kh(i)]=1}if(n)l-=1;return t+(n?"."+i:"["+i+"]")},CallExpression:e=>{if(e.callee.type!=="Identifier"){error("Illegal callee type: "+e.callee.type)}const n=e.callee.name,t=e.arguments,i=hasOwnProperty(r,n)&&r[n];if(!i)error("Unrecognized function: "+n);return isFunction(i)?i(t):i+"("+t.map(f).join(",")+")"},ArrayExpression:e=>"["+e.elements.map(f).join(",")+"]",BinaryExpression:e=>"("+f(e.left)+" "+e.operator+" "+f(e.right)+")",UnaryExpression:e=>"("+e.operator+f(e.argument)+")",ConditionalExpression:e=>"("+f(e.test)+"?"+f(e.consequent)+":"+f(e.alternate)+")",LogicalExpression:e=>"("+f(e.left)+e.operator+f(e.right)+")",ObjectExpression:e=>"{"+e.properties.map(f).join(",")+"}",Property:e=>{l+=1;const n=f(e.key);l-=1;return n+":"+f(e.value)}};function p(e){const n={code:f(e),globals:Object.keys(u),fields:Object.keys(c)};u={};c={};return n}p.functions=r;p.constants=i;return p}function Qh(e){const n=[];if(e.type==="Identifier"){return[e.name]}if(e.type==="Literal"){return[e.value]}if(e.type==="MemberExpression"){n.push(...Qh(e.object));n.push(...Qh(e.property))}return n}function Xh(e){if(e.object.type==="MemberExpression"){return Xh(e.object)}return e.object.name==="datum"}function Jh(e){const n=Hh(e);const t=new Set;n.visit((e=>{if(e.type==="MemberExpression"&&Xh(e)){t.add(Qh(e).slice(1).join("."))}}));return t}class Zh extends ep{clone(){return new Zh(null,this.model,b(this.filter))}constructor(e,n,t){super(e);this.model=n;this.filter=t;this.expr=rb(this.model,this.filter,this);this._dependentFields=Jh(this.expr)}dependentFields(){return this._dependentFields}producedFields(){return new Set}assemble(){return{type:"filter",expr:this.expr}}hash(){return`Filter ${this.expr}`}}function eb(e,n){var t;const i={};const s=e.config.selection;if(!n||!n.length)return i;for(const o of n){const n=q(o.name);const a=o.select;const u=(0,r.Kg)(a)?a:a.type;const c=(0,r.Gv)(a)?b(a):{type:u};const l=s[u];for(const e in l){if(e==="fields"||e==="encodings"){continue}if(e==="mark"){c[e]=Object.assign(Object.assign({},l[e]),c[e])}if(c[e]===undefined||c[e]===true){c[e]=(t=l[e])!==null&&t!==void 0?t:c[e]}}const f=i[n]=Object.assign(Object.assign({},c),{name:n,type:u,init:o.value,bind:o.bind,events:(0,r.Kg)(c.on)?(0,Id.P)(c.on,"scope"):(0,r.YO)(b(c.on))});for(const t of _g){if(t.defined(f)&&t.parse){t.parse(e,f,o)}}}return i}function nb(e,n,t,i="datum"){const s=(0,r.Kg)(n)?n:n.param;const o=q(s);const a=(0,r.r$)(o+Cg);let u;try{u=e.getSelectionComponent(o,s)}catch(p){return`!!${o}`}if(u.project.timeUnit){const n=t!==null&&t!==void 0?t:e.component.data.raw;const i=u.project.timeUnit.clone();if(n.parent){i.insertAsParentOf(n)}else{n.parent=i}}const c=u.project.hasSelectionId?"vlSelectionIdTest(":"vlSelectionTest(";const l=u.resolve==="global"?")":`, ${(0,r.r$)(u.resolve)})`;const f=`${c}${a}, ${i}${l}`;const d=`length(data(${a}))`;return n.empty===false?`${d} && ${f}`:`!${d} || ${f}`}function tb(e,n,t){const i=q(n);const s=t["encoding"];let o=t["field"];let a;try{a=e.getSelectionComponent(i,n)}catch(u){return i}if(!s&&!o){o=a.project.items[0].field;if(a.project.items.length>1){Vr('A "field" or "encoding" must be specified when using a selection as a scale domain. '+`Using "field": ${(0,r.r$)(o)}.`)}}else if(s&&!o){const e=a.project.items.filter((e=>e.channel===s));if(!e.length||e.length>1){o=a.project.items[0].field;Vr((!e.length?"No ":"Multiple ")+`matching ${(0,r.r$)(s)} encoding found for selection ${(0,r.r$)(t.param)}. `+`Using "field": ${(0,r.r$)(o)}.`)}else{o=e[0].field}}return`${a.name}[${(0,r.r$)(Y(o))}]`}function ib(e,n){var t;for(const[i,r]of M((t=e.component.selection)!==null&&t!==void 0?t:{})){const t=e.getName(`lookup_${i}`);e.component.data.outputNodes[t]=r.materialized=new np(new Zh(n,e,{param:i}),t,Rd.Lookup,e.component.data.outputNodeRefCounts)}}function rb(e,n,t){return U(n,(n=>{if((0,r.Kg)(n)){return n}else if(As(n)){return nb(e,n,t)}else{return Ls(n)}}))}var sb=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function ob(e,n){if(!e){return undefined}if((0,r.cy)(e)&&!Nt(e)){return e.map((e=>Mu(e,n))).join(", ")}return e}function ab(e,n,t,i){var r,s,o;var a,u;(r=e.encode)!==null&&r!==void 0?r:e.encode={};(s=(a=e.encode)[n])!==null&&s!==void 0?s:a[n]={};(o=(u=e.encode[n]).update)!==null&&o!==void 0?o:u.update={};e.encode[n].update[t]=i}function ub(e,n,t,i={header:false}){var s,o;const a=e.combine(),{disable:u,orient:c,scale:l,labelExpr:f,title:d,zindex:p}=a,g=sb(a,["disable","orient","scale","labelExpr","title","zindex"]);if(u){return undefined}for(const m in g){const e=rc[m];const t=g[m];if(e&&e!==n&&e!=="both"){delete g[m]}else if(tc(t)){const{condition:e}=t,n=sb(t,["condition"]);const i=(0,r.YO)(e);const s=nc[m];if(s){const{vgProp:e,part:t}=s;const r=[...i.map((e=>{const{test:n}=e,t=sb(e,["test"]);return Object.assign({test:rb(null,n)},t)})),n];ab(g,t,e,r);delete g[m]}else if(s===null){const e={signal:i.map((e=>{const{test:n}=e,t=sb(e,["test"]);return`${rb(null,n)} ? ${Zt(t)} : `})).join("")+Zt(n)};g[m]=e}}else if(Tt(t)){const e=nc[m];if(e){const{vgProp:n,part:i}=e;ab(g,i,n,t);delete g[m]}}if(F(["labelAlign","labelBaseline"],m)&&g[m]===null){delete g[m]}}if(n==="grid"){if(!g.grid){return undefined}if(g.encode){const{grid:e}=g.encode;g.encode=Object.assign({},e?{grid:e}:{});if(z(g.encode)){delete g.encode}}return Object.assign(Object.assign({scale:l,orient:c},g),{domain:false,labels:false,aria:false,maxExtent:0,minExtent:0,ticks:false,zindex:X(p,0)})}else{if(!i.header&&e.mainExtracted){return undefined}if(f!==undefined){let e=f;if(((o=(s=g.encode)===null||s===void 0?void 0:s.labels)===null||o===void 0?void 0:o.update)&&Tt(g.encode.labels.update.text)){e=K(f,"datum.label",g.encode.labels.update.text.signal)}ab(g,"labels","text",{signal:e})}if(g.labelAlign===null){delete g.labelAlign}if(g.encode){for(const n of ic){if(!e.hasAxisPart(n)){delete g.encode[n]}}if(z(g.encode)){delete g.encode}}const n=ob(d,t);return Object.assign(Object.assign(Object.assign(Object.assign({scale:l,orient:c,grid:false},n?{title:n}:{}),g),t.aria===false?{aria:false}:{}),{zindex:X(p,0)})}}function cb(e){const{axes:n}=e.component;const t=[];for(const i of Un){if(n[i]){for(const r of n[i]){if(!r.get("disable")&&!r.get("gridScale")){const n=i==="x"?"height":"width";const r=e.getSizeSignalRef(n).signal;if(n!==r){t.push({name:n,update:r})}}}}}return t}function lb(e,n){const{x:t=[],y:i=[]}=e;return[...t.map((e=>ub(e,"grid",n))),...i.map((e=>ub(e,"grid",n))),...t.map((e=>ub(e,"main",n))),...i.map((e=>ub(e,"main",n)))].filter((e=>e))}function fb(e,n,t,i){return Object.assign.apply(null,[{},...e.map((e=>{if(e==="axisOrient"){const e=t==="x"?"bottom":"left";const r=n[t==="x"?"axisBottom":"axisLeft"]||{};const s=n[t==="x"?"axisTop":"axisRight"]||{};const o=new Set([...N(r),...N(s)]);const a={};for(const n of o.values()){a[n]={signal:`${i["signal"]} === "${e}" ? ${ei(r[n])} : ${ei(s[n])}`}}return a}return n[e]}))])}function db(e,n,t,i){const r=n==="band"?["axisDiscrete","axisBand"]:n==="point"?["axisDiscrete","axisPoint"]:co(n)?["axisQuantitative"]:n==="time"||n==="utc"?["axisTemporal"]:[];const s=e==="x"?"axisX":"axisY";const o=Tt(t)?"axisOrient":`axis${I(t)}`;const a=[...r,...r.map((e=>s+e.substr(4)))];const u=["axis",o,s];return{vlOnlyAxisConfig:fb(a,i,e,t),vgAxisConfig:fb(u,i,e,t),axisConfigStyle:pb([...u,...a],i)}}function pb(e,n){var t;const i=[{}];for(const s of e){let e=(t=n[s])===null||t===void 0?void 0:t.style;if(e){e=(0,r.YO)(e);for(const t of e){i.push(n.style[t])}}}return Object.assign.apply(null,i)}function gb(e,n,t,i={}){var r;const s=oi(e,t,n);if(s!==undefined){return{configFrom:"style",configValue:s}}for(const o of["vlOnlyAxisConfig","vgAxisConfig","axisConfigStyle"]){if(((r=i[o])===null||r===void 0?void 0:r[e])!==undefined){return{configFrom:o,configValue:i[o][e]}}}return{}}const mb={scale:({model:e,channel:n})=>e.scaleName(n),format:({fieldOrDatumDef:e,config:n,axis:t})=>{const{format:i,formatType:r}=t;return Na(e,e.type,i,r,n,true)},formatType:({axis:e,fieldOrDatumDef:n,scaleType:t})=>{const{formatType:i}=e;return Ta(i,n,t)},grid:({fieldOrDatumDef:e,axis:n,scaleType:t})=>{var i;return(i=n.grid)!==null&&i!==void 0?i:hb(t,e)},gridScale:({model:e,channel:n})=>bb(e,n),labelAlign:({axis:e,labelAngle:n,orient:t,channel:i})=>e.labelAlign||xb(n,t,i),labelAngle:({labelAngle:e})=>e,labelBaseline:({axis:e,labelAngle:n,orient:t,channel:i})=>e.labelBaseline||Ob(n,t,i),labelFlush:({axis:e,fieldOrDatumDef:n,channel:t})=>{var i;return(i=e.labelFlush)!==null&&i!==void 0?i:wb(n.type,t)},labelOverlap:({axis:e,fieldOrDatumDef:n,scaleType:t})=>{var i;return(i=e.labelOverlap)!==null&&i!==void 0?i:jb(n.type,t,fu(n)&&!!n.timeUnit,fu(n)?n.sort:undefined)},orient:({orient:e})=>e,tickCount:({channel:e,model:n,axis:t,fieldOrDatumDef:i,scaleType:r})=>{var s;const o=e==="x"?"width":e==="y"?"height":undefined;const a=o?n.getSizeSignalRef(o):undefined;return(s=t.tickCount)!==null&&s!==void 0?s:$b({fieldOrDatumDef:i,scaleType:r,size:a,values:t.values})},title:({axis:e,model:n,channel:t})=>{if(e.title!==undefined){return e.title}const i=Db(n,t);if(i!==undefined){return i}const r=n.typedFieldDef(t);const s=t==="x"?"x2":"y2";const o=n.fieldDef(s);return ui(r?[tu(r)]:[],fu(o)?[tu(o)]:[])},values:({axis:e,fieldOrDatumDef:n})=>Ab(e,n),zindex:({axis:e,fieldOrDatumDef:n,mark:t})=>{var i;return(i=e.zindex)!==null&&i!==void 0?i:kb(t,n)}};function hb(e,n){return!mo(e)&&fu(n)&&!At(n===null||n===void 0?void 0:n.bin)&&!kt(n===null||n===void 0?void 0:n.bin)}function bb(e,n){const t=n==="x"?"y":"x";if(e.getScaleComponent(t)){return e.scaleName(t)}return undefined}function yb(e,n,t,i,r){const s=n===null||n===void 0?void 0:n.labelAngle;if(s!==undefined){return Tt(s)?s:ie(s)}else{const{configValue:s}=gb("labelAngle",i,n===null||n===void 0?void 0:n.style,r);if(s!==undefined){return ie(s)}else{if(t===ce&&F([Qs,Ks],e.type)&&!(fu(e)&&e.timeUnit)){return 270}return undefined}}}function vb(e){return`(((${e.signal} % 360) + 360) % 360)`}function Ob(e,n,t,i){if(e!==undefined){if(t==="x"){if(Tt(e)){const t=vb(e);const i=Tt(n)?`(${n.signal} === "top")`:n==="top";return{signal:`(45 < ${t} && ${t} < 135) || (225 < ${t} && ${t} < 315) ? "middle" :`+`(${t} <= 45 || 315 <= ${t}) === ${i} ? "bottom" : "top"`}}if(45<e&&e<135||225<e&&e<315){return"middle"}if(Tt(n)){const t=e<=45||315<=e?"===":"!==";return{signal:`${n.signal} ${t} "top" ? "bottom" : "top"`}}return(e<=45||315<=e)===(n==="top")?"bottom":"top"}else{if(Tt(e)){const t=vb(e);const r=Tt(n)?`(${n.signal} === "left")`:n==="left";const s=i?'"middle"':"null";return{signal:`${t} <= 45 || 315 <= ${t} || (135 <= ${t} && ${t} <= 225) ? ${s} : (45 <= ${t} && ${t} <= 135) === ${r} ? "top" : "bottom"`}}if(e<=45||315<=e||135<=e&&e<=225){return i?"middle":null}if(Tt(n)){const t=45<=e&&e<=135?"===":"!==";return{signal:`${n.signal} ${t} "left" ? "top" : "bottom"`}}return(45<=e&&e<=135)===(n==="left")?"top":"bottom"}}return undefined}function xb(e,n,t){if(e===undefined){return undefined}const i=t==="x";const r=i?0:90;const s=i?"bottom":"left";if(Tt(e)){const t=vb(e);const o=Tt(n)?`(${n.signal} === "${s}")`:n===s;return{signal:`(${r?`(${t} + 90)`:t} % 180 === 0) ? ${i?null:'"center"'} :`+`(${r} < ${t} && ${t} < ${180+r}) === ${o} ? "left" : "right"`}}if((e+r)%180===0){return i?null:"center"}if(Tt(n)){const t=r<e&&e<180+r?"===":"!==";const i=`${n.signal} ${t} "${s}"`;return{signal:`${i} ? "left" : "right"`}}if((r<e&&e<180+r)===(n===s)){return"left"}return"right"}function wb(e,n){if(n==="x"&&F(["quantitative","temporal"],e)){return true}return undefined}function jb(e,n,t,i){if(t&&!(0,r.Gv)(i)||e!=="nominal"&&e!=="ordinal"){if(n==="log"||n==="symlog"){return"greedy"}return true}return undefined}function Fb(e){return e==="x"?"bottom":"left"}function $b({fieldOrDatumDef:e,scaleType:n,size:t,values:i}){var r;if(!i&&!mo(n)&&n!=="log"){if(fu(e)){if(At(e.bin)){return{signal:`ceil(${t.signal}/10)`}}if(e.timeUnit&&F(["month","hours","day","quarter"],(r=$s(e.timeUnit))===null||r===void 0?void 0:r.unit)){return undefined}}return{signal:`ceil(${t.signal}/40)`}}return undefined}function Db(e,n){const t=n==="x"?"x2":"y2";const i=e.fieldDef(n);const r=e.fieldDef(t);const s=i?i.title:undefined;const o=r?r.title:undefined;if(s&&o){return ci(s,o)}else if(s){return s}else if(o){return o}else if(s!==undefined){return s}else if(o!==undefined){return o}return undefined}function Ab(e,n){const t=e.values;if((0,r.cy)(t)){return Zu(n,t)}else if(Tt(t)){return t}return undefined}function kb(e,n){if(e==="rect"&&Au(n)){return 1}return 0}class Cb extends ep{clone(){return new Cb(null,b(this.transform))}constructor(e,n){super(e);this.transform=n;this._dependentFields=Jh(this.transform.calculate)}static parseAllForSortIndex(e,n){n.forEachFieldDef(((n,t)=>{if(!Ou(n)){return}if(Va(n.sort)){const{field:i,timeUnit:r}=n;const s=n.sort;const o=s.map(((e,n)=>`${Ls({field:i,timeUnit:r,equal:e})} ? ${n} : `)).join("")+s.length;e=new Cb(e,{calculate:o,as:Sb(n,t,{forAs:true})})}}));return e}producedFields(){return new Set([this.transform.as])}dependentFields(){return this._dependentFields}assemble(){return{type:"formula",expr:this.transform.calculate,as:this.transform.as}}hash(){return`Calculate ${w(this.transform)}`}}function Sb(e,n,t){return Du(e,Object.assign({prefix:n,suffix:"sort_index"},t!==null&&t!==void 0?t:{}))}function Eb(e,n){if(F(["top","bottom"],n)){return"column"}else if(F(["left","right"],n)){return"row"}return e==="row"?"row":"column"}function Bb(e,n,t,i){const r=i==="row"?t.headerRow:i==="column"?t.headerColumn:t.headerFacet;return X((n||{})[e],r[e],t.header[e])}function Pb(e,n,t,i){const r={};for(const s of e){const e=Bb(s,n||{},t,i);if(e!==undefined){r[s]=e}}return r}const _b=["row","column"];const zb=["header","footer"];function Nb(e,n){const t=e.component.layoutHeaders[n].title;const i=e.config?e.config:undefined;const r=e.component.layoutHeaders[n].facetFieldDef?e.component.layoutHeaders[n].facetFieldDef:undefined;const{titleAnchor:s,titleAngle:o,titleOrient:a}=Pb(["titleAnchor","titleAngle","titleOrient"],r.header,i,n);const u=Eb(n,a);const c=ie(o);return{name:`${n}-title`,type:"group",role:`${u}-title`,title:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({text:t},n==="row"?{orient:"left"}:{}),{style:"guide-title"}),Mb(c,u)),Tb(u,c,s)),Gb(i,r,n,dl,ll))}}function Tb(e,n,t="middle"){switch(t){case"start":return{align:"left"};case"end":return{align:"right"}}const i=xb(n,e==="row"?"left":"top",e==="row"?"y":"x");return i?{align:i}:{}}function Mb(e,n){const t=Ob(e,n==="row"?"left":"top",n==="row"?"y":"x",true);return t?{baseline:t}:{}}function Lb(e,n){const t=e.component.layoutHeaders[n];const i=[];for(const r of zb){if(t[r]){for(const s of t[r]){const o=Rb(e,n,r,t,s);if(o!=null){i.push(o)}}}}return i}function qb(e,n){var t;const{sort:i}=e;if(Ka(i)){return{field:Du(i,{expr:"datum"}),order:(t=i.order)!==null&&t!==void 0?t:"ascending"}}else if((0,r.cy)(i)){return{field:Sb(e,n,{expr:"datum"}),order:"ascending"}}else{return{field:Du(e,{expr:"datum"}),order:i!==null&&i!==void 0?i:"ascending"}}}function Ub(e,n,t){const{format:i,formatType:r,labelAngle:s,labelAnchor:o,labelOrient:a,labelExpr:u}=Pb(["format","formatType","labelAngle","labelAnchor","labelOrient","labelExpr"],e.header,t,n);const c=Pa({fieldOrDatumDef:e,format:i,formatType:r,expr:"parent",config:t}).signal;const l=Eb(n,a);return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({text:{signal:u?K(K(u,"datum.label",c),"datum.value",Du(e,{expr:"parent"})):c}},n==="row"?{orient:"left"}:{}),{style:"guide-label",frame:"group"}),Mb(s,l)),Tb(l,s,o)),Gb(t,e,n,pl,fl))}function Rb(e,n,t,i,r){if(r){let s=null;const{facetFieldDef:o}=i;const a=e.config?e.config:undefined;if(o&&r.labels){const{labelOrient:e}=Pb(["labelOrient"],o.header,a,n);if(n==="row"&&!F(["top","bottom"],e)||n==="column"&&!F(["left","right"],e)){s=Ub(o,n,a)}}const u=ex(e)&&!Qa(e.facet);const c=r.axes;const l=(c===null||c===void 0?void 0:c.length)>0;if(s||l){const a=n==="row"?"height":"width";return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({name:e.getName(`${n}_${t}`),type:"group",role:`${n}-${t}`},i.facetFieldDef?{from:{data:e.getName(`${n}_domain`)},sort:qb(o,n)}:{}),l&&u?{from:{data:e.getName(`facet_domain_${n}`)}}:{}),s?{title:s}:{}),r.sizeSignal?{encode:{update:{[a]:r.sizeSignal}}}:{}),l?{axes:c}:{})}}return null}const Ib={column:{start:0,end:1},row:{start:1,end:0}};function Wb(e,n){return Ib[n][e]}function Hb(e,n){const t={};for(const i of Je){const r=e[i];if(r===null||r===void 0?void 0:r.facetFieldDef){const{titleAnchor:e,titleOrient:s}=Pb(["titleAnchor","titleOrient"],r.facetFieldDef.header,n,i);const o=Eb(i,s);const a=Wb(e,o);if(a!==undefined){t[o]=a}}}return z(t)?undefined:t}function Gb(e,n,t,i,r){const s={};for(const o of i){if(!r[o]){continue}const i=Bb(o,n===null||n===void 0?void 0:n.header,e,t);if(i!==undefined){s[r[o]]=i}}return s}function Yb(e){return[...Kb(e,"width"),...Kb(e,"height"),...Kb(e,"childWidth"),...Kb(e,"childHeight")]}function Kb(e,n){const t=n==="width"?"x":"y";const i=e.component.layoutSize.get(n);if(!i||i==="merged"){return[]}const r=e.getSizeSignalRef(n).signal;if(i==="step"){const n=e.getScaleComponent(t);if(n){const i=n.get("type");const s=n.get("range");if(mo(i)&&Mt(s)){const i=e.scaleName(t);if(ex(e.parent)){const n=e.parent.component.resolve;if(n.scale[t]==="independent"){return[Vb(i,s)]}}return[Vb(i,s),{name:r,update:Qb(i,n,`domain('${i}').length`)}]}}throw new Error("layout size is step although width/height is not step.")}else if(i=="container"){const n=r.endsWith("width");const t=n?"containerSize()[0]":"containerSize()[1]";const i=Ll(e.config.view,n?"width":"height");const s=`isFinite(${t}) ? ${t} : ${i}`;return[{name:r,init:s,on:[{update:s,events:"window:resize"}]}]}else{return[{name:r,value:i}]}}function Vb(e,n){const t=`${e}_step`;if(Tt(n.step)){return{name:t,update:n.step.signal}}else{return{name:t,value:n.step}}}function Qb(e,n,t){const i=n.get("type");const r=n.get("padding");const s=X(n.get("paddingOuter"),r);let o=n.get("paddingInner");o=i==="band"?o!==undefined?o:r:1;return`bandspace(${t}, ${ei(o)}, ${ei(s)}) * ${e}_step`}function Xb(e){return e==="childWidth"?"width":e==="childHeight"?"height":e}function Jb(e,n){return N(e).reduce(((t,i)=>{const r=e[i];return Object.assign(Object.assign({},t),wp(n,r,i,(e=>Xt(e.value))))}),{})}function Zb(e,n){if(ex(n)){return e==="theta"?"independent":"shared"}else if(tx(n)){return"shared"}else if(nx(n)){return Rn(e)||e==="theta"||e==="radius"?"independent":"shared"}throw new Error("invalid model type for resolve")}function ey(e,n){const t=e.scale[n];const i=Rn(n)?"axis":"legend";if(t==="independent"){if(e[i][n]==="shared"){Vr(Or(n))}return"independent"}return e[i][n]||"shared"}const ny=Object.assign(Object.assign({},yl),{disable:1,labelExpr:1,selections:1,opacity:1,shape:1,stroke:1,fill:1,size:1,strokeWidth:1,strokeDash:1,encode:1});const ty=N(ny);class iy extends kd{}const ry={symbols:sy,gradient:oy,labels:ay,entries:uy};function sy(e,{fieldOrDatumDef:n,model:t,channel:i,legendCmpt:s,legendType:o}){var a,u,c,l,f,d,p,g;if(o!=="symbol"){return undefined}const{markDef:m,encoding:h,config:b,mark:y}=t;const v=m.filled&&y!=="trail";let O=Object.assign(Object.assign({},ni({},t,aa)),Bp(t,{filled:v}));const x=(a=s.get("symbolOpacity"))!==null&&a!==void 0?a:b.legend.symbolOpacity;const w=(u=s.get("symbolFillColor"))!==null&&u!==void 0?u:b.legend.symbolFillColor;const j=(c=s.get("symbolStrokeColor"))!==null&&c!==void 0?c:b.legend.symbolStrokeColor;const F=x===undefined?(l=cy(h.opacity))!==null&&l!==void 0?l:m.opacity:undefined;if(O.fill){if(i==="fill"||v&&i===je){delete O.fill}else{if(O.fill["field"]){if(w){delete O.fill}else{O.fill=Xt((f=b.legend.symbolBaseFillColor)!==null&&f!==void 0?f:"black");O.fillOpacity=Xt(F!==null&&F!==void 0?F:1)}}else if((0,r.cy)(O.fill)){const e=(g=(p=ly((d=h.fill)!==null&&d!==void 0?d:h.color))!==null&&p!==void 0?p:m.fill)!==null&&g!==void 0?g:v&&m.color;if(e){O.fill=Xt(e)}}}}if(O.stroke){if(i==="stroke"||!v&&i===je){delete O.stroke}else{if(O.stroke["field"]||j){delete O.stroke}else if((0,r.cy)(O.stroke)){const e=X(ly(h.stroke||h.color),m.stroke,v?m.color:undefined);if(e){O.stroke={value:e}}}}}if(i!==Ce){const e=fu(n)&&dy(t,s,n);if(e){O.opacity=[Object.assign({test:e},Xt(F!==null&&F!==void 0?F:1)),Xt(b.legend.unselectedOpacity)]}else if(F){O.opacity=Xt(F)}}O=Object.assign(Object.assign({},O),e);return z(O)?undefined:O}function oy(e,{model:n,legendType:t,legendCmpt:i}){var r;if(t!=="gradient"){return undefined}const{config:s,markDef:o,encoding:a}=n;let u={};const c=(r=i.get("gradientOpacity"))!==null&&r!==void 0?r:s.legend.gradientOpacity;const l=c===undefined?cy(a.opacity)||o.opacity:undefined;if(l){u.opacity=Xt(l)}u=Object.assign(Object.assign({},u),e);return z(u)?undefined:u}function ay(e,{fieldOrDatumDef:n,model:t,channel:i,legendCmpt:r}){const s=t.legend(i)||{};const o=t.config;const a=fu(n)?dy(t,r,n):undefined;const u=a?[{test:a,value:1},{value:o.legend.unselectedOpacity}]:undefined;const{format:c,formatType:l}=s;let f=undefined;if(Sa(l)){f=za({fieldOrDatumDef:n,field:"datum.value",format:c,formatType:l,config:o})}else if(c===undefined&&l===undefined&&o.customFormatTypes){if(n.type==="quantitative"&&o.numberFormatType){f=za({fieldOrDatumDef:n,field:"datum.value",format:o.numberFormat,formatType:o.numberFormatType,config:o})}else if(n.type==="temporal"&&o.timeFormatType&&fu(n)&&n.timeUnit===undefined){f=za({fieldOrDatumDef:n,field:"datum.value",format:o.timeFormat,formatType:o.timeFormatType,config:o})}}const d=Object.assign(Object.assign(Object.assign({},u?{opacity:u}:{}),f?{text:f}:{}),e);return z(d)?undefined:d}function uy(e,{legendCmpt:n}){const t=n.get("selections");return(t===null||t===void 0?void 0:t.length)?Object.assign(Object.assign({},e),{fill:{value:"transparent"}}):e}function cy(e){return fy(e,((e,n)=>Math.max(e,n.value)))}function ly(e){return fy(e,((e,n)=>X(e,n.value)))}function fy(e,n){if(lu(e)){return(0,r.YO)(e.condition).reduce(n,e.value)}else if(vu(e)){return e.value}return undefined}function dy(e,n,t){const i=n.get("selections");if(!(i===null||i===void 0?void 0:i.length))return undefined;const s=(0,r.r$)(t.field);return i.map((e=>{const n=(0,r.r$)(q(e)+Cg);return`(!length(data(${n})) || (${e}[${s}] && indexof(${e}[${s}], datum.value) >= 0))`})).join(" || ")}const py={direction:({direction:e})=>e,format:({fieldOrDatumDef:e,legend:n,config:t})=>{const{format:i,formatType:r}=n;return Na(e,e.type,i,r,t,false)},formatType:({legend:e,fieldOrDatumDef:n,scaleType:t})=>{const{formatType:i}=e;return Ta(i,n,t)},gradientLength:e=>{var n,t;const{legend:i,legendConfig:r}=e;return(t=(n=i.gradientLength)!==null&&n!==void 0?n:r.gradientLength)!==null&&t!==void 0?t:xy(e)},labelOverlap:({legend:e,legendConfig:n,scaleType:t})=>{var i,r;return(r=(i=e.labelOverlap)!==null&&i!==void 0?i:n.labelOverlap)!==null&&r!==void 0?r:jy(t)},symbolType:({legend:e,markDef:n,channel:t,encoding:i})=>{var r;return(r=e.symbolType)!==null&&r!==void 0?r:my(n.type,t,i.shape,n.shape)},title:({fieldOrDatumDef:e,config:n})=>Nu(e,n,{allowDisabling:true}),type:({legendType:e,scaleType:n,channel:t})=>{if(Qe(t)&&bo(n)){if(e==="gradient"){return undefined}}else if(e==="symbol"){return undefined}return e},values:({fieldOrDatumDef:e,legend:n})=>gy(n,e)};function gy(e,n){const t=e.values;if((0,r.cy)(t)){return Zu(n,t)}else if(Tt(t)){return t}return undefined}function my(e,n,t,i){var r;if(n!=="shape"){const e=(r=ly(t))!==null&&r!==void 0?r:i;if(e){return e}}switch(e){case"bar":case"rect":case"image":case"square":return"square";case"line":case"trail":case"rule":return"stroke";case"arc":case"point":case"circle":case"tick":case"geoshape":case"area":case"text":return"circle"}}function hy(e){if(e==="gradient"){return 20}return undefined}function by(e){const{legend:n}=e;return X(n.type,yy(e))}function yy({channel:e,timeUnit:n,scaleType:t}){if(Qe(e)){if(F(["quarter","month","day"],n)){return"symbol"}if(bo(t)){return"gradient"}}return"symbol"}function vy({legendConfig:e,legendType:n,orient:t,legend:i}){var r,s;return(s=(r=i.direction)!==null&&r!==void 0?r:e[n?"gradientDirection":"symbolDirection"])!==null&&s!==void 0?s:Oy(t,n)}function Oy(e,n){switch(e){case"top":case"bottom":return"horizontal";case"left":case"right":case"none":case undefined:return undefined;default:return n==="gradient"?"horizontal":undefined}}function xy({legendConfig:e,model:n,direction:t,orient:i,scaleType:r}){const{gradientHorizontalMaxLength:s,gradientHorizontalMinLength:o,gradientVerticalMaxLength:a,gradientVerticalMinLength:u}=e;if(bo(r)){if(t==="horizontal"){if(i==="top"||i==="bottom"){return wy(n,"width",o,s)}else{return o}}else{return wy(n,"height",u,a)}}return undefined}function wy(e,n,t,i){const r=e.getSizeSignalRef(n).signal;return{signal:`clamp(${r}, ${t}, ${i})`}}function jy(e){if(F(["quantile","threshold","log","symlog"],e)){return"greedy"}return undefined}function Fy(e){const n=ZO(e)?$y(e):Cy(e);e.component.legends=n;return n}function $y(e){const{encoding:n}=e;const t={};for(const i of[je,...hl]){const r=Ru(n[i]);if(!r||!e.getScaleComponent(i)){continue}if(i===De&&fu(r)&&r.type===Xs){continue}t[i]=ky(e,i)}return t}function Dy(e,n){const t=e.scaleName(n);if(e.mark==="trail"){if(n==="color"){return{stroke:t}}else if(n==="size"){return{strokeWidth:t}}}if(n==="color"){return e.markDef.filled?{fill:t}:{stroke:t}}return{[n]:t}}function Ay(e,n,t,i){switch(n){case"disable":return t!==undefined;case"values":return!!(t===null||t===void 0?void 0:t.values);case"title":if(n==="title"&&e===(i===null||i===void 0?void 0:i.title)){return true}}return e===(t||{})[n]}function ky(e,n){var t,i,r;let s=e.legend(n);const{markDef:o,encoding:a,config:u}=e;const c=u.legend;const l=new iy({},Dy(e,n));yg(e,n,l);const f=s!==undefined?!s:c.disable;l.set("disable",f,s!==undefined);if(f){return l}s=s||{};const d=e.getScaleComponent(n).get("type");const p=Ru(a[n]);const g=fu(p)?(t=$s(p.timeUnit))===null||t===void 0?void 0:t.unit:undefined;const m=s.orient||u.legend.orient||"right";const h=by({legend:s,channel:n,timeUnit:g,scaleType:d});const b=vy({legend:s,legendType:h,orient:m,legendConfig:c});const y={legend:s,channel:n,model:e,markDef:o,encoding:a,fieldOrDatumDef:p,legendConfig:c,config:u,scaleType:d,orient:m,legendType:h,direction:b};for(const j of ty){if(h==="gradient"&&j.startsWith("symbol")||h==="symbol"&&j.startsWith("gradient")){continue}const t=j in py?py[j](y):s[j];if(t!==undefined){const i=Ay(t,j,s,e.fieldDef(n));if(i||u.legend[j]===undefined){l.set(j,t,i)}}}const v=(i=s===null||s===void 0?void 0:s.encoding)!==null&&i!==void 0?i:{};const O=l.get("selections");const x={};const w={fieldOrDatumDef:p,model:e,channel:n,legendCmpt:l,legendType:h};for(const j of["labels","legend","title","symbols","gradient","entries"]){const n=Jb((r=v[j])!==null&&r!==void 0?r:{},e);const t=j in ry?ry[j](n,w):n;if(t!==undefined&&!z(t)){x[j]=Object.assign(Object.assign(Object.assign({},(O===null||O===void 0?void 0:O.length)&&fu(p)?{name:`${q(p.field)}_legend_${j}`}:{}),(O===null||O===void 0?void 0:O.length)?{interactive:!!O}:{}),{update:t})}}if(!z(x)){l.set("encode",x,!!(s===null||s===void 0?void 0:s.encoding))}return l}function Cy(e){const{legends:n,resolve:t}=e.component;for(const i of e.children){Fy(i);for(const r of N(i.component.legends)){t.legend[r]=ey(e.component.resolve,r);if(t.legend[r]==="shared"){n[r]=Sy(n[r],i.component.legends[r]);if(!n[r]){t.legend[r]="independent";delete n[r]}}}}for(const i of N(n)){for(const n of e.children){if(!n.component.legends[i]){continue}if(t.legend[i]==="shared"){delete n.component.legends[i]}}}return n}function Sy(e,n){var t,i,r,s;if(!e){return n.clone()}const o=e.getWithExplicit("orient");const a=n.getWithExplicit("orient");if(o.explicit&&a.explicit&&o.value!==a.value){return undefined}let u=false;for(const c of ty){const t=Pd(e.getWithExplicit(c),n.getWithExplicit(c),c,"legend",((e,n)=>{switch(c){case"symbolType":return Ey(e,n);case"title":return li(e,n);case"type":u=true;return Sd("symbol")}return Bd(e,n,c,"legend")}));e.setWithExplicit(c,t)}if(u){if((i=(t=e.implicit)===null||t===void 0?void 0:t.encode)===null||i===void 0?void 0:i.gradient){R(e.implicit,["encode","gradient"])}if((s=(r=e.explicit)===null||r===void 0?void 0:r.encode)===null||s===void 0?void 0:s.gradient){R(e.explicit,["encode","gradient"])}}return e}function Ey(e,n){if(n.value==="circle"){return n}return e}var By=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function Py(e,n,t,i){var r,s,o;var a,u;(r=e.encode)!==null&&r!==void 0?r:e.encode={};(s=(a=e.encode)[n])!==null&&s!==void 0?s:a[n]={};(o=(u=e.encode[n]).update)!==null&&o!==void 0?o:u.update={};e.encode[n].update[t]=i}function _y(e){const n=e.component.legends;const t={};for(const r of N(n)){const i=e.getScaleComponent(r);const s=x(i.get("domains"));if(t[s]){for(const e of t[s]){const i=Sy(e,n[r]);if(!i){t[s].push(n[r])}}}else{t[s]=[n[r].clone()]}}const i=T(t).flat().map((n=>zy(n,e.config))).filter((e=>e!==undefined));return i}function zy(e,n){var t,i,r;const s=e.combine(),{disable:o,labelExpr:a,selections:u}=s,c=By(s,["disable","labelExpr","selections"]);if(o){return undefined}if(n.aria===false&&c.aria==undefined){c.aria=false}if((t=c.encode)===null||t===void 0?void 0:t.symbols){const e=c.encode.symbols.update;if(e.fill&&e.fill["value"]!=="transparent"&&!e.stroke&&!c.stroke){e.stroke={value:"transparent"}}for(const n of hl){if(c[n]){delete e[n]}}}if(!c.title){delete c.title}if(a!==undefined){let e=a;if(((r=(i=c.encode)===null||i===void 0?void 0:i.labels)===null||r===void 0?void 0:r.update)&&Tt(c.encode.labels.update.text)){e=K(a,"datum.label",c.encode.labels.update.text.signal)}Py(c,"labels","text",{signal:e})}return c}function Ny(e){if(tx(e)||nx(e)){return Ty(e)}else{return My(e)}}function Ty(e){return e.children.reduce(((e,n)=>e.concat(n.assembleProjections())),My(e))}function My(e){const n=e.component.projection;if(!n||n.merged){return[]}const t=n.combine();const{name:i}=t;if(!n.data){return[Object.assign(Object.assign({name:i},{translate:{signal:"[width / 2, height / 2]"}}),t)]}else{const r={signal:`[${n.size.map((e=>e.signal)).join(", ")}]`};const s=n.data.reduce(((n,t)=>{const i=Tt(t)?t.signal:`data('${e.lookupDataSource(t)}')`;if(!F(n,i)){n.push(i)}return n}),[]);if(s.length<=0){throw new Error("Projection's fit didn't find any data sources")}return[Object.assign({name:i,size:r,fit:{signal:s.length>1?`[${s.join(", ")}]`:s[0]}},t)]}}const Ly=["type","clipAngle","clipExtent","center","rotate","precision","reflectX","reflectY","coefficient","distance","fraction","lobes","parallel","radius","ratio","spacing","tilt"];class qy extends kd{constructor(e,n,t,i){super(Object.assign({},n),{name:e});this.specifiedProjection=n;this.size=t;this.data=i;this.merged=false}get isFit(){return!!this.data}}function Uy(e){e.component.projection=ZO(e)?Ry(e):Hy(e)}function Ry(e){var n;if(e.hasProjection){const t=Pt(e.specifiedProjection);const i=!(t&&(t.scale!=null||t.translate!=null));const r=i?[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]:undefined;const s=i?Iy(e):undefined;const o=new qy(e.projectionName(true),Object.assign(Object.assign({},(n=Pt(e.config.projection))!==null&&n!==void 0?n:{}),t!==null&&t!==void 0?t:{}),r,s);if(!o.get("type")){o.set("type","equalEarth",false)}return o}return undefined}function Iy(e){const n=[];const{encoding:t}=e;for(const i of[[Oe,ve],[we,xe]]){if(Ru(t[i[0]])||Ru(t[i[1]])){n.push({signal:e.getName(`geojson_${n.length}`)})}}if(e.channelHasField(De)&&e.typedFieldDef(De).type===Xs){n.push({signal:e.getName(`geojson_${n.length}`)})}if(n.length===0){n.push(e.requestDataName(Rd.Main))}return n}function Wy(e,n){const t=D(Ly,(t=>{if(!(0,r.mQ)(e.explicit,t)&&!(0,r.mQ)(n.explicit,t)){return true}if((0,r.mQ)(e.explicit,t)&&(0,r.mQ)(n.explicit,t)&&h(e.get(t),n.get(t))){return true}return false}));const i=h(e.size,n.size);if(i){if(t){return e}else if(h(e.explicit,{})){return n}else if(h(n.explicit,{})){return e}}return null}function Hy(e){if(e.children.length===0){return undefined}let n;for(const i of e.children){Uy(i)}const t=D(e.children,(e=>{const t=e.component.projection;if(!t){return true}else if(!n){n=t;return true}else{const e=Wy(n,t);if(e){n=e}return!!e}}));if(n&&t){const t=e.projectionName(true);const i=new qy(t,n.specifiedProjection,n.size,b(n.data));for(const n of e.children){const e=n.component.projection;if(e){if(e.isFit){i.data.push(...n.component.projection.data)}n.renameProjection(e.get("name"),t);e.merged=true}}return i}return undefined}var Gy=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function Yy(e,n,t,i){var r,s;if(ec(n,t)){const o=ZO(e)?(s=(r=e.axis(t))!==null&&r!==void 0?r:e.legend(t))!==null&&s!==void 0?s:{}:{};const a=Du(n,{expr:"datum"});const u=Du(n,{expr:"datum",binSuffix:"end"});return{formulaAs:Du(n,{binSuffix:"range",forAs:true}),formula:Ra(a,u,o.format,o.formatType,i)}}return{}}function Ky(e,n){return`${Dt(e)}_${n}`}function Vy(e,n){return{signal:e.getName(`${n}_bins`),extentSignal:e.getName(`${n}_extent`)}}function Qy(e,n,t){var i;const r=(i=Yu(t,undefined))!==null&&i!==void 0?i:{};const s=Ky(r,n);return e.getName(`${s}_bins`)}function Xy(e){return"as"in e}function Jy(e,n,t){let i;let s;if(Xy(e)){i=(0,r.Kg)(e.as)?[e.as,`${e.as}_end`]:[e.as[0],e.as[1]]}else{i=[Du(e,{forAs:true}),Du(e,{binSuffix:"end",forAs:true})]}const o=Object.assign({},Yu(n,undefined));const a=Ky(o,e.field);const{signal:u,extentSignal:c}=Vy(t,a);if(St(o.extent)){const e=o.extent;s=tb(t,e.param,e);delete o.extent}const l=Object.assign(Object.assign(Object.assign({bin:o,field:e.field,as:[i]},u?{signal:u}:{}),c?{extentSignal:c}:{}),s?{span:s}:{});return{key:a,binComponent:l}}class Zy extends ep{clone(){return new Zy(null,b(this.bins))}constructor(e,n){super(e);this.bins=n}static makeFromEncoding(e,n){const t=n.reduceFieldDef(((e,t,i)=>{if(yu(t)&&At(t.bin)){const{key:r,binComponent:s}=Jy(t,t.bin,n);e[r]=Object.assign(Object.assign(Object.assign({},s),e[r]),Yy(n,t,i,n.config))}return e}),{});if(z(t)){return null}return new Zy(e,t)}static makeFromTransform(e,n,t){const{key:i,binComponent:r}=Jy(n,n.bin,t);return new Zy(e,{[i]:r})}merge(e,n){for(const t of N(e.bins)){if(t in this.bins){n(e.bins[t].signal,this.bins[t].signal);this.bins[t].as=C([...this.bins[t].as,...e.bins[t].as],w)}else{this.bins[t]=e.bins[t]}}for(const t of e.children){e.removeChild(t);t.parent=this}e.remove()}producedFields(){return new Set(T(this.bins).map((e=>e.as)).flat(2))}dependentFields(){return new Set(T(this.bins).map((e=>e.field)))}hash(){return`Bin ${w(this.bins)}`}assemble(){return T(this.bins).flatMap((e=>{const n=[];const[t,...i]=e.as;const r=e.bin,{extent:s}=r,o=Gy(r,["extent"]);const a=Object.assign(Object.assign(Object.assign({type:"bin",field:Y(e.field),as:t,signal:e.signal},!St(s)?{extent:s}:{extent:null}),e.span?{span:{signal:`span(${e.span})`}}:{}),o);if(!s&&e.extentSignal){n.push({type:"extent",field:Y(e.field),signal:e.extentSignal});a.extent={signal:e.extentSignal}}n.push(a);for(const u of i){for(let e=0;e<2;e++){n.push({type:"formula",expr:Du({field:t[e]},{expr:"datum"}),as:u[e]})}}if(e.formula){n.push({type:"formula",expr:e.formula,as:e.formulaAs})}return n}))}}function ev(e,n,t,i){var r;const s=ZO(i)?i.encoding[yn(n)]:undefined;if(yu(t)&&ZO(i)&&ou(t,s,i.markDef,i.config)){e.add(Du(t,{}));e.add(Du(t,{suffix:"end"}));if(t.bin&&ec(t,n)){e.add(Du(t,{binSuffix:"range"}))}}else if(Ye(n)){const t=Ge(n);e.add(i.getName(t))}else{e.add(Du(t))}if(Ou(t)&&jo((r=t.scale)===null||r===void 0?void 0:r.range)){e.add(t.scale.range.field)}return e}function nv(e,n){var t;for(const i of N(n)){const r=n[i];for(const n of N(r)){if(i in e){e[i][n]=new Set([...(t=e[i][n])!==null&&t!==void 0?t:[],...r[n]])}else{e[i]={[n]:r[n]}}}}}class tv extends ep{clone(){return new tv(null,new Set(this.dimensions),b(this.measures))}constructor(e,n,t){super(e);this.dimensions=n;this.measures=t}get groupBy(){return this.dimensions}static makeFromEncoding(e,n){let t=false;n.forEachFieldDef((e=>{if(e.aggregate){t=true}}));const i={};const r=new Set;if(!t){return null}n.forEachFieldDef(((e,t)=>{var s,o,a,u;const{aggregate:c,field:l}=e;if(c){if(c==="count"){(s=i["*"])!==null&&s!==void 0?s:i["*"]={};i["*"]["count"]=new Set([Du(e,{forAs:true})])}else{if(yt(c)||vt(c)){const e=yt(c)?"argmin":"argmax";const n=c[e];(o=i[n])!==null&&o!==void 0?o:i[n]={};i[n][e]=new Set([Du({op:e,field:n},{forAs:true})])}else{(a=i[l])!==null&&a!==void 0?a:i[l]={};i[l][c]=new Set([Du(e,{forAs:true})])}if(ct(t)&&n.scaleDomain(t)==="unaggregated"){(u=i[l])!==null&&u!==void 0?u:i[l]={};i[l]["min"]=new Set([Du({field:l,aggregate:"min"},{forAs:true})]);i[l]["max"]=new Set([Du({field:l,aggregate:"max"},{forAs:true})])}}}else{ev(r,t,e,n)}}));if(r.size+N(i).length===0){return null}return new tv(e,r,i)}static makeFromTransform(e,n){var t,i,r;const s=new Set;const o={};for(const a of n.aggregate){const{op:e,field:n,as:r}=a;if(e){if(e==="count"){(t=o["*"])!==null&&t!==void 0?t:o["*"]={};o["*"]["count"]=new Set([r?r:Du(a,{forAs:true})])}else{(i=o[n])!==null&&i!==void 0?i:o[n]={};o[n][e]=new Set([r?r:Du(a,{forAs:true})])}}}for(const a of(r=n.groupby)!==null&&r!==void 0?r:[]){s.add(a)}if(s.size+N(o).length===0){return null}return new tv(e,s,o)}merge(e){if(E(this.dimensions,e.dimensions)){nv(this.measures,e.measures);return true}Xr("different dimensions, cannot merge");return false}addDimensions(e){e.forEach(this.dimensions.add,this.dimensions)}dependentFields(){return new Set([...this.dimensions,...N(this.measures)])}producedFields(){const e=new Set;for(const n of N(this.measures)){for(const t of N(this.measures[n])){const i=this.measures[n][t];if(i.size===0){e.add(`${t}_${n}`)}else{i.forEach(e.add,e)}}}return e}hash(){return`Aggregate ${w({dimensions:this.dimensions,measures:this.measures})}`}assemble(){const e=[];const n=[];const t=[];for(const r of N(this.measures)){for(const i of N(this.measures[r])){for(const s of this.measures[r][i]){t.push(s);e.push(i);n.push(r==="*"?null:Y(r))}}}const i={type:"aggregate",groupby:[...this.dimensions].map(Y),ops:e,fields:n,as:t};return i}}class iv extends ep{constructor(e,n,t,i){super(e);this.model=n;this.name=t;this.data=i;for(const s of Je){const e=n.facet[s];if(e){const{bin:t,sort:i}=e;this[s]=Object.assign({name:n.getName(`${s}_domain`),fields:[Du(e),...At(t)?[Du(e,{binSuffix:"end"})]:[]]},Ka(i)?{sortField:i}:(0,r.cy)(i)?{sortIndexField:Sb(e,s)}:{})}}this.childModel=n.child}hash(){let e=`Facet`;for(const n of Je){if(this[n]){e+=` ${n.charAt(0)}:${w(this[n])}`}}return e}get fields(){var e;const n=[];for(const t of Je){if((e=this[t])===null||e===void 0?void 0:e.fields){n.push(...this[t].fields)}}return n}dependentFields(){const e=new Set(this.fields);for(const n of Je){if(this[n]){if(this[n].sortField){e.add(this[n].sortField.field)}if(this[n].sortIndexField){e.add(this[n].sortIndexField)}}}return e}producedFields(){return new Set}getSource(){return this.name}getChildIndependentFieldsWithStep(){const e={};for(const n of Un){const t=this.childModel.component.scales[n];if(t&&!t.merged){const i=t.get("type");const r=t.get("range");if(mo(i)&&Mt(r)){const t=cO(this.childModel,n);const i=uO(t);if(i){e[n]=i}else{Vr(hi(n))}}}}return e}assembleRowColumnHeaderData(e,n,t){const i={row:"y",column:"x",facet:undefined}[e];const r=[];const s=[];const o=[];if(i&&t&&t[i]){if(n){r.push(`distinct_${t[i]}`);s.push("max")}else{r.push(t[i]);s.push("distinct")}o.push(`distinct_${t[i]}`)}const{sortField:a,sortIndexField:u}=this[e];if(a){const{op:e=Wa,field:n}=a;r.push(n);s.push(e);o.push(Du(a,{forAs:true}))}else if(u){r.push(u);s.push("max");o.push(u)}return{name:this[e].name,source:n!==null&&n!==void 0?n:this.data,transform:[Object.assign({type:"aggregate",groupby:this[e].fields},r.length?{fields:r,ops:s,as:o}:{})]}}assembleFacetHeaderData(e){var n,t;const{columns:i}=this.model.layout;const{layoutHeaders:r}=this.model.component;const s=[];const o={};for(const c of _b){for(const e of zb){const i=(n=r[c]&&r[c][e])!==null&&n!==void 0?n:[];for(const e of i){if(((t=e.axes)===null||t===void 0?void 0:t.length)>0){o[c]=true;break}}}if(o[c]){const e=`length(data("${this.facet.name}"))`;const n=c==="row"?i?{signal:`ceil(${e} / ${i})`}:1:i?{signal:`min(${e}, ${i})`}:{signal:e};s.push({name:`${this.facet.name}_${c}`,transform:[{type:"sequence",start:0,stop:n}]})}}const{row:a,column:u}=o;if(a||u){s.unshift(this.assembleRowColumnHeaderData("facet",null,e))}return s}assemble(){var e,n;const t=[];let i=null;const r=this.getChildIndependentFieldsWithStep();const{column:s,row:o,facet:a}=this;if(s&&o&&(r.x||r.y)){i=`cross_${this.column.name}_${this.row.name}`;const s=[].concat((e=r.x)!==null&&e!==void 0?e:[],(n=r.y)!==null&&n!==void 0?n:[]);const o=s.map((()=>"distinct"));t.push({name:i,source:this.data,transform:[{type:"aggregate",groupby:this.fields,fields:s,ops:o}]})}for(const u of[ae,oe]){if(this[u]){t.push(this.assembleRowColumnHeaderData(u,i,r))}}if(a){const e=this.assembleFacetHeaderData(r);if(e){t.push(...e)}}return t}}function rv(e){if(e.startsWith("'")&&e.endsWith("'")||e.startsWith('"')&&e.endsWith('"')){return e.slice(1,-1)}return e}function sv(e,n){const t=W(e);if(n==="number"){return`toNumber(${t})`}else if(n==="boolean"){return`toBoolean(${t})`}else if(n==="string"){return`toString(${t})`}else if(n==="date"){return`toDate(${t})`}else if(n==="flatten"){return t}else if(n.startsWith("date:")){const e=rv(n.slice(5,n.length));return`timeParse(${t},'${e}')`}else if(n.startsWith("utc:")){const e=rv(n.slice(4,n.length));return`utcParse(${t},'${e}')`}else{Vr(Ei(n));return null}}function ov(e){const n={};g(e.filter,(e=>{var t;if(Ns(e)){let i=null;if(ks(e)){i=Vt(e.equal)}else if(Ss(e)){i=Vt(e.lte)}else if(Cs(e)){i=Vt(e.lt)}else if(Es(e)){i=Vt(e.gt)}else if(Bs(e)){i=Vt(e.gte)}else if(Ps(e)){i=e.range[0]}else if(_s(e)){i=((t=e.oneOf)!==null&&t!==void 0?t:e["in"])[0]}if(i){if(Jr(i)){n[e.field]="date"}else if((0,r.Et)(i)){n[e.field]="number"}else if((0,r.Kg)(i)){n[e.field]="string"}}if(e.timeUnit){n[e.field]="date"}}}));return n}function av(e){const n={};function t(e){if(Qu(e)){n[e.field]="date"}else if(e.type==="quantitative"&&jt(e.aggregate)){n[e.field]="number"}else if(Q(e.field)>1){if(!(e.field in n)){n[e.field]="flatten"}}else if(Ou(e)&&Ka(e.sort)&&Q(e.sort.field)>1){if(!(e.sort.field in n)){n[e.sort.field]="flatten"}}}if(ZO(e)||ex(e)){e.forEachFieldDef(((n,i)=>{if(yu(n)){t(n)}else{const r=hn(i);const s=e.fieldDef(r);t(Object.assign(Object.assign({},n),{type:s.type}))}}))}if(ZO(e)){const{mark:t,markDef:i,encoding:r}=e;if(ea(t)&&!e.encoding.order){const e=i.orient==="horizontal"?"y":"x";const t=r[e];if(fu(t)&&t.type==="quantitative"&&!(t.field in n)){n[t.field]="number"}}}return n}function uv(e){const n={};if(ZO(e)&&e.component.selection){for(const t of N(e.component.selection)){const i=e.component.selection[t];for(const e of i.project.items){if(!e.channel&&Q(e.field)>1){n[e.field]="flatten"}}}}return n}class cv extends ep{clone(){return new cv(null,b(this._parse))}constructor(e,n){super(e);this._parse=n}hash(){return`Parse ${w(this._parse)}`}static makeExplicit(e,n,t){var i;let r={};const s=n.data;if(!Md(s)&&((i=s===null||s===void 0?void 0:s.format)===null||i===void 0?void 0:i.parse)){r=s.format.parse}return this.makeWithAncestors(e,r,{},t)}static makeWithAncestors(e,n,t,i){for(const o of N(t)){const e=i.getWithExplicit(o);if(e.value!==undefined){if(e.explicit||e.value===t[o]||e.value==="derived"||t[o]==="flatten"){delete t[o]}else{Vr(Bi(o,t[o],e.value))}}}for(const o of N(n)){const e=i.get(o);if(e!==undefined){if(e===n[o]){delete n[o]}else{Vr(Bi(o,n[o],e))}}}const r=new kd(n,t);i.copyAll(r);const s={};for(const o of N(r.combine())){const e=r.get(o);if(e!==null){s[o]=e}}if(N(s).length===0||i.parseNothing){return null}return new cv(e,s)}get parse(){return this._parse}merge(e){this._parse=Object.assign(Object.assign({},this._parse),e.parse);e.remove()}assembleFormatParse(){const e={};for(const n of N(this._parse)){const t=this._parse[n];if(Q(n)===1){e[n]=t}}return e}producedFields(){return new Set(N(this._parse))}dependentFields(){return new Set(N(this._parse))}assembleTransforms(e=false){return N(this._parse).filter((n=>e?Q(n)>1:true)).map((e=>{const n=sv(e,this._parse[e]);if(!n){return null}const t={type:"formula",expr:n,as:V(e)};return t})).filter((e=>e!==null))}}class lv extends ep{clone(){return new lv(null)}constructor(e){super(e)}dependentFields(){return new Set}producedFields(){return new Set([Ol])}hash(){return"Identifier"}assemble(){return{type:"identifier",as:Ol}}}class fv extends ep{clone(){return new fv(null,this.params)}constructor(e,n){super(e);this.params=n}dependentFields(){return new Set}producedFields(){return undefined}hash(){return`Graticule ${w(this.params)}`}assemble(){return Object.assign({type:"graticule"},this.params===true?{}:this.params)}}class dv extends ep{clone(){return new dv(null,this.params)}constructor(e,n){super(e);this.params=n}dependentFields(){return new Set}producedFields(){var e;return new Set([(e=this.params.as)!==null&&e!==void 0?e:"data"])}hash(){return`Hash ${w(this.params)}`}assemble(){return Object.assign({type:"sequence"},this.params)}}class pv extends ep{constructor(e){super(null);e!==null&&e!==void 0?e:e={name:"source"};let n;if(!Md(e)){n=e.format?Object.assign({},O(e.format,["parse"])):{}}if(Nd(e)){this._data={values:e.values}}else if(zd(e)){this._data={url:e.url};if(!n.type){let t=/(?:\.([^.]+))?$/.exec(e.url)[1];if(!F(["json","csv","tsv","dsv","topojson"],t)){t="json"}n.type=t}}else if(qd(e)){this._data={values:[{type:"Sphere"}]}}else if(Td(e)||Md(e)){this._data={}}this._generator=Md(e);if(e.name){this._name=e.name}if(n&&!z(n)){this._data.format=n}}dependentFields(){return new Set}producedFields(){return undefined}get data(){return this._data}hasName(){return!!this._name}get isGenerator(){return this._generator}get dataName(){return this._name}set dataName(e){this._name=e}set parent(e){throw new Error("Source nodes have to be roots.")}remove(){throw new Error("Source nodes are roots and cannot be removed.")}hash(){throw new Error("Cannot hash sources")}assemble(){return Object.assign(Object.assign({name:this._name},this._data),{transform:[]})}}var gv=undefined&&undefined.__classPrivateFieldSet||function(e,n,t,i,r){if(i==="m")throw new TypeError("Private method is not writable");if(i==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof n==="function"?e!==n||!r:!n.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i==="a"?r.call(e,t):r?r.value=t:n.set(e,t),t};var mv=undefined&&undefined.__classPrivateFieldGet||function(e,n,t,i){if(t==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof n==="function"?e!==n||!i:!n.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?i:t==="a"?i.call(e):i?i.value:n.get(e)};var hv;function bv(e){return e instanceof pv||e instanceof fv||e instanceof dv}class yv{constructor(){hv.set(this,void 0);gv(this,hv,false,"f")}setModified(){gv(this,hv,true,"f")}get modifiedFlag(){return mv(this,hv,"f")}}hv=new WeakMap;class vv extends yv{getNodeDepths(e,n,t){t.set(e,n);for(const i of e.children){this.getNodeDepths(i,n+1,t)}return t}optimize(e){const n=this.getNodeDepths(e,0,new Map);const t=[...n.entries()].sort(((e,n)=>n[1]-e[1]));for(const i of t){this.run(i[0])}return this.modifiedFlag}}class Ov extends yv{optimize(e){this.run(e);for(const n of e.children){this.optimize(n)}return this.modifiedFlag}}class xv extends Ov{mergeNodes(e,n){const t=n.shift();for(const i of n){e.removeChild(i);i.parent=t;i.remove()}}run(e){const n=e.children.map((e=>e.hash()));const t={};for(let i=0;i<n.length;i++){if(t[n[i]]===undefined){t[n[i]]=[e.children[i]]}else{t[n[i]].push(e.children[i])}}for(const i of N(t)){if(t[i].length>1){this.setModified();this.mergeNodes(e,t[i])}}}}class wv extends Ov{constructor(e){super();this.requiresSelectionId=e&&Tg(e)}run(e){if(e instanceof lv){if(!(this.requiresSelectionId&&(bv(e.parent)||e.parent instanceof tv||e.parent instanceof cv))){this.setModified();e.remove()}}}}class jv extends yv{optimize(e){this.run(e,new Set);return this.modifiedFlag}run(e,n){let t=new Set;if(e instanceof ip){t=e.producedFields();if(B(t,n)){this.setModified();e.removeFormulas(n);if(e.producedFields.length===0){e.remove()}}}for(const i of e.children){this.run(i,new Set([...n,...t]))}}}class Fv extends Ov{constructor(){super()}run(e){if(e instanceof np&&!e.isRequired()){this.setModified();e.remove()}}}class $v extends vv{run(e){if(bv(e)){return}if(e.numChildren()>1){return}for(const n of e.children){if(n instanceof cv){if(e instanceof cv){this.setModified();e.merge(n)}else{if(_(e.producedFields(),n.dependentFields())){continue}this.setModified();n.swapWithParent()}}}return}}class Dv extends vv{run(e){const n=[...e.children];const t=e.children.filter((e=>e instanceof cv));if(e.numChildren()>1&&t.length>=1){const i={};const r=new Set;for(const e of t){const n=e.parse;for(const e of N(n)){if(!(e in i)){i[e]=n[e]}else if(i[e]!==n[e]){r.add(e)}}}for(const e of r){delete i[e]}if(!z(i)){this.setModified();const t=new cv(e,i);for(const r of n){if(r instanceof cv){for(const e of N(i)){delete r.parse[e]}}e.removeChild(r);r.parent=t;if(r instanceof cv&&N(r.parse).length===0){r.remove()}}}}}}class Av extends vv{run(e){if(e instanceof np||e.numChildren()>0||e instanceof iv){}else if(e instanceof pv){}else{this.setModified();e.remove()}}}class kv extends vv{run(e){const n=e.children.filter((e=>e instanceof ip));const t=n.pop();for(const i of n){this.setModified();t.merge(i)}}}class Cv extends vv{run(e){const n=e.children.filter((e=>e instanceof tv));const t={};for(const i of n){const e=w(i.groupBy);if(!(e in t)){t[e]=[]}t[e].push(i)}for(const i of N(t)){const n=t[i];if(n.length>1){const t=n.pop();for(const i of n){if(t.merge(i)){e.removeChild(i);i.parent=t;i.remove();this.setModified()}}}}}}class Sv extends vv{constructor(e){super();this.model=e}run(e){const n=!(bv(e)||e instanceof Zh||e instanceof cv||e instanceof lv);const t=[];const i=[];for(const r of e.children){if(r instanceof Zy){if(n&&!_(e.producedFields(),r.dependentFields())){t.push(r)}else{i.push(r)}}}if(t.length>0){const n=t.pop();for(const e of t){n.merge(e,this.model.renameSignal.bind(this.model))}this.setModified();if(e instanceof Zy){e.merge(n,this.model.renameSignal.bind(this.model))}else{n.swapWithParent()}}if(i.length>1){const e=i.pop();for(const n of i){e.merge(n,this.model.renameSignal.bind(this.model))}this.setModified()}}}class Ev extends vv{run(e){const n=[...e.children];const t=$(n,(e=>e instanceof np));if(!t||e.numChildren()<=1){return}const i=[];let r;for(const s of n){if(s instanceof np){let n=s;while(n.numChildren()===1){const[e]=n.children;if(e instanceof np){n=e}else{break}}i.push(...n.children);if(r){e.removeChild(s);s.parent=r.parent;r.parent.removeChild(r);r.parent=n;this.setModified()}else{r=n}}else{i.push(s)}}if(i.length){this.setModified();for(const e of i){e.parent.removeChild(e);e.parent=r}}}}class Bv extends ep{clone(){return new Bv(null,b(this.transform))}constructor(e,n){super(e);this.transform=n}addDimensions(e){this.transform.groupby=C(this.transform.groupby.concat(e),(e=>e))}dependentFields(){const e=new Set;if(this.transform.groupby){this.transform.groupby.forEach(e.add,e)}this.transform.joinaggregate.map((e=>e.field)).filter((e=>e!==undefined)).forEach(e.add,e);return e}producedFields(){return new Set(this.transform.joinaggregate.map(this.getDefaultName))}getDefaultName(e){var n;return(n=e.as)!==null&&n!==void 0?n:Du(e)}hash(){return`JoinAggregateTransform ${w(this.transform)}`}assemble(){const e=[];const n=[];const t=[];for(const r of this.transform.joinaggregate){n.push(r.op);t.push(this.getDefaultName(r));e.push(r.field===undefined?null:r.field)}const i=this.transform.groupby;return Object.assign({type:"joinaggregate",as:t,ops:n,fields:e},i!==undefined?{groupby:i}:{})}}function Pv(e){return e.stack.stackBy.reduce(((e,n)=>{const t=n.fieldDef;const i=Du(t);if(i){e.push(i)}return e}),[])}function _v(e){return(0,r.cy)(e)&&e.every((e=>(0,r.Kg)(e)))&&e.length>1}class zv extends ep{clone(){return new zv(null,b(this._stack))}constructor(e,n){super(e);this._stack=n}static makeFromTransform(e,n){const{stack:t,groupby:i,as:s,offset:o="zero"}=n;const a=[];const u=[];if(n.sort!==undefined){for(const e of n.sort){a.push(e.field);u.push(X(e.order,"ascending"))}}const c={field:a,order:u};let l;if(_v(s)){l=s}else if((0,r.Kg)(s)){l=[s,`${s}_end`]}else{l=[`${n.stack}_start`,`${n.stack}_end`]}return new zv(e,{dimensionFieldDefs:[],stackField:t,groupby:i,offset:o,sort:c,facetby:[],as:l})}static makeFromEncoding(e,n){const t=n.stack;const{encoding:i}=n;if(!t){return null}const{groupbyChannels:s,fieldChannel:o,offset:a,impute:u}=t;const c=s.map((e=>{const n=i[e];return Uu(n)})).filter((e=>!!e));const l=Pv(n);const f=n.encoding.order;let d;if((0,r.cy)(f)||fu(f)){d=ai(f)}else{d=l.reduce(((e,n)=>{e.field.push(n);e.order.push(o==="y"?"descending":"ascending");return e}),{field:[],order:[]})}return new zv(e,{dimensionFieldDefs:c,stackField:n.vgField(o),facetby:[],stackby:l,sort:d,offset:a,impute:u,as:[n.vgField(o,{suffix:"start",forAs:true}),n.vgField(o,{suffix:"end",forAs:true})]})}get stack(){return this._stack}addDimensions(e){this._stack.facetby.push(...e)}dependentFields(){const e=new Set;e.add(this._stack.stackField);this.getGroupbyFields().forEach(e.add,e);this._stack.facetby.forEach(e.add,e);this._stack.sort.field.forEach(e.add,e);return e}producedFields(){return new Set(this._stack.as)}hash(){return`Stack ${w(this._stack)}`}getGroupbyFields(){const{dimensionFieldDefs:e,impute:n,groupby:t}=this._stack;if(e.length>0){return e.map((e=>{if(e.bin){if(n){return[Du(e,{binSuffix:"mid"})]}return[Du(e,{}),Du(e,{binSuffix:"end"})]}return[Du(e)]})).flat()}return t!==null&&t!==void 0?t:[]}assemble(){const e=[];const{facetby:n,dimensionFieldDefs:t,stackField:i,stackby:r,sort:s,offset:o,impute:a,as:u}=this._stack;if(a){for(const s of t){const{bandPosition:t=.5,bin:o}=s;if(o){const n=Du(s,{expr:"datum"});const i=Du(s,{expr:"datum",binSuffix:"end"});e.push({type:"formula",expr:`${t}*${n}+${1-t}*${i}`,as:Du(s,{binSuffix:"mid",forAs:true})})}e.push({type:"impute",field:i,groupby:[...r,...n],key:Du(s,{binSuffix:"mid"}),method:"value",value:0})}}e.push({type:"stack",groupby:[...this.getGroupbyFields(),...n],field:i,sort:s,as:u,offset:o});return e}}class Nv extends ep{clone(){return new Nv(null,b(this.transform))}constructor(e,n){super(e);this.transform=n}addDimensions(e){this.transform.groupby=C(this.transform.groupby.concat(e),(e=>e))}dependentFields(){var e,n;const t=new Set;((e=this.transform.groupby)!==null&&e!==void 0?e:[]).forEach(t.add,t);((n=this.transform.sort)!==null&&n!==void 0?n:[]).forEach((e=>t.add(e.field)));this.transform.window.map((e=>e.field)).filter((e=>e!==undefined)).forEach(t.add,t);return t}producedFields(){return new Set(this.transform.window.map(this.getDefaultName))}getDefaultName(e){var n;return(n=e.as)!==null&&n!==void 0?n:Du(e)}hash(){return`WindowTransform ${w(this.transform)}`}assemble(){var e;const n=[];const t=[];const i=[];const r=[];for(const f of this.transform.window){t.push(f.op);i.push(this.getDefaultName(f));r.push(f.param===undefined?null:f.param);n.push(f.field===undefined?null:f.field)}const s=this.transform.frame;const o=this.transform.groupby;if(s&&s[0]===null&&s[1]===null&&t.every((e=>Ot(e)))){return Object.assign({type:"joinaggregate",as:i,ops:t,fields:n},o!==undefined?{groupby:o}:{})}const a=[];const u=[];if(this.transform.sort!==undefined){for(const n of this.transform.sort){a.push(n.field);u.push((e=n.order)!==null&&e!==void 0?e:"ascending")}}const c={field:a,order:u};const l=this.transform.ignorePeers;return Object.assign(Object.assign(Object.assign({type:"window",params:r,as:i,ops:t,fields:n,sort:c},l!==undefined?{ignorePeers:l}:{}),o!==undefined?{groupby:o}:{}),s!==undefined?{frame:s}:{})}}function Tv(e){function n(t){if(!(t instanceof iv)){const i=t.clone();if(i instanceof np){const n=qv+i.getSource();i.setSource(n);e.model.component.data.outputNodes[n]=i}else if(i instanceof tv||i instanceof zv||i instanceof Nv||i instanceof Bv){i.addDimensions(e.fields)}for(const e of t.children.flatMap(n)){e.parent=i}return[i]}return t.children.flatMap(n)}return n}function Mv(e){if(e instanceof iv){if(e.numChildren()===1&&!(e.children[0]instanceof np)){const n=e.children[0];if(n instanceof tv||n instanceof zv||n instanceof Nv||n instanceof Bv){n.addDimensions(e.fields)}n.swapWithParent();Mv(e)}else{const n=e.model.component.data.main;Lv(n);const t=Tv(e);const i=e.children.map(t).flat();for(const e of i){e.parent=n}}}else{e.children.map(Mv)}}function Lv(e){if(e instanceof np&&e.type===Rd.Main){if(e.numChildren()===1){const n=e.children[0];if(!(n instanceof iv)){n.swapWithParent();Lv(e)}}}}const qv="scale_";const Uv=5;function Rv(e){for(const n of e){for(const e of n.children){if(e.parent!==n){return false}}if(!Rv(n.children)){return false}}return true}function Iv(e,n){let t=false;for(const i of n){t=e.optimize(i)||t}return t}function Wv(e,n,t){let i=e.sources;let r=false;r=Iv(new Fv,i)||r;r=Iv(new wv(n),i)||r;i=i.filter((e=>e.numChildren()>0));r=Iv(new Av,i)||r;i=i.filter((e=>e.numChildren()>0));if(!t){r=Iv(new $v,i)||r;r=Iv(new Sv(n),i)||r;r=Iv(new jv,i)||r;r=Iv(new Dv,i)||r;r=Iv(new Cv,i)||r;r=Iv(new kv,i)||r;r=Iv(new xv,i)||r;r=Iv(new Ev,i)||r}e.sources=i;return r}function Hv(e,n){Rv(e.sources);let t=0;let i=0;for(let r=0;r<Uv;r++){if(!Wv(e,n,true)){break}t++}e.sources.map(Mv);for(let r=0;r<Uv;r++){if(!Wv(e,n,false)){break}i++}Rv(e.sources);if(Math.max(t,i)===Uv){Vr(`Maximum optimization runs(${Uv}) reached.`)}}class Gv{constructor(e){Object.defineProperty(this,"signal",{enumerable:true,get:e})}static fromName(e,n){return new Gv((()=>e(n)))}}var Yv=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function Kv(e){if(ZO(e)){Vv(e)}else{Qv(e)}}function Vv(e){const n=e.component.scales;for(const t of N(n)){const i=Jv(e,t);const r=n[t];r.setWithExplicit("domains",i);iO(e,t);if(e.component.data.isFaceted){let n=e;while(!ex(n)&&n.parent){n=n.parent}const r=n.component.resolve.scale[t];if(r==="shared"){for(const e of i.value){if(Ut(e)){e.data=qv+e.data.replace(qv,"")}}}}}}function Qv(e){for(const t of e.children){Kv(t)}const n=e.component.scales;for(const t of N(n)){let i;let r=null;for(const n of e.children){const e=n.component.scales[t];if(e){if(i===undefined){i=e.getWithExplicit("domains")}else{i=Pd(i,e.getWithExplicit("domains"),"domains","scale",oO)}const n=e.get("selectionExtent");if(r&&n&&r.param!==n.param){Vr(Di)}r=n}}n[t].setWithExplicit("domains",i);if(r){n[t].set("selectionExtent",r,true)}}}function Xv(e,n,t,i){if(e==="unaggregated"){const{valid:e,reason:i}=sO(n,t);if(!e){Vr(i);return undefined}}else if(e===undefined&&i.useUnaggregatedDomain){const{valid:e}=sO(n,t);if(e){return"unaggregated"}}return e}function Jv(e,n){const t=e.getScaleComponent(n).get("type");const{encoding:i}=e;const r=Xv(e.scaleDomain(n),e.typedFieldDef(n),t,e.config.scale);if(r!==e.scaleDomain(n)){e.specifiedScales[n]=Object.assign(Object.assign({},e.specifiedScales[n]),{domain:r})}if(n==="x"&&Ru(i.x2)){if(Ru(i.x)){return Pd(nO(t,r,e,"x"),nO(t,r,e,"x2"),"domain","scale",oO)}else{return nO(t,r,e,"x2")}}else if(n==="y"&&Ru(i.y2)){if(Ru(i.y)){return Pd(nO(t,r,e,"y"),nO(t,r,e,"y2"),"domain","scale",oO)}else{return nO(t,r,e,"y2")}}return nO(t,r,e,n)}function Zv(e,n,t){return e.map((e=>{const i=Ju(e,{timeUnit:t,type:n});return{signal:`{data: ${i}}`}}))}function eO(e,n,t){var i;const r=(i=$s(t))===null||i===void 0?void 0:i.unit;if(n==="temporal"||r){return Zv(e,n,r)}return[e]}function nO(e,n,t,i){const{encoding:s}=t;const o=Ru(s[i]);const{type:a}=o;const u=o["timeUnit"];if(wo(n)){const r=nO(e,undefined,t,i);const s=eO(n.unionWith,a,u);return Cd([...s,...r.value])}else if(Tt(n)){return Cd([n])}else if(n&&n!=="unaggregated"&&!xo(n)){return Cd(eO(n,a,u))}const c=t.stack;if(c&&i===c.fieldChannel){if(c.offset==="normalize"){return Sd([[0,1]])}const e=t.requestDataName(Rd.Main);return Sd([{data:e,field:t.vgField(i,{suffix:"start"})},{data:e,field:t.vgField(i,{suffix:"end"})}])}const l=ct(i)&&fu(o)?rO(t,i,e):undefined;if(pu(o)){const e=eO([o.datum],a,u);return Sd(e)}const f=o;if(n==="unaggregated"){const e=t.requestDataName(Rd.Main);const{field:n}=o;return Sd([{data:e,field:Du({field:n,aggregate:"min"})},{data:e,field:Du({field:n,aggregate:"max"})}])}else if(At(f.bin)){if(mo(e)){if(e==="bin-ordinal"){return Sd([])}return Sd([{data:L(l)?t.requestDataName(Rd.Main):t.requestDataName(Rd.Raw),field:t.vgField(i,ec(f,i)?{binSuffix:"range"}:{}),sort:l===true||!(0,r.Gv)(l)?{field:t.vgField(i,{}),op:"min"}:l}])}else{const{bin:e}=f;if(At(e)){const n=Qy(t,f.field,e);return Sd([new Gv((()=>{const e=t.getSignalName(n);return`[${e}.start, ${e}.stop]`}))])}else{return Sd([{data:t.requestDataName(Rd.Main),field:t.vgField(i,{})}])}}}else if(f.timeUnit&&F(["time","utc"],e)&&ou(f,ZO(t)?t.encoding[yn(i)]:undefined,t.markDef,t.config)){const e=t.requestDataName(Rd.Main);return Sd([{data:e,field:t.vgField(i)},{data:e,field:t.vgField(i,{suffix:"end"})}])}else if(l){return Sd([{data:L(l)?t.requestDataName(Rd.Main):t.requestDataName(Rd.Raw),field:t.vgField(i),sort:l}])}else{return Sd([{data:t.requestDataName(Rd.Main),field:t.vgField(i)}])}}function tO(e,n){const{op:t,field:i,order:r}=e;return Object.assign(Object.assign({op:t!==null&&t!==void 0?t:n?"sum":Wa},i?{field:Y(i)}:{}),r?{order:r}:{})}function iO(e,n){var t;const i=e.component.scales[n];const r=e.specifiedScales[n].domain;const s=(t=e.fieldDef(n))===null||t===void 0?void 0:t.bin;const o=xo(r)&&r;const a=Ct(s)&&St(s.extent)&&s.extent;if(o||a){i.set("selectionExtent",o!==null&&o!==void 0?o:a,true)}}function rO(e,n,t){if(!mo(t)){return undefined}const i=e.fieldDef(n);const r=i.sort;if(Va(r)){return{op:"min",field:Sb(i,n),order:"ascending"}}const{stack:s}=e;const o=s?new Set([...s.groupbyFields,...s.stackBy.map((e=>e.fieldDef.field))]):undefined;if(Ka(r)){const e=s&&!o.has(r.field);return tO(r,e)}else if(Ya(r)){const{encoding:n,order:t}=r;const i=e.fieldDef(n);const{aggregate:a,field:u}=i;const c=s&&!o.has(u);if(yt(a)||vt(a)){return tO({field:Du(i),order:t},c)}else if(Ot(a)||!a){return tO({op:a,field:u,order:t},c)}}else if(r==="descending"){return{op:"min",field:e.vgField(n),order:"descending"}}else if(F(["ascending",undefined],r)){return true}return undefined}function sO(e,n){const{aggregate:t,type:i}=e;if(!t){return{valid:false,reason:cr(e)}}if((0,r.Kg)(t)&&!$t.has(t)){return{valid:false,reason:lr(t)}}if(i==="quantitative"){if(n==="log"){return{valid:false,reason:fr(e)}}}return{valid:true}}function oO(e,n,t,i){if(e.explicit&&n.explicit){Vr(vr(t,i,e.value,n.value))}return{explicit:e.explicit,value:[...e.value,...n.value]}}function aO(e){const n=C(e.map((e=>{if(Ut(e)){const{sort:n}=e,t=Yv(e,["sort"]);return t}return e})),w);const t=C(e.map((e=>{if(Ut(e)){const n=e.sort;if(n!==undefined&&!L(n)){if("op"in n&&n.op==="count"){delete n.field}if(n.order==="ascending"){delete n.order}}return n}return undefined})).filter((e=>e!==undefined)),w);if(n.length===0){return undefined}else if(n.length===1){const n=e[0];if(Ut(n)&&t.length>0){let e=t[0];if(t.length>1){Vr(wr);e=true}else{if((0,r.Gv)(e)&&"field"in e){const t=e.field;if(n.field===t){e=e.order?{order:e.order}:true}}}return Object.assign(Object.assign({},n),{sort:e})}return n}const i=C(t.map((e=>{if(L(e)||!("op"in e)||(0,r.Kg)(e.op)&&e.op in bt){return e}Vr(xr(e));return true})),w);let s;if(i.length===1){s=i[0]}else if(i.length>1){Vr(wr);s=true}const o=C(e.map((e=>{if(Ut(e)){return e.data}return null})),(e=>e));if(o.length===1&&o[0]!==null){const e=Object.assign({data:o[0],fields:n.map((e=>e.field))},s?{sort:s}:{});return e}return Object.assign({fields:n},s?{sort:s}:{})}function uO(e){if(Ut(e)&&(0,r.Kg)(e.field)){return e.field}else if(Lt(e)){let n;for(const t of e.fields){if(Ut(t)&&(0,r.Kg)(t.field)){if(!n){n=t.field}else if(n!==t.field){Vr(jr);return n}}}Vr(Fr);return n}else if(qt(e)){Vr($r);const n=e.fields[0];return(0,r.Kg)(n)?n:undefined}return undefined}function cO(e,n){const t=e.component.scales[n];const i=t.get("domains").map((n=>{if(Ut(n)){n.data=e.lookupDataSource(n.data)}return n}));return aO(i)}var lO=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function fO(e){if(tx(e)||nx(e)){return e.children.reduce(((e,n)=>e.concat(fO(n))),dO(e))}else{return dO(e)}}function dO(e){return N(e.component.scales).reduce(((n,t)=>{const i=e.component.scales[t];if(i.merged){return n}const r=i.combine();const{name:s,type:o,selectionExtent:a,domains:u,range:c,reverse:l}=r,f=lO(r,["name","type","selectionExtent","domains","range","reverse"]);const d=pO(r.range,s,t,e);const p=cO(e,t);const g=a?Jd(e,a,i,p):null;n.push(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({name:s,type:o},p?{domain:p}:{}),g?{domainRaw:g}:{}),{range:d}),l!==undefined?{reverse:l}:{}),f));return n}),[])}function pO(e,n,t,i){if(Rn(t)){if(Mt(e)){return{step:{signal:`${n}_step`}}}}else if((0,r.Gv)(e)&&Ut(e)){return Object.assign(Object.assign({},e),{data:i.lookupDataSource(e.data)})}return e}class gO extends kd{constructor(e,n){super({},{name:e});this.merged=false;this.setWithExplicit("type",n)}domainDefinitelyIncludesZero(){if(this.get("zero")!==false){return true}return $(this.get("domains"),(e=>(0,r.cy)(e)&&e.length===2&&e[0]<=0&&e[1]>=0))}}const mO=["range","scheme"];function hO(e){const n=e.component.scales;for(const t of ut){const i=n[t];if(!i){continue}const r=yO(t,e);i.setWithExplicit("range",r)}}function bO(e,n){const t=e.fieldDef(n);if(t===null||t===void 0?void 0:t.bin){const{bin:i,field:s}=t;const o=vn(n);const a=e.getName(o);if((0,r.Gv)(i)&&i.binned&&i.step!==undefined){return new Gv((()=>{const t=e.scaleName(n);const r=`(domain("${t}")[1] - domain("${t}")[0]) / ${i.step}`;return`${e.getSignalName(a)} / (${r})`}))}else if(At(i)){const n=Qy(e,s,i);return new Gv((()=>{const t=e.getSignalName(n);const i=`(${t}.stop - ${t}.start) / ${t}.step`;return`${e.getSignalName(a)} / (${i})`}))}}return undefined}function yO(e,n){const t=n.specifiedScales[e];const{size:i}=n;const s=n.getScaleComponent(e);const o=s.get("type");for(const d of mO){if(t[d]!==undefined){const i=_o(o,d);const s=zo(e,d);if(!i){Vr(mr(o,d,e))}else if(s){Vr(s)}else{switch(d){case"range":{const i=t.range;if((0,r.cy)(i)){if(Rn(e)){return Cd(i.map((e=>{if(e==="width"||e==="height"){const t=n.getName(e);const i=n.getSignalName.bind(n);return Gv.fromName(i,t)}return e})))}}else if((0,r.Gv)(i)){return Cd({data:n.requestDataName(Rd.Main),field:i.field,sort:{op:"min",field:n.vgField(e)}})}return Cd(i)}case"scheme":return Cd(vO(t[d]))}}}}const a=e===ce||e==="xOffset"?"width":"height";const u=i[a];if(Bl(u)){if(Rn(e)){if(mo(o)){const t=xO(u,n,e);if(t){return Cd({step:t})}}else{Vr(br(a))}}else if(Kn(e)){const t=e===pe?"x":"y";const i=n.getScaleComponent(t);const r=i.get("type");if(r==="band"){const e=wO(u,o);if(e){return Cd(e)}}}}const{rangeMin:c,rangeMax:l}=t;const f=OO(e,n);if((c!==undefined||l!==undefined)&&_o(o,"rangeMin")&&(0,r.cy)(f)&&f.length===2){return Cd([c!==null&&c!==void 0?c:f[0],l!==null&&l!==void 0?l:f[1]])}return Sd(f)}function vO(e){if(Oo(e)){return Object.assign({scheme:e.name},O(e,["name"]))}return{scheme:e}}function OO(e,n){const{size:t,config:i,mark:r,encoding:s}=n;const o=n.getSignalName.bind(n);const{type:a}=Ru(s[e]);const u=n.getScaleComponent(e);const c=u.get("type");const{domain:l,domainMid:f}=n.specifiedScales[e];switch(e){case ce:case le:{if(F(["point","band"],c)){const r=FO(e,t,i.view);if(Bl(r)){const t=xO(r,n,e);return{step:t}}}const r=vn(e);const s=n.getName(r);if(e===le&&ho(c)){return[Gv.fromName(o,s),0]}else{return[0,Gv.fromName(o,s)]}}case pe:case ge:return jO(e,n,c);case Ae:{const s=n.component.scales[e].get("zero");const o=AO(r,s,i);const a=CO(r,t,n,i);if(yo(c)){return DO(o,a,$O(c,i,l,e))}else{return[o,a]}}case be:return[0,Math.PI*2];case ke:return[0,360];case me:{return[0,new Gv((()=>{const e=n.getSignalName("width");const t=n.getSignalName("height");return`min(${e},${t})/2`}))]}case Be:return[i.scale.minStrokeWidth,i.scale.maxStrokeWidth];case Pe:return[[1,0],[4,2],[2,1],[1,1],[1,2,4,2]];case De:return"symbol";case je:case Fe:case $e:if(c==="ordinal"){return a==="nominal"?"category":"ordinal"}else{if(f!==undefined){return"diverging"}else{return r==="rect"||r==="geoshape"?"heatmap":"ramp"}}case Ce:case Se:case Ee:return[i.scale.minOpacity,i.scale.maxOpacity]}}function xO(e,n,t){var i,r,s,o,a;const{encoding:u}=n;const c=n.getScaleComponent(t);const l=xn(t);const f=u[l];const d=El({step:e,offsetIsDiscrete:bu(f)&&Gs(f.type)});if(d==="offset"&&mc(u,l)){const t=n.getScaleComponent(l);const u=n.scaleName(l);let f=`domain('${u}').length`;if(t.get("type")==="band"){const e=(r=(i=t.get("paddingInner"))!==null&&i!==void 0?i:t.get("padding"))!==null&&r!==void 0?r:0;const n=(o=(s=t.get("paddingOuter"))!==null&&s!==void 0?s:t.get("padding"))!==null&&o!==void 0?o:0;f=`bandspace(${f}, ${e}, ${n})`}const d=(a=c.get("paddingInner"))!==null&&a!==void 0?a:c.get("padding");return{signal:`${e.step} * ${f} / (1-${Jt(d)})`}}else{return e.step}}function wO(e,n){const t=El({step:e,offsetIsDiscrete:mo(n)});if(t==="offset"){return{step:e.step}}return undefined}function jO(e,n,t){const i=e===pe?"x":"y";const r=n.getScaleComponent(i);const s=r.get("type");const o=n.scaleName(i);if(s==="band"){const e=FO(i,n.size,n.config.view);if(Bl(e)){const n=wO(e,t);if(n){return n}}return[0,{signal:`bandwidth('${o}')`}]}else{return y(`Cannot use ${e} scale if ${i} scale is not discrete.`)}}function FO(e,n,t){const i=e===ce?"width":"height";const r=n[i];if(r){return r}return Ul(t,i)}function $O(e,n,t,i){switch(e){case"quantile":return n.scale.quantileCount;case"quantize":return n.scale.quantizeCount;case"threshold":if(t!==undefined&&(0,r.cy)(t)){return t.length+1}else{Vr(Mr(i));return 3}}}function DO(e,n,t){const i=()=>{const i=ei(n);const r=ei(e);const s=`(${i} - ${r}) / (${t} - 1)`;return`sequence(${r}, ${i} + ${s}, ${s})`};if(Tt(n)){return new Gv(i)}else{return{signal:i()}}}function AO(e,n,t){if(n){if(Tt(n)){return{signal:`${n.signal} ? 0 : ${AO(e,false,t)}`}}else{return 0}}switch(e){case"bar":case"tick":return t.scale.minBandSize;case"line":case"trail":case"rule":return t.scale.minStrokeWidth;case"text":return t.scale.minFontSize;case"point":case"square":case"circle":return t.scale.minSize}throw new Error(Qi("size",e))}const kO=.95;function CO(e,n,t,i){const s={x:bO(t,"x"),y:bO(t,"y")};switch(e){case"bar":case"tick":{if(i.scale.maxBandSize!==undefined){return i.scale.maxBandSize}const e=SO(n,s,i.view);if((0,r.Et)(e)){return e-1}else{return new Gv((()=>`${e.signal} - 1`))}}case"line":case"trail":case"rule":return i.scale.maxStrokeWidth;case"text":return i.scale.maxFontSize;case"point":case"square":case"circle":{if(i.scale.maxSize){return i.scale.maxSize}const e=SO(n,s,i.view);if((0,r.Et)(e)){return Math.pow(kO*e,2)}else{return new Gv((()=>`pow(${kO} * ${e.signal}, 2)`))}}}throw new Error(Qi("size",e))}function SO(e,n,t){const i=Bl(e.width)?e.width.step:ql(t,"width");const r=Bl(e.height)?e.height.step:ql(t,"height");if(n.x||n.y){return new Gv((()=>{const e=[n.x?n.x.signal:i,n.y?n.y.signal:r];return`min(${e.join(", ")})`}))}return Math.min(i,r)}function EO(e,n){if(ZO(e)){BO(e,n)}else{zO(e,n)}}function BO(e,n){const t=e.component.scales;const{config:i,encoding:r,markDef:s,specifiedScales:o}=e;for(const a of N(t)){const u=o[a];const c=t[a];const l=e.getScaleComponent(a);const f=Ru(r[a]);const d=u[n];const p=l.get("type");const g=l.get("padding");const m=l.get("paddingInner");const h=_o(p,n);const b=zo(a,n);if(d!==undefined){if(!h){Vr(mr(p,n,a))}else if(b){Vr(b)}}if(h&&b===undefined){if(d!==undefined){const e=f["timeUnit"];const t=f.type;switch(n){case"domainMax":case"domainMin":if(Jr(u[n])||t==="temporal"||e){c.set(n,{signal:Ju(u[n],{type:t,timeUnit:e})},true)}else{c.set(n,u[n],true)}break;default:c.copyKeyFromObject(n,u)}}else{const t=n in PO?PO[n]({model:e,channel:a,fieldOrDatumDef:f,scaleType:p,scalePadding:g,scalePaddingInner:m,domain:u.domain,domainMin:u.domainMin,domainMax:u.domainMax,markDef:s,config:i,hasNestedOffsetScale:hc(r,a),hasSecondaryRangeChannel:!!r[yn(a)]}):i.scale[n];if(t!==undefined){c.set(n,t,false)}}}}}const PO={bins:({model:e,fieldOrDatumDef:n})=>fu(n)?NO(e,n):undefined,interpolate:({channel:e,fieldOrDatumDef:n})=>TO(e,n.type),nice:({scaleType:e,channel:n,domain:t,domainMin:i,domainMax:r,fieldOrDatumDef:s})=>MO(e,n,t,i,r,s),padding:({channel:e,scaleType:n,fieldOrDatumDef:t,markDef:i,config:r})=>LO(e,n,r.scale,t,i,r.bar),paddingInner:({scalePadding:e,channel:n,markDef:t,scaleType:i,config:r,hasNestedOffsetScale:s})=>qO(e,n,t.type,i,r.scale,s),paddingOuter:({scalePadding:e,channel:n,scaleType:t,scalePaddingInner:i,config:r,hasNestedOffsetScale:s})=>UO(e,n,t,i,r.scale,s),reverse:({fieldOrDatumDef:e,scaleType:n,channel:t,config:i})=>{const r=fu(e)?e.sort:undefined;return RO(n,r,t,i.scale)},zero:({channel:e,fieldOrDatumDef:n,domain:t,markDef:i,scaleType:r,config:s,hasSecondaryRangeChannel:o})=>IO(e,n,t,i,r,s.scale,o)};function _O(e){if(ZO(e)){hO(e)}else{zO(e,"range")}}function zO(e,n){const t=e.component.scales;for(const i of e.children){if(n==="range"){_O(i)}else{EO(i,n)}}for(const i of N(t)){let r;for(const t of e.children){const e=t.component.scales[i];if(e){const t=e.getWithExplicit(n);r=Pd(r,t,n,"scale",Ed(((e,t)=>{switch(n){case"range":if(e.step&&t.step){return e.step-t.step}return 0}return 0})))}}t[i].setWithExplicit(n,r)}}function NO(e,n){const t=n.bin;if(At(t)){const i=Qy(e,n.field,t);return new Gv((()=>e.getSignalName(i)))}else if(kt(t)&&Ct(t)&&t.step!==undefined){return{step:t.step}}return undefined}function TO(e,n){if(F([je,Fe,$e],e)&&n!=="nominal"){return"hcl"}return undefined}function MO(e,n,t,i,s,o){var a;if(((a=Uu(o))===null||a===void 0?void 0:a.bin)||(0,r.cy)(t)||s!=null||i!=null||F([no.TIME,no.UTC],e)){return undefined}return Rn(n)?true:undefined}function LO(e,n,t,i,r,s){if(Rn(e)){if(bo(n)){if(t.continuousPadding!==undefined){return t.continuousPadding}const{type:n,orient:o}=r;if(n==="bar"&&!(fu(i)&&(i.bin||i.timeUnit))){if(o==="vertical"&&e==="x"||o==="horizontal"&&e==="y"){return s.continuousBandSize}}}if(n===no.POINT){return t.pointPadding}}return undefined}function qO(e,n,t,i,r,s=false){if(e!==undefined){return undefined}if(Rn(n)){const{bandPaddingInner:e,barBandPaddingInner:n,rectBandPaddingInner:i,bandWithNestedOffsetPaddingInner:o}=r;if(s){return o}return X(e,t==="bar"?n:i)}else if(Kn(n)){if(i===no.BAND){return r.offsetBandPaddingInner}}return undefined}function UO(e,n,t,i,r,s=false){if(e!==undefined){return undefined}if(Rn(n)){const{bandPaddingOuter:e,bandWithNestedOffsetPaddingOuter:n}=r;if(s){return n}if(t===no.BAND){return X(e,Tt(i)?{signal:`${i.signal}/2`}:i/2)}}else if(Kn(n)){if(t===no.POINT){return.5}else if(t===no.BAND){return r.offsetBandPaddingOuter}}return undefined}function RO(e,n,t,i){if(t==="x"&&i.xReverse!==undefined){if(ho(e)&&n==="descending"){if(Tt(i.xReverse)){return{signal:`!${i.xReverse.signal}`}}else{return!i.xReverse}}return i.xReverse}if(ho(e)&&n==="descending"){return true}return undefined}function IO(e,n,t,i,s,o,a){const u=!!t&&t!=="unaggregated";if(u){if(ho(s)){if((0,r.cy)(t)){const e=t[0];const n=t[t.length-1];if(e<=0&&n>=0){return true}}return false}}if(e==="size"&&n.type==="quantitative"&&!yo(s)){return true}if(!(fu(n)&&n.bin)&&F([...Un,...Wn],e)){const{orient:n,type:t}=i;if(F(["bar","area","line","trail"],t)){if(n==="horizontal"&&e==="y"||n==="vertical"&&e==="x"){return false}}if(F(["bar","area"],t)&&!a){return true}return o===null||o===void 0?void 0:o.zero}return false}function WO(e,n,t,i,r=false){const s=HO(n,t,i,r);const{type:o}=e;if(!ct(n)){return null}if(o!==undefined){if(!To(n,o)){Vr(pr(n,o,s));return s}if(fu(t)&&!No(o,t.type)){Vr(gr(o,s));return s}return o}return s}function HO(e,n,t,i){var r;switch(n.type){case"nominal":case"ordinal":{if(Qe(e)||mt(e)==="discrete"){if(e==="shape"&&n.type==="ordinal"){Vr(tr(e,"ordinal"))}return"ordinal"}if(Rn(e)||Kn(e)){if(F(["rect","bar","image","rule"],t.type)){return"band"}if(i){return"band"}}else if(t.type==="arc"&&e in In){return"band"}const s=t[vn(e)];if(ga(s)){return"band"}if(xu(n)&&((r=n.axis)===null||r===void 0?void 0:r.tickBand)){return"band"}return"point"}case"temporal":if(Qe(e)){return"time"}else if(mt(e)==="discrete"){Vr(tr(e,"temporal"));return"ordinal"}else if(fu(n)&&n.timeUnit&&$s(n.timeUnit).utc){return"utc"}return"time";case"quantitative":if(Qe(e)){if(fu(n)&&At(n.bin)){return"bin-ordinal"}return"linear"}else if(mt(e)==="discrete"){Vr(tr(e,"quantitative"));return"ordinal"}return"linear";case"geojson":return undefined}throw new Error(Ri(n.type))}function GO(e,{ignoreRange:n}={}){YO(e);Kv(e);for(const t of Po){EO(e,t)}if(!n){_O(e)}}function YO(e){if(ZO(e)){e.component.scales=KO(e)}else{e.component.scales=QO(e)}}function KO(e){const{encoding:n,mark:t,markDef:i}=e;const r={};for(const s of ut){const o=Ru(n[s]);if(o&&t===Jo&&s===De&&o.type===Xs){continue}let a=o&&o["scale"];if(Kn(s)){const e=wn(s);if(!hc(n,e)){if(a){Vr(Xi(s))}continue}}if(o&&a!==null&&a!==false){a!==null&&a!==void 0?a:a={};const t=hc(n,s);const u=WO(a,s,o,i,t);r[s]=new gO(e.scaleName(`${s}`,true),{value:u,explicit:a.type===u})}}return r}const VO=Ed(((e,n)=>oo(e)-oo(n)));function QO(e){var n;var t;const i=e.component.scales={};const r={};const s=e.component.resolve;for(const o of e.children){YO(o);for(const i of N(o.component.scales)){(n=(t=s.scale)[i])!==null&&n!==void 0?n:t[i]=Zb(i,e);if(s.scale[i]==="shared"){const e=r[i];const n=o.component.scales[i].getWithExplicit("type");if(e){if(ro(e.value,n.value)){r[i]=Pd(e,n,"type","scale",VO)}else{s.scale[i]="independent";delete r[i]}}else{r[i]=n}}}}for(const o of N(r)){const n=e.scaleName(o,true);const t=r[o];i[o]=new gO(n,t);for(const i of e.children){const e=i.component.scales[o];if(e){i.renameScale(e.get("name"),n);e.merged=true}}}return i}var XO=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};class JO{constructor(){this.nameMap={}}rename(e,n){this.nameMap[e]=n}has(e){return this.nameMap[e]!==undefined}get(e){while(this.nameMap[e]&&e!==this.nameMap[e]){e=this.nameMap[e]}return e}}function ZO(e){return(e===null||e===void 0?void 0:e.type)==="unit"}function ex(e){return(e===null||e===void 0?void 0:e.type)==="facet"}function nx(e){return(e===null||e===void 0?void 0:e.type)==="concat"}function tx(e){return(e===null||e===void 0?void 0:e.type)==="layer"}class ix{constructor(e,n,t,i,r,s,o){var a,u;this.type=n;this.parent=t;this.config=r;this.correctDataNames=e=>{var n,t,i;if((n=e.from)===null||n===void 0?void 0:n.data){e.from.data=this.lookupDataSource(e.from.data)}if((i=(t=e.from)===null||t===void 0?void 0:t.facet)===null||i===void 0?void 0:i.data){e.from.facet.data=this.lookupDataSource(e.from.facet.data)}return e};this.parent=t;this.config=r;this.view=Pt(o);this.name=(a=e.name)!==null&&a!==void 0?a:i;this.title=Nt(e.title)?{text:e.title}:e.title?Pt(e.title):undefined;this.scaleNameMap=t?t.scaleNameMap:new JO;this.projectionNameMap=t?t.projectionNameMap:new JO;this.signalNameMap=t?t.signalNameMap:new JO;this.data=e.data;this.description=e.description;this.transforms=ud((u=e.transform)!==null&&u!==void 0?u:[]);this.layout=n==="layer"||n==="unit"?{}:Tl(e,n,r);this.component={data:{sources:t?t.component.data.sources:[],outputNodes:t?t.component.data.outputNodes:{},outputNodeRefCounts:t?t.component.data.outputNodeRefCounts:{},isFaceted:Ja(e)||(t===null||t===void 0?void 0:t.component.data.isFaceted)&&e.data===undefined},layoutSize:new kd,layoutHeaders:{row:{},column:{},facet:{}},mark:null,resolve:Object.assign({scale:{},axis:{},legend:{}},s?b(s):{}),selection:null,scales:null,projection:null,axes:{},legends:{}}}get width(){return this.getSizeSignalRef("width")}get height(){return this.getSizeSignalRef("height")}parse(){this.parseScale();this.parseLayoutSize();this.renameTopLevelLayoutSizeSignal();this.parseSelections();this.parseProjection();this.parseData();this.parseAxesAndHeaders();this.parseLegends();this.parseMarkGroup()}parseScale(){GO(this)}parseProjection(){Uy(this)}renameTopLevelLayoutSizeSignal(){if(this.getName("width")!=="width"){this.renameSignal(this.getName("width"),"width")}if(this.getName("height")!=="height"){this.renameSignal(this.getName("height"),"height")}}parseLegends(){Fy(this)}assembleEncodeFromView(e){const{style:n}=e,t=XO(e,["style"]);const i={};for(const r of N(t)){const e=t[r];if(e!==undefined){i[r]=Xt(e)}}return i}assembleGroupEncodeEntry(e){let n={};if(this.view){n=this.assembleEncodeFromView(this.view)}if(!e){if(this.description){n["description"]=Xt(this.description)}if(this.type==="unit"||this.type==="layer"){return Object.assign({width:this.getSizeSignalRef("width"),height:this.getSizeSignalRef("height")},n!==null&&n!==void 0?n:{})}}return z(n)?undefined:n}assembleLayout(){if(!this.layout){return undefined}const e=this.layout,{spacing:n}=e,t=XO(e,["spacing"]);const{component:i,config:r}=this;const s=Hb(i.layoutHeaders,r);return Object.assign(Object.assign(Object.assign({padding:n},this.assembleDefaultLayout()),t),s?{titleBand:s}:{})}assembleDefaultLayout(){return{}}assembleHeaderMarks(){const{layoutHeaders:e}=this.component;let n=[];for(const t of Je){if(e[t].title){n.push(Nb(this,t))}}for(const t of _b){n=n.concat(Lb(this,t))}return n}assembleAxes(){return lb(this.component.axes,this.config)}assembleLegends(){return _y(this)}assembleProjections(){return Ny(this)}assembleTitle(){var e,n,t;const i=(e=this.title)!==null&&e!==void 0?e:{},{encoding:r}=i,s=XO(i,["encoding"]);const o=Object.assign(Object.assign(Object.assign({},zt(this.config.title).nonMarkTitleProperties),s),r?{encode:{update:r}}:{});if(o.text){if(F(["unit","layer"],this.type)){if(F(["middle",undefined],o.anchor)){(n=o.frame)!==null&&n!==void 0?n:o.frame="group"}}else{(t=o.anchor)!==null&&t!==void 0?t:o.anchor="start"}return z(o)?undefined:o}return undefined}assembleGroup(e=[]){const n={};e=e.concat(this.assembleSignals());if(e.length>0){n.signals=e}const t=this.assembleLayout();if(t){n.layout=t}n.marks=[].concat(this.assembleHeaderMarks(),this.assembleMarks());const i=!this.parent||ex(this.parent)?fO(this):[];if(i.length>0){n.scales=i}const r=this.assembleAxes();if(r.length>0){n.axes=r}const s=this.assembleLegends();if(s.length>0){n.legends=s}return n}getName(e){return q((this.name?`${this.name}_`:"")+e)}getDataName(e){return this.getName(Rd[e].toLowerCase())}requestDataName(e){const n=this.getDataName(e);const t=this.component.data.outputNodeRefCounts;t[n]=(t[n]||0)+1;return n}getSizeSignalRef(e){if(ex(this.parent)){const n=Xb(e);const t=Hn(n);const i=this.component.scales[t];if(i&&!i.merged){const e=i.get("type");const n=i.get("range");if(mo(e)&&Mt(n)){const e=i.get("name");const n=cO(this,t);const r=uO(n);if(r){const n=Du({aggregate:"distinct",field:r},{expr:"datum"});return{signal:Qb(e,i,n)}}else{Vr(hi(t));return null}}}}return{signal:this.signalNameMap.get(this.getName(e))}}lookupDataSource(e){const n=this.component.data.outputNodes[e];if(!n){return e}return n.getSource()}getSignalName(e){return this.signalNameMap.get(e)}renameSignal(e,n){this.signalNameMap.rename(e,n)}renameScale(e,n){this.scaleNameMap.rename(e,n)}renameProjection(e,n){this.projectionNameMap.rename(e,n)}scaleName(e,n){if(n){return this.getName(e)}if(pn(e)&&ct(e)&&this.component.scales[e]||this.scaleNameMap.has(this.getName(e))){return this.scaleNameMap.get(this.getName(e))}return undefined}projectionName(e){if(e){return this.getName("projection")}if(this.component.projection&&!this.component.projection.merged||this.projectionNameMap.has(this.getName("projection"))){return this.projectionNameMap.get(this.getName("projection"))}return undefined}getScaleComponent(e){if(!this.component.scales){throw new Error("getScaleComponent cannot be called before parseScale(). Make sure you have called parseScale or use parseUnitModelWithScale().")}const n=this.component.scales[e];if(n&&!n.merged){return n}return this.parent?this.parent.getScaleComponent(e):undefined}getSelectionComponent(e,n){let t=this.component.selection[e];if(!t&&this.parent){t=this.parent.getSelectionComponent(e,n)}if(!t){throw new Error(xi(n))}return t}hasAxisOrientSignalRef(){var e,n;return((e=this.component.axes.x)===null||e===void 0?void 0:e.some((e=>e.hasOrientSignalRef())))||((n=this.component.axes.y)===null||n===void 0?void 0:n.some((e=>e.hasOrientSignalRef())))}}class rx extends ix{vgField(e,n={}){const t=this.fieldDef(e);if(!t){return undefined}return Du(t,n)}reduceFieldDef(e,n){return Fc(this.getMapping(),((n,t,i)=>{const r=Uu(t);if(r){return e(n,r,i)}return n}),n)}forEachFieldDef(e,n){jc(this.getMapping(),((n,t)=>{const i=Uu(n);if(i){e(i,t)}}),n)}}var sx=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};class ox extends ep{clone(){return new ox(null,b(this.transform))}constructor(e,n){var t,i,r;super(e);this.transform=n;this.transform=b(n);const s=(t=this.transform.as)!==null&&t!==void 0?t:[undefined,undefined];this.transform.as=[(i=s[0])!==null&&i!==void 0?i:"value",(r=s[1])!==null&&r!==void 0?r:"density"];if(n.groupby&&n.minsteps==null&&n.maxsteps==null&&n.steps==null){this.transform.steps=200}}dependentFields(){var e;return new Set([this.transform.density,...(e=this.transform.groupby)!==null&&e!==void 0?e:[]])}producedFields(){return new Set(this.transform.as)}hash(){return`DensityTransform ${w(this.transform)}`}assemble(){const e=this.transform,{density:n}=e,t=sx(e,["density"]);const i=Object.assign({type:"kde",field:n},t);return i}}class ax extends ep{clone(){return new ax(null,Object.assign({},this.filter))}constructor(e,n){super(e);this.filter=n}static make(e,n){const{config:t,mark:i,markDef:r}=n;const s=ii("invalid",r,t);if(s!=="filter"){return null}const o=n.reduceFieldDef(((e,t,r)=>{const s=ct(r)&&n.getScaleComponent(r);if(s){const n=s.get("type");if(ho(n)&&t.aggregate!=="count"&&!ea(i)){e[t.field]=t}}return e}),{});if(!N(o).length){return null}return new ax(e,o)}dependentFields(){return new Set(N(this.filter))}producedFields(){return new Set}hash(){return`FilterInvalid ${w(this.filter)}`}assemble(){const e=N(this.filter).reduce(((e,n)=>{const t=this.filter[n];const i=Du(t,{expr:"datum"});if(t!==null){if(t.type==="temporal"){e.push(`(isDate(${i}) || (isValid(${i}) && isFinite(+${i})))`)}else if(t.type==="quantitative"){e.push(`isValid(${i})`);e.push(`isFinite(+${i})`)}else{}}return e}),[]);return e.length>0?{type:"filter",expr:e.join(" && ")}:null}}class ux extends ep{clone(){return new ux(this.parent,b(this.transform))}constructor(e,n){super(e);this.transform=n;this.transform=b(n);const{flatten:t,as:i=[]}=this.transform;this.transform.as=t.map(((e,n)=>{var t;return(t=i[n])!==null&&t!==void 0?t:e}))}dependentFields(){return new Set(this.transform.flatten)}producedFields(){return new Set(this.transform.as)}hash(){return`FlattenTransform ${w(this.transform)}`}assemble(){const{flatten:e,as:n}=this.transform;const t={type:"flatten",fields:e,as:n};return t}}class cx extends ep{clone(){return new cx(null,b(this.transform))}constructor(e,n){var t,i,r;super(e);this.transform=n;this.transform=b(n);const s=(t=this.transform.as)!==null&&t!==void 0?t:[undefined,undefined];this.transform.as=[(i=s[0])!==null&&i!==void 0?i:"key",(r=s[1])!==null&&r!==void 0?r:"value"]}dependentFields(){return new Set(this.transform.fold)}producedFields(){return new Set(this.transform.as)}hash(){return`FoldTransform ${w(this.transform)}`}assemble(){const{fold:e,as:n}=this.transform;const t={type:"fold",fields:e,as:n};return t}}class lx extends ep{clone(){return new lx(null,b(this.fields),this.geojson,this.signal)}static parseAll(e,n){if(n.component.projection&&!n.component.projection.isFit){return e}let t=0;for(const i of[[Oe,ve],[we,xe]]){const r=i.map((e=>{const t=Ru(n.encoding[e]);return fu(t)?t.field:pu(t)?{expr:`${t.datum}`}:vu(t)?{expr:`${t["value"]}`}:undefined}));if(r[0]||r[1]){e=new lx(e,r,null,n.getName(`geojson_${t++}`))}}if(n.channelHasField(De)){const i=n.typedFieldDef(De);if(i.type===Xs){e=new lx(e,null,i.field,n.getName(`geojson_${t++}`))}}return e}constructor(e,n,t,i){super(e);this.fields=n;this.geojson=t;this.signal=i}dependentFields(){var e;const n=((e=this.fields)!==null&&e!==void 0?e:[]).filter(r.Kg);return new Set([...this.geojson?[this.geojson]:[],...n])}producedFields(){return new Set}hash(){return`GeoJSON ${this.geojson} ${this.signal} ${w(this.fields)}`}assemble(){return[...this.geojson?[{type:"filter",expr:`isValid(datum["${this.geojson}"])`}]:[],Object.assign(Object.assign(Object.assign({type:"geojson"},this.fields?{fields:this.fields}:{}),this.geojson?{geojson:this.geojson}:{}),{signal:this.signal})]}}class fx extends ep{clone(){return new fx(null,this.projection,b(this.fields),b(this.as))}constructor(e,n,t,i){super(e);this.projection=n;this.fields=t;this.as=i}static parseAll(e,n){if(!n.projectionName()){return e}for(const t of[[Oe,ve],[we,xe]]){const i=t.map((e=>{const t=Ru(n.encoding[e]);return fu(t)?t.field:pu(t)?{expr:`${t.datum}`}:vu(t)?{expr:`${t["value"]}`}:undefined}));const r=t[0]===we?"2":"";if(i[0]||i[1]){e=new fx(e,n.projectionName(),i,[n.getName(`x${r}`),n.getName(`y${r}`)])}}return e}dependentFields(){return new Set(this.fields.filter(r.Kg))}producedFields(){return new Set(this.as)}hash(){return`Geopoint ${this.projection} ${w(this.fields)} ${w(this.as)}`}assemble(){return{type:"geopoint",projection:this.projection,fields:this.fields,as:this.as}}}class dx extends ep{clone(){return new dx(null,b(this.transform))}constructor(e,n){super(e);this.transform=n}dependentFields(){var e;return new Set([this.transform.impute,this.transform.key,...(e=this.transform.groupby)!==null&&e!==void 0?e:[]])}producedFields(){return new Set([this.transform.impute])}processSequence(e){const{start:n=0,stop:t,step:i}=e;const r=[n,t,...i?[i]:[]].join(",");return{signal:`sequence(${r})`}}static makeFromTransform(e,n){return new dx(e,n)}static makeFromEncoding(e,n){const t=n.encoding;const i=t.x;const r=t.y;if(fu(i)&&fu(r)){const s=i.impute?i:r.impute?r:undefined;if(s===undefined){return undefined}const o=i.impute?r:r.impute?i:undefined;const{method:a,value:u,frame:c,keyvals:l}=s.impute;const f=$c(n.mark,t);return new dx(e,Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({impute:s.field,key:o.field},a?{method:a}:{}),u!==undefined?{value:u}:{}),c?{frame:c}:{}),l!==undefined?{keyvals:l}:{}),f.length?{groupby:f}:{}))}return null}hash(){return`Impute ${w(this.transform)}`}assemble(){const{impute:e,key:n,keyvals:t,method:i,groupby:r,value:s,frame:o=[null,null]}=this.transform;const a=Object.assign(Object.assign(Object.assign(Object.assign({type:"impute",field:e,key:n},t?{keyvals:Rf(t)?this.processSequence(t):t}:{}),{method:"value"}),r?{groupby:r}:{}),{value:!i||i==="value"?s:null});if(i&&i!=="value"){const n=Object.assign({type:"window",as:[`imputed_${e}_value`],ops:[i],fields:[e],frame:o,ignorePeers:false},r?{groupby:r}:{});const t={type:"formula",expr:`datum.${e} === null ? datum.imputed_${e}_value : datum.${e}`,as:e};return[a,n,t]}else{return[a]}}}var px=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};class gx extends ep{clone(){return new gx(null,b(this.transform))}constructor(e,n){var t,i,r;super(e);this.transform=n;this.transform=b(n);const s=(t=this.transform.as)!==null&&t!==void 0?t:[undefined,undefined];this.transform.as=[(i=s[0])!==null&&i!==void 0?i:n.on,(r=s[1])!==null&&r!==void 0?r:n.loess]}dependentFields(){var e;return new Set([this.transform.loess,this.transform.on,...(e=this.transform.groupby)!==null&&e!==void 0?e:[]])}producedFields(){return new Set(this.transform.as)}hash(){return`LoessTransform ${w(this.transform)}`}assemble(){const e=this.transform,{loess:n,on:t}=e,i=px(e,["loess","on"]);const r=Object.assign({type:"loess",x:t,y:n},i);return r}}class mx extends ep{clone(){return new mx(null,b(this.transform),this.secondary)}constructor(e,n,t){super(e);this.transform=n;this.secondary=t}static make(e,n,t,i){const r=n.component.data.sources;const{from:s}=t;let o=null;if(Wf(s)){let e=Lx(s.data,r);if(!e){e=new pv(s.data);r.push(e)}const t=n.getName(`lookup_${i}`);o=new np(e,t,Rd.Lookup,n.component.data.outputNodeRefCounts);n.component.data.outputNodes[t]=o}else if(Hf(s)){const e=s.param;t=Object.assign({as:e},t);let i;try{i=n.getSelectionComponent(q(e),e)}catch(a){throw new Error(Fi(e))}o=i.materialized;if(!o){throw new Error($i(e))}}return new mx(e,t,o.getSource())}dependentFields(){return new Set([this.transform.lookup])}producedFields(){return new Set(this.transform.as?(0,r.YO)(this.transform.as):this.transform.from.fields)}hash(){return`Lookup ${w({transform:this.transform,secondary:this.secondary})}`}assemble(){let e;if(this.transform.from.fields){e=Object.assign({values:this.transform.from.fields},this.transform.as?{as:(0,r.YO)(this.transform.as)}:{})}else{let n=this.transform.as;if(!(0,r.Kg)(n)){Vr(zi);n="_lookup"}e={as:[n]}}return Object.assign(Object.assign({type:"lookup",from:this.secondary,key:this.transform.from.key,fields:[this.transform.lookup]},e),this.transform.default?{default:this.transform.default}:{})}}var hx=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};class bx extends ep{clone(){return new bx(null,b(this.transform))}constructor(e,n){var t,i,r;super(e);this.transform=n;this.transform=b(n);const s=(t=this.transform.as)!==null&&t!==void 0?t:[undefined,undefined];this.transform.as=[(i=s[0])!==null&&i!==void 0?i:"prob",(r=s[1])!==null&&r!==void 0?r:"value"]}dependentFields(){var e;return new Set([this.transform.quantile,...(e=this.transform.groupby)!==null&&e!==void 0?e:[]])}producedFields(){return new Set(this.transform.as)}hash(){return`QuantileTransform ${w(this.transform)}`}assemble(){const e=this.transform,{quantile:n}=e,t=hx(e,["quantile"]);const i=Object.assign({type:"quantile",field:n},t);return i}}var yx=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};class vx extends ep{clone(){return new vx(null,b(this.transform))}constructor(e,n){var t,i,r;super(e);this.transform=n;this.transform=b(n);const s=(t=this.transform.as)!==null&&t!==void 0?t:[undefined,undefined];this.transform.as=[(i=s[0])!==null&&i!==void 0?i:n.on,(r=s[1])!==null&&r!==void 0?r:n.regression]}dependentFields(){var e;return new Set([this.transform.regression,this.transform.on,...(e=this.transform.groupby)!==null&&e!==void 0?e:[]])}producedFields(){return new Set(this.transform.as)}hash(){return`RegressionTransform ${w(this.transform)}`}assemble(){const e=this.transform,{regression:n,on:t}=e,i=yx(e,["regression","on"]);const r=Object.assign({type:"regression",x:t,y:n},i);return r}}class Ox extends ep{clone(){return new Ox(null,b(this.transform))}constructor(e,n){super(e);this.transform=n}addDimensions(e){var n;this.transform.groupby=C(((n=this.transform.groupby)!==null&&n!==void 0?n:[]).concat(e),(e=>e))}producedFields(){return undefined}dependentFields(){var e;return new Set([this.transform.pivot,this.transform.value,...(e=this.transform.groupby)!==null&&e!==void 0?e:[]])}hash(){return`PivotTransform ${w(this.transform)}`}assemble(){const{pivot:e,value:n,groupby:t,limit:i,op:r}=this.transform;return Object.assign(Object.assign(Object.assign({type:"pivot",field:e,value:n},i!==undefined?{limit:i}:{}),r!==undefined?{op:r}:{}),t!==undefined?{groupby:t}:{})}}class xx extends ep{clone(){return new xx(null,b(this.transform))}constructor(e,n){super(e);this.transform=n}dependentFields(){return new Set}producedFields(){return new Set}hash(){return`SampleTransform ${w(this.transform)}`}assemble(){return{type:"sample",size:this.transform.sample}}}function wx(e){let n=0;function t(i,r){var s;if(i instanceof pv){if(!i.isGenerator&&!zd(i.data)){e.push(r);const n={name:null,source:r.name,transform:[]};r=n}}if(i instanceof cv){if(i.parent instanceof pv&&!r.source){r.format=Object.assign(Object.assign({},(s=r.format)!==null&&s!==void 0?s:{}),{parse:i.assembleFormatParse()});r.transform.push(...i.assembleTransforms(true))}else{r.transform.push(...i.assembleTransforms())}}if(i instanceof iv){if(!r.name){r.name=`data_${n++}`}if(!r.source||r.transform.length>0){e.push(r);i.data=r.name}else{i.data=r.source}e.push(...i.assemble());return}if(i instanceof fv||i instanceof dv||i instanceof ax||i instanceof Zh||i instanceof Cb||i instanceof fx||i instanceof tv||i instanceof mx||i instanceof Nv||i instanceof Bv||i instanceof cx||i instanceof ux||i instanceof ox||i instanceof gx||i instanceof bx||i instanceof vx||i instanceof lv||i instanceof xx||i instanceof Ox){r.transform.push(i.assemble())}if(i instanceof Zy||i instanceof ip||i instanceof dx||i instanceof zv||i instanceof lx){r.transform.push(...i.assemble())}if(i instanceof np){if(r.source&&r.transform.length===0){i.setSource(r.source)}else if(i.parent instanceof np){i.setSource(r.name)}else{if(!r.name){r.name=`data_${n++}`}i.setSource(r.name);if(i.numChildren()===1){e.push(r);const n={name:null,source:r.name,transform:[]};r=n}}}switch(i.numChildren()){case 0:if(i instanceof np&&(!r.source||r.transform.length>0)){e.push(r)}break;case 1:t(i.children[0],r);break;default:{if(!r.name){r.name=`data_${n++}`}let s=r.name;if(!r.source||r.transform.length>0){e.push(r)}else{s=r.source}for(const e of i.children){const n={name:null,source:s,transform:[]};t(e,n)}break}}}return t}function jx(e){const n=[];const t=wx(n);for(const i of e.children){t(i,{source:e.name,name:null,transform:[]})}return n}function Fx(e,n){var t,i;const r=[];const s=wx(r);let o=0;for(const u of e.sources){if(!u.hasName()){u.dataName=`source_${o++}`}const e=u.assemble();s(u,e)}for(const u of r){if(u.transform.length===0){delete u.transform}}let a=0;for(const[u,c]of r.entries()){if(((t=c.transform)!==null&&t!==void 0?t:[]).length===0&&!c.source){r.splice(a++,0,r.splice(u,1)[0])}}for(const u of r){for(const n of(i=u.transform)!==null&&i!==void 0?i:[]){if(n.type==="lookup"){n.from=e.outputNodes[n.from].getSource()}}}for(const u of r){if(u.name in n){u.values=n[u.name]}}return r}function $x(e){if(e==="top"||e==="left"||Tt(e)){return"header"}return"footer"}function Dx(e){for(const n of Je){Ax(e,n)}Cx(e,"x");Cx(e,"y")}function Ax(e,n){var t;const{facet:i,config:s,child:o,component:a}=e;if(e.channelHasField(n)){const u=i[n];const c=Bb("title",null,s,n);let l=Nu(u,s,{allowDisabling:true,includeDefault:c===undefined||!!c});if(o.component.layoutHeaders[n].title){l=(0,r.cy)(l)?l.join(", "):l;l+=` / ${o.component.layoutHeaders[n].title}`;o.component.layoutHeaders[n].title=null}const f=Bb("labelOrient",u.header,s,n);const d=u.header!==null?X((t=u.header)===null||t===void 0?void 0:t.labels,s.header.labels,true):false;const p=F(["bottom","right"],f)?"footer":"header";a.layoutHeaders[n]={title:u.header!==null?l:null,facetFieldDef:u,[p]:n==="facet"?[]:[kx(e,n,d)]}}}function kx(e,n,t){const i=n==="row"?"height":"width";return{labels:t,sizeSignal:e.child.component.layoutSize.get(i)?e.child.getSizeSignalRef(i):undefined,axes:[]}}function Cx(e,n){var t;const{child:i}=e;if(i.component.axes[n]){const{layoutHeaders:r,resolve:s}=e.component;s.axis[n]=ey(s,n);if(s.axis[n]==="shared"){const s=n==="x"?"column":"row";const o=r[s];for(const r of i.component.axes[n]){const n=$x(r.get("orient"));(t=o[n])!==null&&t!==void 0?t:o[n]=[kx(e,s,false)];const i=ub(r,"main",e.config,{header:true});if(i){o[n][0].axes.push(i)}r.mainExtracted=true}}else{}}}function Sx(e){Bx(e);Px(e,"width");Px(e,"height")}function Ex(e){Bx(e);const n=e.layout.columns===1?"width":"childWidth";const t=e.layout.columns===undefined?"height":"childHeight";Px(e,n);Px(e,t)}function Bx(e){for(const n of e.children){n.parseLayoutSize()}}function Px(e,n){var t;const i=Xb(n);const r=Hn(i);const s=e.component.resolve;const o=e.component.layoutSize;let a;for(const u of e.children){const n=u.component.layoutSize.getWithExplicit(i);const o=(t=s.scale[r])!==null&&t!==void 0?t:Zb(r,e);if(o==="independent"&&n.value==="step"){a=undefined;break}if(a){if(o==="independent"&&a.value!==n.value){a=undefined;break}a=Pd(a,n,i,"")}else{a=n}}if(a){for(const t of e.children){e.renameSignal(t.getName(i),e.getName(n));t.component.layoutSize.set(i,"merged",false)}o.setWithExplicit(n,a)}else{o.setWithExplicit(n,{explicit:false,value:undefined})}}function _x(e){const{size:n,component:t}=e;for(const i of Un){const r=vn(i);if(n[r]){const e=n[r];t.layoutSize.set(r,Bl(e)?"step":e,true)}else{const n=zx(e,r);t.layoutSize.set(r,n,false)}}}function zx(e,n){const t=n==="width"?"x":"y";const i=e.config;const r=e.getScaleComponent(t);if(r){const e=r.get("type");const t=r.get("range");if(mo(e)){const e=Ul(i.view,n);if(Mt(t)||Bl(e)){return"step"}else{return e}}else{return Ll(i.view,n)}}else if(e.hasProjection||e.mark==="arc"){return Ll(i.view,n)}else{const e=Ul(i.view,n);return Bl(e)?e.step:e}}function Nx(e,n,t){return Du(n,Object.assign({suffix:`by_${Du(e)}`},t!==null&&t!==void 0?t:{}))}class Tx extends rx{constructor(e,n,t,i){super(e,"facet",n,t,i,e.resolve);this.child=Uw(e.spec,this,this.getName("child"),undefined,i);this.children=[this.child];this.facet=this.initFacet(e.facet)}initFacet(e){if(!Qa(e)){return{facet:this.initFacetFieldDef(e,"facet")}}const n=N(e);const t={};for(const i of n){if(![oe,ae].includes(i)){Vr(Qi(i,"facet"));break}const n=e[i];if(n.field===undefined){Vr(Ki(n,i));break}t[i]=this.initFacetFieldDef(n,i)}return t}initFacetFieldDef(e,n){const t=Gu(e,n);if(t.header){t.header=Pt(t.header)}else if(t.header===null){t.header=null}return t}channelHasField(e){return!!this.facet[e]}fieldDef(e){return this.facet[e]}parseData(){this.component.data=Rx(this);this.child.parseData()}parseLayoutSize(){Bx(this)}parseSelections(){this.child.parseSelections();this.component.selection=this.child.component.selection}parseMarkGroup(){this.child.parseMarkGroup()}parseAxesAndHeaders(){this.child.parseAxesAndHeaders();Dx(this)}assembleSelectionTopLevelSignals(e){return this.child.assembleSelectionTopLevelSignals(e)}assembleSignals(){this.child.assembleSignals();return[]}assembleSelectionData(e){return this.child.assembleSelectionData(e)}getHeaderLayoutMixins(){var e,n,t;const i={};for(const r of Je){for(const s of zb){const o=this.component.layoutHeaders[r];const a=o[s];const{facetFieldDef:u}=o;if(u){const n=Bb("titleOrient",u.header,this.config,r);if(["right","bottom"].includes(n)){const t=Eb(r,n);(e=i.titleAnchor)!==null&&e!==void 0?e:i.titleAnchor={};i.titleAnchor[t]="end"}}if(a===null||a===void 0?void 0:a[0]){const e=r==="row"?"height":"width";const a=s==="header"?"headerBand":"footerBand";if(r!=="facet"&&!this.child.component.layoutSize.get(e)){(n=i[a])!==null&&n!==void 0?n:i[a]={};i[a][r]=.5}if(o.title){(t=i.offset)!==null&&t!==void 0?t:i.offset={};i.offset[r==="row"?"rowTitle":"columnTitle"]=10}}}}return i}assembleDefaultLayout(){const{column:e,row:n}=this.facet;const t=e?this.columnDistinctSignal():n?1:undefined;let i="all";if(!n&&this.component.resolve.scale.x==="independent"){i="none"}else if(!e&&this.component.resolve.scale.y==="independent"){i="none"}return Object.assign(Object.assign(Object.assign({},this.getHeaderLayoutMixins()),t?{columns:t}:{}),{bounds:"full",align:i})}assembleLayoutSignals(){return this.child.assembleLayoutSignals()}columnDistinctSignal(){if(this.parent&&this.parent instanceof Tx){return undefined}else{const e=this.getName("column_domain");return{signal:`length(data('${e}'))`}}}assembleGroupStyle(){return undefined}assembleGroup(e){if(this.parent&&this.parent instanceof Tx){return Object.assign(Object.assign({},this.channelHasField("column")?{encode:{update:{columns:{field:Du(this.facet.column,{prefix:"distinct"})}}}}:{}),super.assembleGroup(e))}return super.assembleGroup(e)}getCardinalityAggregateForChild(){const e=[];const n=[];const t=[];if(this.child instanceof Tx){if(this.child.channelHasField("column")){const i=Du(this.child.facet.column);e.push(i);n.push("distinct");t.push(`distinct_${i}`)}}else{for(const i of Un){const r=this.child.component.scales[i];if(r&&!r.merged){const s=r.get("type");const o=r.get("range");if(mo(s)&&Mt(o)){const r=cO(this.child,i);const s=uO(r);if(s){e.push(s);n.push("distinct");t.push(`distinct_${s}`)}else{Vr(hi(i))}}}}}return{fields:e,ops:n,as:t}}assembleFacet(){const{name:e,data:n}=this.component.data.facetRoot;const{row:t,column:i}=this.facet;const{fields:s,ops:o,as:a}=this.getCardinalityAggregateForChild();const u=[];for(const l of Je){const e=this.facet[l];if(e){u.push(Du(e));const{bin:n,sort:c}=e;if(At(n)){u.push(Du(e,{binSuffix:"end"}))}if(Ka(c)){const{field:n,op:r=Wa}=c;const u=Nx(e,c);if(t&&i){s.push(u);o.push("max");a.push(u)}else{s.push(n);o.push(r);a.push(u)}}else if((0,r.cy)(c)){const n=Sb(e,l);s.push(n);o.push("max");a.push(n)}}}const c=!!t&&!!i;return Object.assign({name:e,data:n,groupby:u},c||s.length>0?{aggregate:Object.assign(Object.assign({},c?{cross:c}:{}),s.length?{fields:s,ops:o,as:a}:{})}:{})}facetSortFields(e){const{facet:n}=this;const t=n[e];if(t){if(Ka(t.sort)){return[Nx(t,t.sort,{expr:"datum"})]}else if((0,r.cy)(t.sort)){return[Sb(t,e,{expr:"datum"})]}return[Du(t,{expr:"datum"})]}return[]}facetSortOrder(e){const{facet:n}=this;const t=n[e];if(t){const{sort:e}=t;const n=(Ka(e)?e.order:!(0,r.cy)(e)&&e)||"ascending";return[n]}return[]}assembleLabelTitle(){var e;const{facet:n,config:t}=this;if(n.facet){return Ub(n.facet,"facet",t)}const i={row:["top","bottom"],column:["left","right"]};for(const r of _b){if(n[r]){const s=Bb("labelOrient",(e=n[r])===null||e===void 0?void 0:e.header,t,r);if(i[r].includes(s)){return Ub(n[r],r,t)}}}return undefined}assembleMarks(){const{child:e}=this;const n=this.component.data.facetRoot;const t=jx(n);const i=e.assembleGroupEncodeEntry(false);const r=this.assembleLabelTitle()||e.assembleTitle();const s=e.assembleGroupStyle();const o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({name:this.getName("cell"),type:"group"},r?{title:r}:{}),s?{style:s}:{}),{from:{facet:this.assembleFacet()},sort:{field:Je.map((e=>this.facetSortFields(e))).flat(),order:Je.map((e=>this.facetSortOrder(e))).flat()}}),t.length>0?{data:t}:{}),i?{encode:{update:i}}:{}),e.assembleGroup(Yd(this,[])));return[o]}getMapping(){return this.facet}}function Mx(e,n){const{row:t,column:i}=n;if(t&&i){let n=null;for(const r of[t,i]){if(Ka(r.sort)){const{field:t,op:i=Wa}=r.sort;e=n=new Bv(e,{joinaggregate:[{op:i,field:t,as:Nx(r,r.sort,{forAs:true})}],groupby:[Du(r)]})}}return n}return null}function Lx(e,n){var t,i,r,s;for(const o of n){const n=o.data;if(e.name&&o.hasName()&&e.name!==o.dataName){continue}const a=(t=e["format"])===null||t===void 0?void 0:t.mesh;const u=(i=n.format)===null||i===void 0?void 0:i.feature;if(a&&u){continue}const c=(r=e["format"])===null||r===void 0?void 0:r.feature;if((c||u)&&c!==u){continue}const l=(s=n.format)===null||s===void 0?void 0:s.mesh;if((a||l)&&a!==l){continue}if(Nd(e)&&Nd(n)){if(h(e.values,n.values)){return o}}else if(zd(e)&&zd(n)){if(e.url===n.url){return o}}else if(Td(e)){if(e.name===o.dataName){return o}}}return null}function qx(e,n){if(e.data||!e.parent){if(e.data===null){const e=new pv({values:[]});n.push(e);return e}const t=Lx(e.data,n);if(t){if(!Md(e.data)){t.data.format=A({},e.data.format,t.data.format)}if(!t.hasName()&&e.data.name){t.dataName=e.data.name}return t}else{const t=new pv(e.data);n.push(t);return t}}else{return e.parent.component.data.facetRoot?e.parent.component.data.facetRoot:e.parent.component.data.main}}function Ux(e,n,t){var i,r;let s=0;for(const o of n.transforms){let a=undefined;let u;if(nd(o)){u=e=new Cb(e,o);a="derived"}else if(Uf(o)){const r=ov(o);u=e=(i=cv.makeWithAncestors(e,{},r,t))!==null&&i!==void 0?i:e;e=new Zh(e,n,o.filter)}else if(td(o)){u=e=Zy.makeFromTransform(e,o,n);a="number"}else if(rd(o)){a="date";const n=t.getWithExplicit(o.field);if(n.value===undefined){e=new cv(e,{[o.field]:a});t.set(o.field,a,false)}u=e=ip.makeFromTransform(e,o)}else if(sd(o)){u=e=tv.makeFromTransform(e,o);a="number";if(Tg(n)){e=new lv(e)}}else if(If(o)){u=e=mx.make(e,n,o,s++);a="derived"}else if(Jf(o)){u=e=new Nv(e,o);a="number"}else if(Zf(o)){u=e=new Bv(e,o);a="number"}else if(od(o)){u=e=zv.makeFromTransform(e,o);a="derived"}else if(ad(o)){u=e=new cx(e,o);a="derived"}else if(ed(o)){u=e=new ux(e,o);a="derived"}else if(Gf(o)){u=e=new Ox(e,o);a="derived"}else if(Xf(o)){e=new xx(e,o)}else if(id(o)){u=e=dx.makeFromTransform(e,o);a="derived"}else if(Yf(o)){u=e=new ox(e,o);a="derived"}else if(Kf(o)){u=e=new bx(e,o);a="derived"}else if(Vf(o)){u=e=new vx(e,o);a="derived"}else if(Qf(o)){u=e=new gx(e,o);a="derived"}else{Vr(_i(o));continue}if(u&&a!==undefined){for(const e of(r=u.producedFields())!==null&&r!==void 0?r:[]){t.set(e,a,false)}}}return e}function Rx(e){var n,t,i,r,s,o,a,u,c,l;let f=qx(e,e.component.data.sources);const{outputNodes:d,outputNodeRefCounts:p}=e.component.data;const g=e.data;const m=g&&(Md(g)||zd(g)||Nd(g));const h=!m&&e.parent?e.parent.component.data.ancestorParse.clone():new _d;if(Md(g)){if(Ld(g)){f=new dv(f,g.sequence)}else if(Ud(g)){f=new fv(f,g.graticule)}h.parseNothing=true}else if(((n=g===null||g===void 0?void 0:g.format)===null||n===void 0?void 0:n.parse)===null){h.parseNothing=true}f=(t=cv.makeExplicit(f,e,h))!==null&&t!==void 0?t:f;f=new lv(f);const b=e.parent&&tx(e.parent);if(ZO(e)||ex(e)){if(b){f=(i=Zy.makeFromEncoding(f,e))!==null&&i!==void 0?i:f}}if(e.transforms.length>0){f=Ux(f,e,h)}const y=uv(e);const v=av(e);f=(r=cv.makeWithAncestors(f,{},Object.assign(Object.assign({},y),v),h))!==null&&r!==void 0?r:f;if(ZO(e)){f=lx.parseAll(f,e);f=fx.parseAll(f,e)}if(ZO(e)||ex(e)){if(!b){f=(s=Zy.makeFromEncoding(f,e))!==null&&s!==void 0?s:f}f=(o=ip.makeFromEncoding(f,e))!==null&&o!==void 0?o:f;f=Cb.parseAllForSortIndex(f,e)}const O=e.getDataName(Rd.Raw);const x=new np(f,O,Rd.Raw,p);d[O]=x;f=x;if(ZO(e)){const n=tv.makeFromEncoding(f,e);if(n){f=n;if(Tg(e)){f=new lv(f)}}f=(a=dx.makeFromEncoding(f,e))!==null&&a!==void 0?a:f;f=(u=zv.makeFromEncoding(f,e))!==null&&u!==void 0?u:f}if(ZO(e)){f=(c=ax.make(f,e))!==null&&c!==void 0?c:f}const w=e.getDataName(Rd.Main);const j=new np(f,w,Rd.Main,p);d[w]=j;f=j;if(ZO(e)){ib(e,j)}let F=null;if(ex(e)){const n=e.getName("facet");f=(l=Mx(f,e.facet))!==null&&l!==void 0?l:f;F=new iv(f,e,n,j.getSource());d[n]=F}return Object.assign(Object.assign({},e.component.data),{outputNodes:d,outputNodeRefCounts:p,raw:x,main:j,facetRoot:F,ancestorParse:h})}class Ix extends ix{constructor(e,n,t,i){var r,s,o,a;super(e,"concat",n,t,i,e.resolve);if(((s=(r=e.resolve)===null||r===void 0?void 0:r.axis)===null||s===void 0?void 0:s.x)==="shared"||((a=(o=e.resolve)===null||o===void 0?void 0:o.axis)===null||a===void 0?void 0:a.y)==="shared"){Vr(Si)}this.children=this.getChildren(e).map(((e,n)=>Uw(e,this,this.getName(`concat_${n}`),undefined,i)))}parseData(){this.component.data=Rx(this);for(const e of this.children){e.parseData()}}parseSelections(){this.component.selection={};for(const e of this.children){e.parseSelections();for(const n of N(e.component.selection)){this.component.selection[n]=e.component.selection[n]}}}parseMarkGroup(){for(const e of this.children){e.parseMarkGroup()}}parseAxesAndHeaders(){for(const e of this.children){e.parseAxesAndHeaders()}}getChildren(e){if(Cl(e)){return e.vconcat}else if(Sl(e)){return e.hconcat}return e.concat}parseLayoutSize(){Ex(this)}parseAxisGroup(){return null}assembleSelectionTopLevelSignals(e){return this.children.reduce(((e,n)=>n.assembleSelectionTopLevelSignals(e)),e)}assembleSignals(){this.children.forEach((e=>e.assembleSignals()));return[]}assembleLayoutSignals(){const e=Yb(this);for(const n of this.children){e.push(...n.assembleLayoutSignals())}return e}assembleSelectionData(e){return this.children.reduce(((e,n)=>n.assembleSelectionData(e)),e)}assembleMarks(){return this.children.map((e=>{const n=e.assembleTitle();const t=e.assembleGroupStyle();const i=e.assembleGroupEncodeEntry(false);return Object.assign(Object.assign(Object.assign(Object.assign({type:"group",name:e.getName("group")},n?{title:n}:{}),t?{style:t}:{}),i?{encode:{update:i}}:{}),e.assembleGroup())}))}assembleGroupStyle(){return undefined}assembleDefaultLayout(){const e=this.layout.columns;return Object.assign(Object.assign({},e!=null?{columns:e}:{}),{bounds:"full",align:"each"})}}function Wx(e){return e===false||e===null}const Hx=Object.assign(Object.assign({disable:1,gridScale:1,scale:1},sc),{labelExpr:1,encode:1});const Gx=N(Hx);class Yx extends kd{constructor(e={},n={},t=false){super();this.explicit=e;this.implicit=n;this.mainExtracted=t}clone(){return new Yx(b(this.explicit),b(this.implicit),this.mainExtracted)}hasAxisPart(e){if(e==="axis"){return true}if(e==="grid"||e==="title"){return!!this.get(e)}return!Wx(this.get(e))}hasOrientSignalRef(){return Tt(this.explicit.orient)}}function Kx(e,n,t){var i;const{encoding:r,config:s}=e;const o=(i=Ru(r[n]))!==null&&i!==void 0?i:Ru(r[yn(n)]);const a=e.axis(n)||{};const{format:u,formatType:c}=a;if(Sa(c)){return Object.assign({text:za({fieldOrDatumDef:o,field:"datum.value",format:u,formatType:c,config:s})},t)}else if(u===undefined&&c===undefined&&s.customFormatTypes){if(du(o)==="quantitative"){if(xu(o)&&o.stack==="normalize"&&s.normalizedNumberFormatType){return Object.assign({text:za({fieldOrDatumDef:o,field:"datum.value",format:s.normalizedNumberFormat,formatType:s.normalizedNumberFormatType,config:s})},t)}else if(s.numberFormatType){return Object.assign({text:za({fieldOrDatumDef:o,field:"datum.value",format:s.numberFormat,formatType:s.numberFormatType,config:s})},t)}}if(du(o)==="temporal"&&s.timeFormatType&&fu(o)&&!o.timeUnit){return Object.assign({text:za({fieldOrDatumDef:o,field:"datum.value",format:s.timeFormat,formatType:s.timeFormatType,config:s})},t)}}return t}function Vx(e){return Un.reduce(((n,t)=>{if(e.component.scales[t]){n[t]=[tw(t,e)]}return n}),{})}const Qx={bottom:"top",top:"bottom",left:"right",right:"left"};function Xx(e){var n;const{axes:t,resolve:i}=e.component;const r={top:0,bottom:0,right:0,left:0};for(const s of e.children){s.parseAxesAndHeaders();for(const n of N(s.component.axes)){i.axis[n]=ey(e.component.resolve,n);if(i.axis[n]==="shared"){t[n]=Jx(t[n],s.component.axes[n]);if(!t[n]){i.axis[n]="independent";delete t[n]}}}}for(const s of Un){for(const o of e.children){if(!o.component.axes[s]){continue}if(i.axis[s]==="independent"){t[s]=((n=t[s])!==null&&n!==void 0?n:[]).concat(o.component.axes[s]);for(const e of o.component.axes[s]){const{value:n,explicit:t}=e.getWithExplicit("orient");if(Tt(n)){continue}if(r[n]>0&&!t){const t=Qx[n];if(r[n]>r[t]){e.set("orient",t,false)}}r[n]++}}delete o.component.axes[s]}if(i.axis[s]==="independent"&&t[s]&&t[s].length>1){for(const e of t[s]){if(!!e.get("grid")&&!e.explicit.grid){e.implicit.grid=false}}}}}function Jx(e,n){if(e){if(e.length!==n.length){return undefined}const t=e.length;for(let i=0;i<t;i++){const t=e[i];const r=n[i];if(!!t!==!!r){return undefined}else if(t&&r){const n=t.getWithExplicit("orient");const s=r.getWithExplicit("orient");if(n.explicit&&s.explicit&&n.value!==s.value){return undefined}else{e[i]=Zx(t,r)}}}}else{return n.map((e=>e.clone()))}return e}function Zx(e,n){for(const t of Gx){const i=Pd(e.getWithExplicit(t),n.getWithExplicit(t),t,"axis",((e,n)=>{switch(t){case"title":return li(e,n);case"gridScale":return{explicit:e.explicit,value:X(e.value,n.value)}}return Bd(e,n,t,"axis")}));e.setWithExplicit(t,i)}return e}function ew(e,n,t,i,r){if(n==="disable"){return t!==undefined}t=t||{};switch(n){case"titleAngle":case"labelAngle":return e===(Tt(t.labelAngle)?t.labelAngle:ie(t.labelAngle));case"values":return!!t.values;case"encode":return!!t.encoding||!!t.labelAngle;case"title":if(e===Db(i,r)){return true}}return e===t[n]}const nw=new Set(["grid","translate","format","formatType","orient","labelExpr","tickCount","position","tickMinStep"]);function tw(e,n){var t,i,r;let s=n.axis(e);const o=new Yx;const a=Ru(n.encoding[e]);const{mark:u,config:c}=n;const l=(s===null||s===void 0?void 0:s.orient)||((t=c[e==="x"?"axisX":"axisY"])===null||t===void 0?void 0:t.orient)||((i=c.axis)===null||i===void 0?void 0:i.orient)||Fb(e);const f=n.getScaleComponent(e).get("type");const d=db(e,f,l,n.config);const p=s!==undefined?!s:gb("disable",c.style,s===null||s===void 0?void 0:s.style,d).configValue;o.set("disable",p,s!==undefined);if(p){return o}s=s||{};const g=yb(a,s,e,c.style,d);const m={fieldOrDatumDef:a,axis:s,channel:e,model:n,scaleType:f,orient:l,labelAngle:g,mark:u,config:c};for(const y of Gx){const t=y in mb?mb[y](m):ac(y)?s[y]:undefined;const i=t!==undefined;const r=ew(t,y,s,n,e);if(i&&r){o.set(y,t,r)}else{const{configValue:e=undefined,configFrom:n=undefined}=ac(y)&&y!=="values"?gb(y,c.style,s.style,d):{};const a=e!==undefined;if(i&&!a){o.set(y,t,r)}else if(!(n==="vgAxisConfig")||nw.has(y)&&a||tc(e)||Tt(e)){o.set(y,e,false)}}}const h=(r=s.encoding)!==null&&r!==void 0?r:{};const b=ic.reduce(((t,i)=>{var r;if(!o.hasAxisPart(i)){return t}const s=Jb((r=h[i])!==null&&r!==void 0?r:{},n);const a=i==="labels"?Kx(n,e,s):s;if(a!==undefined&&!z(a)){t[i]={update:a}}return t}),{});if(!z(b)){o.set("encode",b,!!s.encoding||s.labelAngle!==undefined)}return o}function iw({encoding:e,size:n}){for(const t of Un){const i=vn(t);if(Bl(n[i])){if(gu(e[t])){delete n[i];Vr(br(i))}}}return n}function rw(e,n,t){const i=Pt(e);const r=ii("orient",i,t);i.orient=uw(i.type,n,r);if(r!==undefined&&r!==i.orient){Vr(sr(i.orient,r))}if(i.type==="bar"&&i.orient){const e=ii("cornerRadiusEnd",i,t);if(e!==undefined){const t=i.orient==="horizontal"&&n.x2||i.orient==="vertical"&&n.y2?["cornerRadius"]:ma[i.orient];for(const n of t){i[n]=e}if(i.cornerRadiusEnd!==undefined){delete i.cornerRadiusEnd}}}const s=ii("opacity",i,t);if(s===undefined){i.opacity=ow(i.type,n)}const o=ii("cursor",i,t);if(o===undefined){i.cursor=sw(i,n,t)}return i}function sw(e,n,t){if(n.href||e.href||ii("href",e,t)){return"pointer"}return e.cursor}function ow(e,n){if(F([Wo,Ko,Qo,Xo],e)){if(!bc(n)){return.7}}return undefined}function aw(e,n,{graticule:t}){if(t){return false}const i=ri("filled",e,n);const r=e.type;return X(i,r!==Wo&&r!==Io&&r!==Go)}function uw(e,n,t){switch(e){case Wo:case Qo:case Xo:case Yo:case Ho:case Ro:return undefined}const{x:i,y:r,x2:s,y2:o}=n;switch(e){case Uo:if(fu(i)&&(kt(i.bin)||fu(r)&&r.aggregate&&!i.aggregate)){return"vertical"}if(fu(r)&&(kt(r.bin)||fu(i)&&i.aggregate&&!r.aggregate)){return"horizontal"}if(o||s){if(t){return t}if(!s){if(fu(i)&&i.type===Ys&&!At(i.bin)||hu(i)){if(fu(r)&&kt(r.bin)){return"horizontal"}}return"vertical"}if(!o){if(fu(r)&&r.type===Ys&&!At(r.bin)||hu(r)){if(fu(i)&&kt(i.bin)){return"vertical"}}return"horizontal"}}case Go:if(s&&!(fu(i)&&kt(i.bin))&&o&&!(fu(r)&&kt(r.bin))){return undefined}case qo:if(o){if(fu(r)&&kt(r.bin)){return"horizontal"}else{return"vertical"}}else if(s){if(fu(i)&&kt(i.bin)){return"vertical"}else{return"horizontal"}}else if(e===Go){if(i&&!r){return"vertical"}else if(r&&!i){return"horizontal"}}case Io:case Ko:{const n=gu(i);const s=gu(r);if(t){return t}else if(n&&!s){return e!=="tick"?"horizontal":"vertical"}else if(!n&&s){return e!=="tick"?"vertical":"horizontal"}else if(n&&s){const n=i;const t=r;const s=n.type===Vs;const o=t.type===Vs;if(s&&!o){return e!=="tick"?"vertical":"horizontal"}else if(!s&&o){return e!=="tick"?"horizontal":"vertical"}if(!n.aggregate&&t.aggregate){return e!=="tick"?"vertical":"horizontal"}else if(n.aggregate&&!t.aggregate){return e!=="tick"?"horizontal":"vertical"}return"vertical"}else{return undefined}}}return"vertical"}const cw={vgMark:"arc",encodeEntry:e=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"})),zp("x",e,{defaultPos:"mid"})),zp("y",e,{defaultPos:"mid"})),Gp(e,"radius")),Gp(e,"theta"))};const lw={vgMark:"area",encodeEntry:e=>Object.assign(Object.assign(Object.assign(Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"include",orient:"include",size:"ignore",theta:"ignore"})),Up("x",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="horizontal"})),Up("y",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="vertical"})),ig(e))};const fw={vgMark:"rect",encodeEntry:e=>Object.assign(Object.assign(Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"})),Gp(e,"x")),Gp(e,"y"))};const dw={vgMark:"shape",encodeEntry:e=>Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"})),postEncodingTransform:e=>{const{encoding:n}=e;const t=n.shape;const i=Object.assign({type:"geoshape",projection:e.projectionName()},t&&fu(t)&&t.type===Xs?{field:Du(t,{expr:"datum"})}:{});return[i]}};const pw={vgMark:"image",encodeEntry:e=>Object.assign(Object.assign(Object.assign(Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"ignore",orient:"ignore",size:"ignore",theta:"ignore"})),Gp(e,"x")),Gp(e,"y")),jp(e,"url"))};const gw={vgMark:"line",encodeEntry:e=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"})),zp("x",e,{defaultPos:"mid"})),zp("y",e,{defaultPos:"mid"})),Ep("size",e,{vgChannel:"strokeWidth"})),ig(e))};const mw={vgMark:"trail",encodeEntry:e=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"})),zp("x",e,{defaultPos:"mid"})),zp("y",e,{defaultPos:"mid"})),Ep("size",e)),ig(e))};function hw(e,n){const{config:t}=e;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"})),zp("x",e,{defaultPos:"mid"})),zp("y",e,{defaultPos:"mid"})),Ep("size",e)),Ep("angle",e)),bw(e,t,n))}function bw(e,n,t){if(t){return{shape:{value:t}}}return Ep("shape",e)}const yw={vgMark:"symbol",encodeEntry:e=>hw(e)};const vw={vgMark:"symbol",encodeEntry:e=>hw(e,"circle")};const Ow={vgMark:"symbol",encodeEntry:e=>hw(e,"square")};const xw={vgMark:"rect",encodeEntry:e=>Object.assign(Object.assign(Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"})),Gp(e,"x")),Gp(e,"y"))};const ww={vgMark:"rule",encodeEntry:e=>{const{markDef:n}=e;const t=n.orient;if(!e.encoding.x&&!e.encoding.y&&!e.encoding.latitude&&!e.encoding.longitude){return{}}return Object.assign(Object.assign(Object.assign(Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"})),Up("x",e,{defaultPos:t==="horizontal"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:t!=="vertical"})),Up("y",e,{defaultPos:t==="vertical"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:t!=="horizontal"})),Ep("size",e,{vgChannel:"strokeWidth"}))}};const jw={vgMark:"text",encodeEntry:e=>{const{config:n,encoding:t}=e;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Zp(e,{align:"include",baseline:"include",color:"include",size:"ignore",orient:"ignore",theta:"include"})),zp("x",e,{defaultPos:"mid"})),zp("y",e,{defaultPos:"mid"})),jp(e)),Ep("size",e,{vgChannel:"fontSize"})),Ep("angle",e)),sg("align",Fw(e.markDef,t,n))),sg("baseline",$w(e.markDef,t,n))),zp("radius",e,{defaultPos:null})),zp("theta",e,{defaultPos:null}))}};function Fw(e,n,t){const i=ii("align",e,t);if(i===undefined){return"center"}return undefined}function $w(e,n,t){const i=ii("baseline",e,t);if(i===undefined){return"middle"}return undefined}const Dw={vgMark:"rect",encodeEntry:e=>{const{config:n,markDef:t}=e;const i=t.orient;const r=i==="horizontal"?"width":"height";const s=i==="horizontal"?"height":"width";return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Zp(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"})),zp("x",e,{defaultPos:"mid",vgChannel:"xc"})),zp("y",e,{defaultPos:"mid",vgChannel:"yc"})),Ep("size",e,{defaultValue:Aw(e),vgChannel:r})),{[s]:Xt(ii("thickness",t,n))})}};function Aw(e){var n;const{config:t,markDef:i}=e;const{orient:s}=i;const o=s==="horizontal"?"width":"height";const a=e.getScaleComponent(s==="horizontal"?"x":"y");const u=(n=ii("size",i,t,{vgChannel:o}))!==null&&n!==void 0?n:t.tick.bandSize;if(u!==undefined){return u}else{const e=a?a.get("range"):undefined;if(e&&Mt(e)&&(0,r.Et)(e.step)){return e.step*3/4}const n=ql(t.view,o);return n*3/4}}const kw={arc:cw,area:lw,bar:fw,circle:vw,geoshape:dw,image:pw,line:gw,point:yw,rect:xw,rule:ww,square:Ow,text:jw,tick:Dw,trail:mw};function Cw(e){if(F([Io,qo,Vo],e.mark)){const n=$c(e.mark,e.encoding);if(n.length>0){return Ew(e,n)}}else if(e.mark===Uo){const n=Ht.some((n=>ii(n,e.markDef,e.config)));if(e.stack&&!e.fieldDef("size")&&n){return Pw(e)}}return zw(e)}const Sw="faceted_path_";function Ew(e,n){return[{name:e.getName("pathgroup"),type:"group",from:{facet:{name:Sw+e.requestDataName(Rd.Main),data:e.requestDataName(Rd.Main),groupby:n}},encode:{update:{width:{field:{group:"width"}},height:{field:{group:"height"}}}},marks:zw(e,{fromPrefix:Sw})}]}const Bw="stack_group_";function Pw(e){var n;const[t]=zw(e,{fromPrefix:Bw});const i=e.scaleName(e.stack.fieldChannel);const r=(n={})=>e.vgField(e.stack.fieldChannel,n);const s=(e,n)=>{const t=[r({prefix:"min",suffix:"start",expr:n}),r({prefix:"max",suffix:"start",expr:n}),r({prefix:"min",suffix:"end",expr:n}),r({prefix:"max",suffix:"end",expr:n})];return`${e}(${t.map((e=>`scale('${i}',${e})`)).join(",")})`};let o;let a;if(e.stack.fieldChannel==="x"){o=Object.assign(Object.assign({},v(t.encode.update,["y","yc","y2","height",...Ht])),{x:{signal:s("min","datum")},x2:{signal:s("max","datum")},clip:{value:true}});a={x:{field:{group:"x"},mult:-1},height:{field:{group:"height"}}};t.encode.update=Object.assign(Object.assign({},O(t.encode.update,["y","yc","y2"])),{height:{field:{group:"height"}}})}else{o=Object.assign(Object.assign({},v(t.encode.update,["x","xc","x2","width"])),{y:{signal:s("min","datum")},y2:{signal:s("max","datum")},clip:{value:true}});a={y:{field:{group:"y"},mult:-1},width:{field:{group:"width"}}};t.encode.update=Object.assign(Object.assign({},O(t.encode.update,["x","xc","x2"])),{width:{field:{group:"width"}}})}for(const l of Ht){const n=ri(l,e.markDef,e.config);if(t.encode.update[l]){o[l]=t.encode.update[l];delete t.encode.update[l]}else if(n){o[l]=Xt(n)}if(n){t.encode.update[l]={value:0}}}const u=[];if(((n=e.stack.groupbyChannels)===null||n===void 0?void 0:n.length)>0){for(const n of e.stack.groupbyChannels){const t=e.fieldDef(n);const i=Du(t);if(i){u.push(i)}if((t===null||t===void 0?void 0:t.bin)||(t===null||t===void 0?void 0:t.timeUnit)){u.push(Du(t,{binSuffix:"end"}))}}}const c=["stroke","strokeWidth","strokeJoin","strokeCap","strokeDash","strokeDashOffset","strokeMiterLimit","strokeOpacity"];o=c.reduce(((n,i)=>{if(t.encode.update[i]){return Object.assign(Object.assign({},n),{[i]:t.encode.update[i]})}else{const t=ri(i,e.markDef,e.config);if(t!==undefined){return Object.assign(Object.assign({},n),{[i]:Xt(t)})}else{return n}}}),o);if(o.stroke){o.strokeForeground={value:true};o.strokeOffset={value:0}}return[{type:"group",from:{facet:{data:e.requestDataName(Rd.Main),name:Bw+e.requestDataName(Rd.Main),groupby:u,aggregate:{fields:[r({suffix:"start"}),r({suffix:"start"}),r({suffix:"end"}),r({suffix:"end"})],ops:["min","max","min","max"]}}},encode:{update:o},marks:[{type:"group",encode:{update:a},marks:[t]}]}]}function _w(e){var n;const{encoding:t,stack:i,mark:s,markDef:o,config:a}=e;const u=t.order;if(!(0,r.cy)(u)&&vu(u)&&j(u.value)||!u&&j(ii("order",o,a))){return undefined}else if(((0,r.cy)(u)||fu(u))&&!i){return ai(u,{expr:"datum"})}else if(ea(s)){const i=o.orient==="horizontal"?"y":"x";const s=t[i];if(fu(s)){const t=s.sort;if((0,r.cy)(t)){return{field:Du(s,{prefix:i,suffix:"sort_index",expr:"datum"})}}else if(Ka(t)){return{field:Du({aggregate:bc(e.encoding)?t.op:undefined,field:t.field},{expr:"datum"})}}else if(Ya(t)){const n=e.fieldDef(t.encoding);return{field:Du(n,{expr:"datum"}),order:t.order}}else if(t===null){return undefined}else{return{field:Du(s,{binSuffix:((n=e.stack)===null||n===void 0?void 0:n.impute)?"mid":undefined,expr:"datum"})}}}return undefined}return undefined}function zw(e,n={fromPrefix:""}){const{mark:t,markDef:i,encoding:r,config:s}=e;const o=X(i.clip,Nw(e),Tw(e));const a=ti(i);const u=r.key;const c=_w(e);const l=Mw(e);const f=ii("aria",i,s);const d=kw[t].postEncodingTransform?kw[t].postEncodingTransform(e):null;return[Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({name:e.getName("marks"),type:kw[t].vgMark},o?{clip:true}:{}),a?{style:a}:{}),u?{key:u.field}:{}),c?{sort:c}:{}),l?l:{}),f===false?{aria:f}:{}),{from:{data:n.fromPrefix+e.requestDataName(Rd.Main)},encode:{update:kw[t].encodeEntry(e)}}),d?{transform:d}:{})]}function Nw(e){const n=e.getScaleComponent("x");const t=e.getScaleComponent("y");return(n===null||n===void 0?void 0:n.get("selectionExtent"))||(t===null||t===void 0?void 0:t.get("selectionExtent"))?true:undefined}function Tw(e){const n=e.component.projection;return n&&!n.isFit?true:undefined}function Mw(e){if(!e.component.selection)return null;const n=N(e.component.selection).length;let t=n;let i=e.parent;while(i&&t===0){t=N(i.component.selection).length;i=i.parent}return t?{interactive:n>0||!!e.encoding.tooltip}:null}class Lw extends rx{constructor(e,n,t,i={},r){var s;super(e,"unit",n,t,r,undefined,Pl(e)?e.view:undefined);this.specifiedScales={};this.specifiedAxes={};this.specifiedLegends={};this.specifiedProjection={};this.selection=[];this.children=[];const o=ia(e.mark)?Object.assign({},e.mark):{type:e.mark};const a=o.type;if(o.filled===undefined){o.filled=aw(o,r,{graticule:e.data&&Ud(e.data)})}const u=this.encoding=Oc(e.encoding||{},a,o.filled,r);this.markDef=rw(o,u,r);this.size=iw({encoding:u,size:Pl(e)?Object.assign(Object.assign(Object.assign({},i),e.width?{width:e.width}:{}),e.height?{height:e.height}:{}):i});this.stack=xf(a,u);this.specifiedScales=this.initScales(a,u);this.specifiedAxes=this.initAxes(u);this.specifiedLegends=this.initLegends(u);this.specifiedProjection=e.projection;this.selection=((s=e.params)!==null&&s!==void 0?s:[]).filter((e=>Fl(e)))}get hasProjection(){const{encoding:e}=this;const n=this.mark===Jo;const t=e&&Ke.some((n=>bu(e[n])));return n||t}scaleDomain(e){const n=this.specifiedScales[e];return n?n.domain:undefined}axis(e){return this.specifiedAxes[e]}legend(e){return this.specifiedLegends[e]}initScales(e,n){return ut.reduce(((e,t)=>{var i;const r=Ru(n[t]);if(r){e[t]=this.initScale((i=r.scale)!==null&&i!==void 0?i:{})}return e}),{})}initScale(e){const{domain:n,range:t}=e;const i=Pt(e);if((0,r.cy)(n)){i.domain=n.map(Vt)}if((0,r.cy)(t)){i.range=t.map(Vt)}return i}initAxes(e){return Un.reduce(((n,t)=>{const i=e[t];if(bu(i)||t===ce&&bu(e.x2)||t===le&&bu(e.y2)){const e=bu(i)?i.axis:undefined;n[t]=e?this.initAxis(Object.assign({},e)):e}return n}),{})}initAxis(e){const n=N(e);const t={};for(const i of n){const n=e[i];t[i]=tc(n)?Kt(n):Vt(n)}return t}initLegends(e){return rt.reduce(((n,t)=>{const i=Ru(e[t]);if(i&&ot(t)){const e=i.legend;n[t]=e?Pt(e):e}return n}),{})}parseData(){this.component.data=Rx(this)}parseLayoutSize(){_x(this)}parseSelections(){this.component.selection=eb(this,this.selection)}parseMarkGroup(){this.component.mark=Cw(this)}parseAxesAndHeaders(){this.component.axes=Vx(this)}assembleSelectionTopLevelSignals(e){return Kd(this,e)}assembleSignals(){return[...cb(this),...Gd(this,[])]}assembleSelectionData(e){return Vd(this,e)}assembleLayout(){return null}assembleLayoutSignals(){return Yb(this)}assembleMarks(){var e;let n=(e=this.component.mark)!==null&&e!==void 0?e:[];if(!this.parent||!tx(this.parent)){n=Qd(this,n)}return n.map(this.correctDataNames)}assembleGroupStyle(){const{style:e}=this.view||{};if(e!==undefined){return e}if(this.encoding.x||this.encoding.y){return"cell"}else{return undefined}}getMapping(){return this.encoding}get mark(){return this.markDef.type}channelHasField(e){return gc(this.encoding,e)}fieldDef(e){const n=this.encoding[e];return Uu(n)}typedFieldDef(e){const n=this.fieldDef(e);if(yu(n)){return n}return null}}class qw extends ix{constructor(e,n,t,i,r){super(e,"layer",n,t,r,e.resolve,e.view);const s=Object.assign(Object.assign(Object.assign({},i),e.width?{width:e.width}:{}),e.height?{height:e.height}:{});this.children=e.layer.map(((e,n)=>{if(cf(e)){return new qw(e,this,this.getName(`layer_${n}`),s,r)}else if(fc(e)){return new Lw(e,this,this.getName(`layer_${n}`),s,r)}throw new Error(fi(e))}))}parseData(){this.component.data=Rx(this);for(const e of this.children){e.parseData()}}parseLayoutSize(){Sx(this)}parseSelections(){this.component.selection={};for(const e of this.children){e.parseSelections();for(const n of N(e.component.selection)){this.component.selection[n]=e.component.selection[n]}}}parseMarkGroup(){for(const e of this.children){e.parseMarkGroup()}}parseAxesAndHeaders(){Xx(this)}assembleSelectionTopLevelSignals(e){return this.children.reduce(((e,n)=>n.assembleSelectionTopLevelSignals(e)),e)}assembleSignals(){return this.children.reduce(((e,n)=>e.concat(n.assembleSignals())),cb(this))}assembleLayoutSignals(){return this.children.reduce(((e,n)=>e.concat(n.assembleLayoutSignals())),Yb(this))}assembleSelectionData(e){return this.children.reduce(((e,n)=>n.assembleSelectionData(e)),e)}assembleGroupStyle(){const e=new Set;for(const t of this.children){for(const n of(0,r.YO)(t.assembleGroupStyle())){e.add(n)}}const n=Array.from(e);return n.length>1?n:n.length===1?n[0]:undefined}assembleTitle(){let e=super.assembleTitle();if(e){return e}for(const n of this.children){e=n.assembleTitle();if(e){return e}}return undefined}assembleLayout(){return null}assembleMarks(){return Xd(this,this.children.flatMap((e=>e.assembleMarks())))}assembleLegends(){return this.children.reduce(((e,n)=>e.concat(n.assembleLegends())),_y(this))}}function Uw(e,n,t,i,r){if(Ja(e)){return new Tx(e,n,t,r)}else if(cf(e)){return new qw(e,n,t,i,r)}else if(fc(e)){return new Lw(e,n,t,i,r)}else if(Al(e)){return new Ix(e,n,t,r)}throw new Error(fi(e))}var Rw=undefined&&undefined.__rest||function(e,n){var t={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0)t[i]=e[i];if(e!=null&&typeof Object.getOwnPropertySymbols==="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++){if(n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r]))t[i[r]]=e[i[r]]}return t};function Iw(e,n={}){if(n.logger){Gr(n.logger)}if(n.fieldTitle){_u(n.fieldTitle)}try{const t=nf((0,r.io)(n.config,e.config));const i=bd(e,t);const s=Uw(i,null,"",undefined,t);s.parse();Hv(s.component.data,s);const o=Hw(s,Ww(e,i.autosize,t,s),e.datasets,e.usermeta);return{spec:o,normalized:i}}finally{if(n.logger){Yr()}if(n.fieldTitle){zu()}}}function Ww(e,n,t,i){const s=i.component.layoutSize.get("width");const o=i.component.layoutSize.get("height");if(n===undefined){n={type:"pad"};if(i.hasAxisOrientSignalRef()){n.resize=true}}else if((0,r.Kg)(n)){n={type:n}}if(s&&o&&Fd(n.type)){if(s==="step"&&o==="step"){Vr(mi());n.type="pad"}else if(s==="step"||o==="step"){const e=s==="step"?"width":"height";Vr(mi(Hn(e)));const t=e==="width"?"height":"width";n.type=$d(t)}}return Object.assign(Object.assign(Object.assign({},N(n).length===1&&n.type?n.type==="pad"?{}:{autosize:n.type}:{autosize:n}),Ad(t,false)),Ad(e,true))}function Hw(e,n,t={},i){const r=e.config?of(e.config):undefined;const s=[].concat(e.assembleSelectionData([]),Fx(e.component.data,t));const o=e.assembleProjections();const a=e.assembleTitle();const u=e.assembleGroupStyle();const c=e.assembleGroupEncodeEntry(true);let l=e.assembleLayoutSignals();l=l.filter((e=>{if((e.name==="width"||e.name==="height")&&e.value!==undefined){n[e.name]=+e.value;return false}return true}));const{params:f}=n,d=Rw(n,["params"]);return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({$schema:"https://vega.github.io/schema/vega/v5.json"},e.description?{description:e.description}:{}),d),a?{title:a}:{}),u?{style:u}:{}),c?{encode:{update:c}}:{}),{data:s}),o.length>0?{projections:o}:{}),e.assembleGroup([...l,...e.assembleSelectionTopLevelSignals([]),...Dl(f)])),r?{config:r}:{}),i?{usermeta:i}:{})}const Gw=i.rE},18729:e=>{var n=function(){"use strict";function e(e,n){return n!=null&&e instanceof n}var n;try{n=Map}catch(l){n=function(){}}var t;try{t=Set}catch(l){t=function(){}}var i;try{i=Promise}catch(l){i=function(){}}function r(s,o,a,u,l){if(typeof o==="object"){a=o.depth;u=o.prototype;l=o.includeNonEnumerable;o=o.circular}var f=[];var d=[];var p=typeof Buffer!="undefined";if(typeof o=="undefined")o=true;if(typeof a=="undefined")a=Infinity;function g(s,a){if(s===null)return null;if(a===0)return s;var m;var h;if(typeof s!="object"){return s}if(e(s,n)){m=new n}else if(e(s,t)){m=new t}else if(e(s,i)){m=new i((function(e,n){s.then((function(n){e(g(n,a-1))}),(function(e){n(g(e,a-1))}))}))}else if(r.__isArray(s)){m=[]}else if(r.__isRegExp(s)){m=new RegExp(s.source,c(s));if(s.lastIndex)m.lastIndex=s.lastIndex}else if(r.__isDate(s)){m=new Date(s.getTime())}else if(p&&Buffer.isBuffer(s)){if(Buffer.allocUnsafe){m=Buffer.allocUnsafe(s.length)}else{m=new Buffer(s.length)}s.copy(m);return m}else if(e(s,Error)){m=Object.create(s)}else{if(typeof u=="undefined"){h=Object.getPrototypeOf(s);m=Object.create(h)}else{m=Object.create(u);h=u}}if(o){var b=f.indexOf(s);if(b!=-1){return d[b]}f.push(s);d.push(m)}if(e(s,n)){s.forEach((function(e,n){var t=g(n,a-1);var i=g(e,a-1);m.set(t,i)}))}if(e(s,t)){s.forEach((function(e){var n=g(e,a-1);m.add(n)}))}for(var y in s){var v;if(h){v=Object.getOwnPropertyDescriptor(h,y)}if(v&&v.set==null){continue}m[y]=g(s[y],a-1)}if(Object.getOwnPropertySymbols){var O=Object.getOwnPropertySymbols(s);for(var y=0;y<O.length;y++){var x=O[y];var w=Object.getOwnPropertyDescriptor(s,x);if(w&&!w.enumerable&&!l){continue}m[x]=g(s[x],a-1);if(!w.enumerable){Object.defineProperty(m,x,{enumerable:false})}}}if(l){var j=Object.getOwnPropertyNames(s);for(var y=0;y<j.length;y++){var F=j[y];var w=Object.getOwnPropertyDescriptor(s,F);if(w&&w.enumerable){continue}m[F]=g(s[F],a-1);Object.defineProperty(m,F,{enumerable:false})}}return m}return g(s,a)}r.clonePrototype=function e(n){if(n===null)return null;var t=function(){};t.prototype=n;return new t};function s(e){return Object.prototype.toString.call(e)}r.__objToStr=s;function o(e){return typeof e==="object"&&s(e)==="[object Date]"}r.__isDate=o;function a(e){return typeof e==="object"&&s(e)==="[object Array]"}r.__isArray=a;function u(e){return typeof e==="object"&&s(e)==="[object RegExp]"}r.__isRegExp=u;function c(e){var n="";if(e.global)n+="g";if(e.ignoreCase)n+="i";if(e.multiline)n+="m";return n}r.__getRegExpFlags=c;return r}();if(true&&e.exports){e.exports=n}}}]);