"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[4311],{4311:(t,e,i)=>{i.d(e,{diagram:()=>z});var a=i(75905);var n=i(24982);var s=function(){var t=(0,a.K2)((function(t,e,i,a){for(i=i||{},a=t.length;a--;i[t[a]]=e);return i}),"o"),e=[1,3],i=[1,4],n=[1,5],s=[1,6],r=[1,7],o=[1,4,5,10,12,13,14,18,25,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],l=[1,4,5,10,12,13,14,18,25,28,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],h=[55,56,57],c=[2,36],d=[1,37],u=[1,36],x=[1,38],f=[1,35],g=[1,43],p=[1,41],y=[1,14],b=[1,23],T=[1,18],m=[1,19],k=[1,20],q=[1,21],_=[1,22],A=[1,24],S=[1,25],F=[1,26],P=[1,27],v=[1,28],C=[1,29],L=[1,32],I=[1,33],E=[1,34],D=[1,39],z=[1,40],w=[1,42],K=[1,44],U=[1,62],N=[1,61],R=[4,5,8,10,12,13,14,18,44,47,49,55,56,57,63,64,65,66,67],B=[1,65],W=[1,66],$=[1,67],Q=[1,68],O=[1,69],X=[1,70],H=[1,71],M=[1,72],Y=[1,73],j=[1,74],G=[1,75],V=[1,76],Z=[4,5,6,7,8,9,10,11,12,13,14,15,18],J=[1,90],tt=[1,91],et=[1,92],it=[1,99],at=[1,93],nt=[1,96],st=[1,94],rt=[1,95],ot=[1,97],lt=[1,98],ht=[1,102],ct=[10,55,56,57],dt=[4,5,6,8,10,11,13,17,18,19,20,55,56,57];var ut={trace:(0,a.K2)((function t(){}),"trace"),yy:{},symbols_:{error:2,idStringToken:3,ALPHA:4,NUM:5,NODE_STRING:6,DOWN:7,MINUS:8,DEFAULT:9,COMMA:10,COLON:11,AMP:12,BRKT:13,MULT:14,UNICODE_TEXT:15,styleComponent:16,UNIT:17,SPACE:18,STYLE:19,PCT:20,idString:21,style:22,stylesOpt:23,classDefStatement:24,CLASSDEF:25,start:26,eol:27,QUADRANT:28,document:29,line:30,statement:31,axisDetails:32,quadrantDetails:33,points:34,title:35,title_value:36,acc_title:37,acc_title_value:38,acc_descr:39,acc_descr_value:40,acc_descr_multiline_value:41,section:42,text:43,point_start:44,point_x:45,point_y:46,class_name:47,"X-AXIS":48,"AXIS-TEXT-DELIMITER":49,"Y-AXIS":50,QUADRANT_1:51,QUADRANT_2:52,QUADRANT_3:53,QUADRANT_4:54,NEWLINE:55,SEMI:56,EOF:57,alphaNumToken:58,textNoTagsToken:59,STR:60,MD_STR:61,alphaNum:62,PUNCTUATION:63,PLUS:64,EQUALS:65,DOT:66,UNDERSCORE:67,$accept:0,$end:1},terminals_:{2:"error",4:"ALPHA",5:"NUM",6:"NODE_STRING",7:"DOWN",8:"MINUS",9:"DEFAULT",10:"COMMA",11:"COLON",12:"AMP",13:"BRKT",14:"MULT",15:"UNICODE_TEXT",17:"UNIT",18:"SPACE",19:"STYLE",20:"PCT",25:"CLASSDEF",28:"QUADRANT",35:"title",36:"title_value",37:"acc_title",38:"acc_title_value",39:"acc_descr",40:"acc_descr_value",41:"acc_descr_multiline_value",42:"section",44:"point_start",45:"point_x",46:"point_y",47:"class_name",48:"X-AXIS",49:"AXIS-TEXT-DELIMITER",50:"Y-AXIS",51:"QUADRANT_1",52:"QUADRANT_2",53:"QUADRANT_3",54:"QUADRANT_4",55:"NEWLINE",56:"SEMI",57:"EOF",60:"STR",61:"MD_STR",63:"PUNCTUATION",64:"PLUS",65:"EQUALS",66:"DOT",67:"UNDERSCORE"},productions_:[0,[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[21,1],[21,2],[22,1],[22,2],[23,1],[23,3],[24,5],[26,2],[26,2],[26,2],[29,0],[29,2],[30,2],[31,0],[31,1],[31,2],[31,1],[31,1],[31,1],[31,2],[31,2],[31,2],[31,1],[31,1],[34,4],[34,5],[34,5],[34,6],[32,4],[32,3],[32,2],[32,4],[32,3],[32,2],[33,2],[33,2],[33,2],[33,2],[27,1],[27,1],[27,1],[43,1],[43,2],[43,1],[43,1],[62,1],[62,2],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[59,1],[59,1],[59,1]],performAction:(0,a.K2)((function t(e,i,a,n,s,r,o){var l=r.length-1;switch(s){case 23:this.$=r[l];break;case 24:this.$=r[l-1]+""+r[l];break;case 26:this.$=r[l-1]+r[l];break;case 27:this.$=[r[l].trim()];break;case 28:r[l-2].push(r[l].trim());this.$=r[l-2];break;case 29:this.$=r[l-4];n.addClass(r[l-2],r[l]);break;case 37:this.$=[];break;case 42:this.$=r[l].trim();n.setDiagramTitle(this.$);break;case 43:this.$=r[l].trim();n.setAccTitle(this.$);break;case 44:case 45:this.$=r[l].trim();n.setAccDescription(this.$);break;case 46:n.addSection(r[l].substr(8));this.$=r[l].substr(8);break;case 47:n.addPoint(r[l-3],"",r[l-1],r[l],[]);break;case 48:n.addPoint(r[l-4],r[l-3],r[l-1],r[l],[]);break;case 49:n.addPoint(r[l-4],"",r[l-2],r[l-1],r[l]);break;case 50:n.addPoint(r[l-5],r[l-4],r[l-2],r[l-1],r[l]);break;case 51:n.setXAxisLeftText(r[l-2]);n.setXAxisRightText(r[l]);break;case 52:r[l-1].text+=" ⟶ ";n.setXAxisLeftText(r[l-1]);break;case 53:n.setXAxisLeftText(r[l]);break;case 54:n.setYAxisBottomText(r[l-2]);n.setYAxisTopText(r[l]);break;case 55:r[l-1].text+=" ⟶ ";n.setYAxisBottomText(r[l-1]);break;case 56:n.setYAxisBottomText(r[l]);break;case 57:n.setQuadrant1Text(r[l]);break;case 58:n.setQuadrant2Text(r[l]);break;case 59:n.setQuadrant3Text(r[l]);break;case 60:n.setQuadrant4Text(r[l]);break;case 64:this.$={text:r[l],type:"text"};break;case 65:this.$={text:r[l-1].text+""+r[l],type:r[l-1].type};break;case 66:this.$={text:r[l],type:"text"};break;case 67:this.$={text:r[l],type:"markdown"};break;case 68:this.$=r[l];break;case 69:this.$=r[l-1]+""+r[l];break}}),"anonymous"),table:[{18:e,26:1,27:2,28:i,55:n,56:s,57:r},{1:[3]},{18:e,26:8,27:2,28:i,55:n,56:s,57:r},{18:e,26:9,27:2,28:i,55:n,56:s,57:r},t(o,[2,33],{29:10}),t(l,[2,61]),t(l,[2,62]),t(l,[2,63]),{1:[2,30]},{1:[2,31]},t(h,c,{30:11,31:12,24:13,32:15,33:16,34:17,43:30,58:31,1:[2,32],4:d,5:u,10:x,12:f,13:g,14:p,18:y,25:b,35:T,37:m,39:k,41:q,42:_,48:A,50:S,51:F,52:P,53:v,54:C,60:L,61:I,63:E,64:D,65:z,66:w,67:K}),t(o,[2,34]),{27:45,55:n,56:s,57:r},t(h,[2,37]),t(h,c,{24:13,32:15,33:16,34:17,43:30,58:31,31:46,4:d,5:u,10:x,12:f,13:g,14:p,18:y,25:b,35:T,37:m,39:k,41:q,42:_,48:A,50:S,51:F,52:P,53:v,54:C,60:L,61:I,63:E,64:D,65:z,66:w,67:K}),t(h,[2,39]),t(h,[2,40]),t(h,[2,41]),{36:[1,47]},{38:[1,48]},{40:[1,49]},t(h,[2,45]),t(h,[2,46]),{18:[1,50]},{4:d,5:u,10:x,12:f,13:g,14:p,43:51,58:31,60:L,61:I,63:E,64:D,65:z,66:w,67:K},{4:d,5:u,10:x,12:f,13:g,14:p,43:52,58:31,60:L,61:I,63:E,64:D,65:z,66:w,67:K},{4:d,5:u,10:x,12:f,13:g,14:p,43:53,58:31,60:L,61:I,63:E,64:D,65:z,66:w,67:K},{4:d,5:u,10:x,12:f,13:g,14:p,43:54,58:31,60:L,61:I,63:E,64:D,65:z,66:w,67:K},{4:d,5:u,10:x,12:f,13:g,14:p,43:55,58:31,60:L,61:I,63:E,64:D,65:z,66:w,67:K},{4:d,5:u,10:x,12:f,13:g,14:p,43:56,58:31,60:L,61:I,63:E,64:D,65:z,66:w,67:K},{4:d,5:u,8:U,10:x,12:f,13:g,14:p,18:N,44:[1,57],47:[1,58],58:60,59:59,63:E,64:D,65:z,66:w,67:K},t(R,[2,64]),t(R,[2,66]),t(R,[2,67]),t(R,[2,70]),t(R,[2,71]),t(R,[2,72]),t(R,[2,73]),t(R,[2,74]),t(R,[2,75]),t(R,[2,76]),t(R,[2,77]),t(R,[2,78]),t(R,[2,79]),t(R,[2,80]),t(o,[2,35]),t(h,[2,38]),t(h,[2,42]),t(h,[2,43]),t(h,[2,44]),{3:64,4:B,5:W,6:$,7:Q,8:O,9:X,10:H,11:M,12:Y,13:j,14:G,15:V,21:63},t(h,[2,53],{59:59,58:60,4:d,5:u,8:U,10:x,12:f,13:g,14:p,18:N,49:[1,77],63:E,64:D,65:z,66:w,67:K}),t(h,[2,56],{59:59,58:60,4:d,5:u,8:U,10:x,12:f,13:g,14:p,18:N,49:[1,78],63:E,64:D,65:z,66:w,67:K}),t(h,[2,57],{59:59,58:60,4:d,5:u,8:U,10:x,12:f,13:g,14:p,18:N,63:E,64:D,65:z,66:w,67:K}),t(h,[2,58],{59:59,58:60,4:d,5:u,8:U,10:x,12:f,13:g,14:p,18:N,63:E,64:D,65:z,66:w,67:K}),t(h,[2,59],{59:59,58:60,4:d,5:u,8:U,10:x,12:f,13:g,14:p,18:N,63:E,64:D,65:z,66:w,67:K}),t(h,[2,60],{59:59,58:60,4:d,5:u,8:U,10:x,12:f,13:g,14:p,18:N,63:E,64:D,65:z,66:w,67:K}),{45:[1,79]},{44:[1,80]},t(R,[2,65]),t(R,[2,81]),t(R,[2,82]),t(R,[2,83]),{3:82,4:B,5:W,6:$,7:Q,8:O,9:X,10:H,11:M,12:Y,13:j,14:G,15:V,18:[1,81]},t(Z,[2,23]),t(Z,[2,1]),t(Z,[2,2]),t(Z,[2,3]),t(Z,[2,4]),t(Z,[2,5]),t(Z,[2,6]),t(Z,[2,7]),t(Z,[2,8]),t(Z,[2,9]),t(Z,[2,10]),t(Z,[2,11]),t(Z,[2,12]),t(h,[2,52],{58:31,43:83,4:d,5:u,10:x,12:f,13:g,14:p,60:L,61:I,63:E,64:D,65:z,66:w,67:K}),t(h,[2,55],{58:31,43:84,4:d,5:u,10:x,12:f,13:g,14:p,60:L,61:I,63:E,64:D,65:z,66:w,67:K}),{46:[1,85]},{45:[1,86]},{4:J,5:tt,6:et,8:it,11:at,13:nt,16:89,17:st,18:rt,19:ot,20:lt,22:88,23:87},t(Z,[2,24]),t(h,[2,51],{59:59,58:60,4:d,5:u,8:U,10:x,12:f,13:g,14:p,18:N,63:E,64:D,65:z,66:w,67:K}),t(h,[2,54],{59:59,58:60,4:d,5:u,8:U,10:x,12:f,13:g,14:p,18:N,63:E,64:D,65:z,66:w,67:K}),t(h,[2,47],{22:88,16:89,23:100,4:J,5:tt,6:et,8:it,11:at,13:nt,17:st,18:rt,19:ot,20:lt}),{46:[1,101]},t(h,[2,29],{10:ht}),t(ct,[2,27],{16:103,4:J,5:tt,6:et,8:it,11:at,13:nt,17:st,18:rt,19:ot,20:lt}),t(dt,[2,25]),t(dt,[2,13]),t(dt,[2,14]),t(dt,[2,15]),t(dt,[2,16]),t(dt,[2,17]),t(dt,[2,18]),t(dt,[2,19]),t(dt,[2,20]),t(dt,[2,21]),t(dt,[2,22]),t(h,[2,49],{10:ht}),t(h,[2,48],{22:88,16:89,23:104,4:J,5:tt,6:et,8:it,11:at,13:nt,17:st,18:rt,19:ot,20:lt}),{4:J,5:tt,6:et,8:it,11:at,13:nt,16:89,17:st,18:rt,19:ot,20:lt,22:105},t(dt,[2,26]),t(h,[2,50],{10:ht}),t(ct,[2,28],{16:103,4:J,5:tt,6:et,8:it,11:at,13:nt,17:st,18:rt,19:ot,20:lt})],defaultActions:{8:[2,30],9:[2,31]},parseError:(0,a.K2)((function t(e,i){if(i.recoverable){this.trace(e)}else{var a=new Error(e);a.hash=i;throw a}}),"parseError"),parse:(0,a.K2)((function t(e){var i=this,n=[0],s=[],r=[null],o=[],l=this.table,h="",c=0,d=0,u=0,x=2,f=1;var g=o.slice.call(arguments,1);var p=Object.create(this.lexer);var y={yy:{}};for(var b in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,b)){y.yy[b]=this.yy[b]}}p.setInput(e,y.yy);y.yy.lexer=p;y.yy.parser=this;if(typeof p.yylloc=="undefined"){p.yylloc={}}var T=p.yylloc;o.push(T);var m=p.options&&p.options.ranges;if(typeof y.yy.parseError==="function"){this.parseError=y.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function k(t){n.length=n.length-2*t;r.length=r.length-t;o.length=o.length-t}(0,a.K2)(k,"popStack");function q(){var t;t=s.pop()||p.lex()||f;if(typeof t!=="number"){if(t instanceof Array){s=t;t=s.pop()}t=i.symbols_[t]||t}return t}(0,a.K2)(q,"lex");var _,A,S,F,P,v,C={},L,I,E,D;while(true){S=n[n.length-1];if(this.defaultActions[S]){F=this.defaultActions[S]}else{if(_===null||typeof _=="undefined"){_=q()}F=l[S]&&l[S][_]}if(typeof F==="undefined"||!F.length||!F[0]){var z="";D=[];for(L in l[S]){if(this.terminals_[L]&&L>x){D.push("'"+this.terminals_[L]+"'")}}if(p.showPosition){z="Parse error on line "+(c+1)+":\n"+p.showPosition()+"\nExpecting "+D.join(", ")+", got '"+(this.terminals_[_]||_)+"'"}else{z="Parse error on line "+(c+1)+": Unexpected "+(_==f?"end of input":"'"+(this.terminals_[_]||_)+"'")}this.parseError(z,{text:p.match,token:this.terminals_[_]||_,line:p.yylineno,loc:T,expected:D})}if(F[0]instanceof Array&&F.length>1){throw new Error("Parse Error: multiple actions possible at state: "+S+", token: "+_)}switch(F[0]){case 1:n.push(_);r.push(p.yytext);o.push(p.yylloc);n.push(F[1]);_=null;if(!A){d=p.yyleng;h=p.yytext;c=p.yylineno;T=p.yylloc;if(u>0){u--}}else{_=A;A=null}break;case 2:I=this.productions_[F[1]][1];C.$=r[r.length-I];C._$={first_line:o[o.length-(I||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(I||1)].first_column,last_column:o[o.length-1].last_column};if(m){C._$.range=[o[o.length-(I||1)].range[0],o[o.length-1].range[1]]}v=this.performAction.apply(C,[h,d,c,y.yy,F[1],r,o].concat(g));if(typeof v!=="undefined"){return v}if(I){n=n.slice(0,-1*I*2);r=r.slice(0,-1*I);o=o.slice(0,-1*I)}n.push(this.productions_[F[1]][0]);r.push(C.$);o.push(C._$);E=l[n[n.length-2]][n[n.length-1]];n.push(E);break;case 3:return true}}return true}),"parse")};var xt=function(){var t={EOF:1,parseError:(0,a.K2)((function t(e,i){if(this.yy.parser){this.yy.parser.parseError(e,i)}else{throw new Error(e)}}),"parseError"),setInput:(0,a.K2)((function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,a.K2)((function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t}),"input"),unput:(0,a.K2)((function(t){var e=t.length;var i=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var a=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(i.length-1){this.yylineno-=i.length-1}var n=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:i?(i.length===a.length?this.yylloc.first_column:0)+a[a.length-i.length].length-i[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[n[0],n[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,a.K2)((function(){this._more=true;return this}),"more"),reject:(0,a.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,a.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,a.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,a.K2)((function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,a.K2)((function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,a.K2)((function(t,e){var i,a,n;if(this.options.backtrack_lexer){n={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){n.yylloc.range=this.yylloc.range.slice(0)}}a=t[0].match(/(?:\r\n?|\n).*/g);if(a){this.yylineno+=a.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:a?a[a.length-1].length-a[a.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];i=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(i){return i}else if(this._backtrack){for(var s in n){this[s]=n[s]}return false}return false}),"test_match"),next:(0,a.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,i,a;if(!this._more){this.yytext="";this.match=""}var n=this._currentRules();for(var s=0;s<n.length;s++){i=this._input.match(this.rules[n[s]]);if(i&&(!e||i[0].length>e[0].length)){e=i;a=s;if(this.options.backtrack_lexer){t=this.test_match(i,n[s]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,n[a]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,a.K2)((function t(){var e=this.next();if(e){return e}else{return this.lex()}}),"lex"),begin:(0,a.K2)((function t(e){this.conditionStack.push(e)}),"begin"),popState:(0,a.K2)((function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,a.K2)((function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,a.K2)((function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}}),"topState"),pushState:(0,a.K2)((function t(e){this.begin(e)}),"pushState"),stateStackSize:(0,a.K2)((function t(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,a.K2)((function t(e,i,a,n){var s=n;switch(a){case 0:break;case 1:break;case 2:return 55;break;case 3:break;case 4:this.begin("title");return 35;break;case 5:this.popState();return"title_value";break;case 6:this.begin("acc_title");return 37;break;case 7:this.popState();return"acc_title_value";break;case 8:this.begin("acc_descr");return 39;break;case 9:this.popState();return"acc_descr_value";break;case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";break;case 13:return 48;break;case 14:return 50;break;case 15:return 49;break;case 16:return 51;break;case 17:return 52;break;case 18:return 53;break;case 19:return 54;break;case 20:return 25;break;case 21:this.begin("md_string");break;case 22:return"MD_STR";break;case 23:this.popState();break;case 24:this.begin("string");break;case 25:this.popState();break;case 26:return"STR";break;case 27:this.begin("class_name");break;case 28:this.popState();return 47;break;case 29:this.begin("point_start");return 44;break;case 30:this.begin("point_x");return 45;break;case 31:this.popState();break;case 32:this.popState();this.begin("point_y");break;case 33:this.popState();return 46;break;case 34:return 28;break;case 35:return 4;break;case 36:return 11;break;case 37:return 64;break;case 38:return 10;break;case 39:return 65;break;case 40:return 65;break;case 41:return 14;break;case 42:return 13;break;case 43:return 67;break;case 44:return 66;break;case 45:return 12;break;case 46:return 8;break;case 47:return 5;break;case 48:return 18;break;case 49:return 56;break;case 50:return 63;break;case 51:return 57;break}}),"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?: *x-axis *)/i,/^(?: *y-axis *)/i,/^(?: *--+> *)/i,/^(?: *quadrant-1 *)/i,/^(?: *quadrant-2 *)/i,/^(?: *quadrant-3 *)/i,/^(?: *quadrant-4 *)/i,/^(?:classDef\b)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?::::)/i,/^(?:^\w+)/i,/^(?:\s*:\s*\[\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?:\s*\] *)/i,/^(?:\s*,\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?: *quadrantChart *)/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s)/i,/^(?:;)/i,/^(?:[!"#$%&'*+,-.`?\\_/])/i,/^(?:$)/i],conditions:{class_name:{rules:[28],inclusive:false},point_y:{rules:[33],inclusive:false},point_x:{rules:[32],inclusive:false},point_start:{rules:[30,31],inclusive:false},acc_descr_multiline:{rules:[11,12],inclusive:false},acc_descr:{rules:[9],inclusive:false},acc_title:{rules:[7],inclusive:false},title:{rules:[5],inclusive:false},md_string:{rules:[22,23],inclusive:false},string:{rules:[25,26],inclusive:false},INITIAL:{rules:[0,1,2,3,4,6,8,10,13,14,15,16,17,18,19,20,21,24,27,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],inclusive:true}}};return t}();ut.lexer=xt;function ft(){this.yy={}}(0,a.K2)(ft,"Parser");ft.prototype=ut;ut.Parser=ft;return new ft}();s.parser=s;var r=s;var o=(0,a.P$)();var l=class{constructor(){this.classes=new Map;this.config=this.getDefaultConfig();this.themeConfig=this.getDefaultThemeConfig();this.data=this.getDefaultData()}static{(0,a.K2)(this,"QuadrantBuilder")}getDefaultData(){return{titleText:"",quadrant1Text:"",quadrant2Text:"",quadrant3Text:"",quadrant4Text:"",xAxisLeftText:"",xAxisRightText:"",yAxisBottomText:"",yAxisTopText:"",points:[]}}getDefaultConfig(){return{showXAxis:true,showYAxis:true,showTitle:true,chartHeight:a.UI.quadrantChart?.chartWidth||500,chartWidth:a.UI.quadrantChart?.chartHeight||500,titlePadding:a.UI.quadrantChart?.titlePadding||10,titleFontSize:a.UI.quadrantChart?.titleFontSize||20,quadrantPadding:a.UI.quadrantChart?.quadrantPadding||5,xAxisLabelPadding:a.UI.quadrantChart?.xAxisLabelPadding||5,yAxisLabelPadding:a.UI.quadrantChart?.yAxisLabelPadding||5,xAxisLabelFontSize:a.UI.quadrantChart?.xAxisLabelFontSize||16,yAxisLabelFontSize:a.UI.quadrantChart?.yAxisLabelFontSize||16,quadrantLabelFontSize:a.UI.quadrantChart?.quadrantLabelFontSize||16,quadrantTextTopPadding:a.UI.quadrantChart?.quadrantTextTopPadding||5,pointTextPadding:a.UI.quadrantChart?.pointTextPadding||5,pointLabelFontSize:a.UI.quadrantChart?.pointLabelFontSize||12,pointRadius:a.UI.quadrantChart?.pointRadius||5,xAxisPosition:a.UI.quadrantChart?.xAxisPosition||"top",yAxisPosition:a.UI.quadrantChart?.yAxisPosition||"left",quadrantInternalBorderStrokeWidth:a.UI.quadrantChart?.quadrantInternalBorderStrokeWidth||1,quadrantExternalBorderStrokeWidth:a.UI.quadrantChart?.quadrantExternalBorderStrokeWidth||2}}getDefaultThemeConfig(){return{quadrant1Fill:o.quadrant1Fill,quadrant2Fill:o.quadrant2Fill,quadrant3Fill:o.quadrant3Fill,quadrant4Fill:o.quadrant4Fill,quadrant1TextFill:o.quadrant1TextFill,quadrant2TextFill:o.quadrant2TextFill,quadrant3TextFill:o.quadrant3TextFill,quadrant4TextFill:o.quadrant4TextFill,quadrantPointFill:o.quadrantPointFill,quadrantPointTextFill:o.quadrantPointTextFill,quadrantXAxisTextFill:o.quadrantXAxisTextFill,quadrantYAxisTextFill:o.quadrantYAxisTextFill,quadrantTitleFill:o.quadrantTitleFill,quadrantInternalBorderStrokeFill:o.quadrantInternalBorderStrokeFill,quadrantExternalBorderStrokeFill:o.quadrantExternalBorderStrokeFill}}clear(){this.config=this.getDefaultConfig();this.themeConfig=this.getDefaultThemeConfig();this.data=this.getDefaultData();this.classes=new Map;a.Rm.info("clear called")}setData(t){this.data={...this.data,...t}}addPoints(t){this.data.points=[...t,...this.data.points]}addClass(t,e){this.classes.set(t,e)}setConfig(t){a.Rm.trace("setConfig called with: ",t);this.config={...this.config,...t}}setThemeConfig(t){a.Rm.trace("setThemeConfig called with: ",t);this.themeConfig={...this.themeConfig,...t}}calculateSpace(t,e,i,a){const n=this.config.xAxisLabelPadding*2+this.config.xAxisLabelFontSize;const s={top:t==="top"&&e?n:0,bottom:t==="bottom"&&e?n:0};const r=this.config.yAxisLabelPadding*2+this.config.yAxisLabelFontSize;const o={left:this.config.yAxisPosition==="left"&&i?r:0,right:this.config.yAxisPosition==="right"&&i?r:0};const l=this.config.titleFontSize+this.config.titlePadding*2;const h={top:a?l:0};const c=this.config.quadrantPadding+o.left;const d=this.config.quadrantPadding+s.top+h.top;const u=this.config.chartWidth-this.config.quadrantPadding*2-o.left-o.right;const x=this.config.chartHeight-this.config.quadrantPadding*2-s.top-s.bottom-h.top;const f=u/2;const g=x/2;const p={quadrantLeft:c,quadrantTop:d,quadrantWidth:u,quadrantHalfWidth:f,quadrantHeight:x,quadrantHalfHeight:g};return{xAxisSpace:s,yAxisSpace:o,titleSpace:h,quadrantSpace:p}}getAxisLabels(t,e,i,a){const{quadrantSpace:n,titleSpace:s}=a;const{quadrantHalfHeight:r,quadrantHeight:o,quadrantLeft:l,quadrantHalfWidth:h,quadrantTop:c,quadrantWidth:d}=n;const u=Boolean(this.data.xAxisRightText);const x=Boolean(this.data.yAxisTopText);const f=[];if(this.data.xAxisLeftText&&e){f.push({text:this.data.xAxisLeftText,fill:this.themeConfig.quadrantXAxisTextFill,x:l+(u?h/2:0),y:t==="top"?this.config.xAxisLabelPadding+s.top:this.config.xAxisLabelPadding+c+o+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:u?"center":"left",horizontalPos:"top",rotation:0})}if(this.data.xAxisRightText&&e){f.push({text:this.data.xAxisRightText,fill:this.themeConfig.quadrantXAxisTextFill,x:l+h+(u?h/2:0),y:t==="top"?this.config.xAxisLabelPadding+s.top:this.config.xAxisLabelPadding+c+o+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:u?"center":"left",horizontalPos:"top",rotation:0})}if(this.data.yAxisBottomText&&i){f.push({text:this.data.yAxisBottomText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+l+d+this.config.quadrantPadding,y:c+o-(x?r/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:x?"center":"left",horizontalPos:"top",rotation:-90})}if(this.data.yAxisTopText&&i){f.push({text:this.data.yAxisTopText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+l+d+this.config.quadrantPadding,y:c+r-(x?r/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:x?"center":"left",horizontalPos:"top",rotation:-90})}return f}getQuadrants(t){const{quadrantSpace:e}=t;const{quadrantHalfHeight:i,quadrantLeft:a,quadrantHalfWidth:n,quadrantTop:s}=e;const r=[{text:{text:this.data.quadrant1Text,fill:this.themeConfig.quadrant1TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:a+n,y:s,width:n,height:i,fill:this.themeConfig.quadrant1Fill},{text:{text:this.data.quadrant2Text,fill:this.themeConfig.quadrant2TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:a,y:s,width:n,height:i,fill:this.themeConfig.quadrant2Fill},{text:{text:this.data.quadrant3Text,fill:this.themeConfig.quadrant3TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:a,y:s+i,width:n,height:i,fill:this.themeConfig.quadrant3Fill},{text:{text:this.data.quadrant4Text,fill:this.themeConfig.quadrant4TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:a+n,y:s+i,width:n,height:i,fill:this.themeConfig.quadrant4Fill}];for(const o of r){o.text.x=o.x+o.width/2;if(this.data.points.length===0){o.text.y=o.y+o.height/2;o.text.horizontalPos="middle"}else{o.text.y=o.y+this.config.quadrantTextTopPadding;o.text.horizontalPos="top"}}return r}getQuadrantPoints(t){const{quadrantSpace:e}=t;const{quadrantHeight:i,quadrantLeft:a,quadrantTop:s,quadrantWidth:r}=e;const o=(0,n.m4Y)().domain([0,1]).range([a,r+a]);const l=(0,n.m4Y)().domain([0,1]).range([i+s,s]);const h=this.data.points.map((t=>{const e=this.classes.get(t.className);if(e){t={...e,...t}}const i={x:o(t.x),y:l(t.y),fill:t.color??this.themeConfig.quadrantPointFill,radius:t.radius??this.config.pointRadius,text:{text:t.text,fill:this.themeConfig.quadrantPointTextFill,x:o(t.x),y:l(t.y)+this.config.pointTextPadding,verticalPos:"center",horizontalPos:"top",fontSize:this.config.pointLabelFontSize,rotation:0},strokeColor:t.strokeColor??this.themeConfig.quadrantPointFill,strokeWidth:t.strokeWidth??"0px"};return i}));return h}getBorders(t){const e=this.config.quadrantExternalBorderStrokeWidth/2;const{quadrantSpace:i}=t;const{quadrantHalfHeight:a,quadrantHeight:n,quadrantLeft:s,quadrantHalfWidth:r,quadrantTop:o,quadrantWidth:l}=i;const h=[{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:s-e,y1:o,x2:s+l+e,y2:o},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:s+l,y1:o+e,x2:s+l,y2:o+n-e},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:s-e,y1:o+n,x2:s+l+e,y2:o+n},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:s,y1:o+e,x2:s,y2:o+n-e},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:s+r,y1:o+e,x2:s+r,y2:o+n-e},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:s+e,y1:o+a,x2:s+l-e,y2:o+a}];return h}getTitle(t){if(t){return{text:this.data.titleText,fill:this.themeConfig.quadrantTitleFill,fontSize:this.config.titleFontSize,horizontalPos:"top",verticalPos:"center",rotation:0,y:this.config.titlePadding,x:this.config.chartWidth/2}}return}build(){const t=this.config.showXAxis&&!!(this.data.xAxisLeftText||this.data.xAxisRightText);const e=this.config.showYAxis&&!!(this.data.yAxisTopText||this.data.yAxisBottomText);const i=this.config.showTitle&&!!this.data.titleText;const a=this.data.points.length>0?"bottom":this.config.xAxisPosition;const n=this.calculateSpace(a,t,e,i);return{points:this.getQuadrantPoints(n),quadrants:this.getQuadrants(n),axisLabels:this.getAxisLabels(a,t,e,n),borderLines:this.getBorders(n),title:this.getTitle(i)}}};var h=class extends Error{static{(0,a.K2)(this,"InvalidStyleError")}constructor(t,e,i){super(`value for ${t} ${e} is invalid, please use a valid ${i}`);this.name="InvalidStyleError"}};function c(t){return!/^#?([\dA-Fa-f]{6}|[\dA-Fa-f]{3})$/.test(t)}(0,a.K2)(c,"validateHexCode");function d(t){return!/^\d+$/.test(t)}(0,a.K2)(d,"validateNumber");function u(t){return!/^\d+px$/.test(t)}(0,a.K2)(u,"validateSizeInPixels");var x=(0,a.D7)();function f(t){return(0,a.jZ)(t.trim(),x)}(0,a.K2)(f,"textSanitizer");var g=new l;function p(t){g.setData({quadrant1Text:f(t.text)})}(0,a.K2)(p,"setQuadrant1Text");function y(t){g.setData({quadrant2Text:f(t.text)})}(0,a.K2)(y,"setQuadrant2Text");function b(t){g.setData({quadrant3Text:f(t.text)})}(0,a.K2)(b,"setQuadrant3Text");function T(t){g.setData({quadrant4Text:f(t.text)})}(0,a.K2)(T,"setQuadrant4Text");function m(t){g.setData({xAxisLeftText:f(t.text)})}(0,a.K2)(m,"setXAxisLeftText");function k(t){g.setData({xAxisRightText:f(t.text)})}(0,a.K2)(k,"setXAxisRightText");function q(t){g.setData({yAxisTopText:f(t.text)})}(0,a.K2)(q,"setYAxisTopText");function _(t){g.setData({yAxisBottomText:f(t.text)})}(0,a.K2)(_,"setYAxisBottomText");function A(t){const e={};for(const i of t){const[t,a]=i.trim().split(/\s*:\s*/);if(t==="radius"){if(d(a)){throw new h(t,a,"number")}e.radius=parseInt(a)}else if(t==="color"){if(c(a)){throw new h(t,a,"hex code")}e.color=a}else if(t==="stroke-color"){if(c(a)){throw new h(t,a,"hex code")}e.strokeColor=a}else if(t==="stroke-width"){if(u(a)){throw new h(t,a,"number of pixels (eg. 10px)")}e.strokeWidth=a}else{throw new Error(`style named ${t} is not supported.`)}}return e}(0,a.K2)(A,"parseStyles");function S(t,e,i,a,n){const s=A(n);g.addPoints([{x:i,y:a,text:f(t.text),className:e,...s}])}(0,a.K2)(S,"addPoint");function F(t,e){g.addClass(t,A(e))}(0,a.K2)(F,"addClass");function P(t){g.setConfig({chartWidth:t})}(0,a.K2)(P,"setWidth");function v(t){g.setConfig({chartHeight:t})}(0,a.K2)(v,"setHeight");function C(){const t=(0,a.D7)();const{themeVariables:e,quadrantChart:i}=t;if(i){g.setConfig(i)}g.setThemeConfig({quadrant1Fill:e.quadrant1Fill,quadrant2Fill:e.quadrant2Fill,quadrant3Fill:e.quadrant3Fill,quadrant4Fill:e.quadrant4Fill,quadrant1TextFill:e.quadrant1TextFill,quadrant2TextFill:e.quadrant2TextFill,quadrant3TextFill:e.quadrant3TextFill,quadrant4TextFill:e.quadrant4TextFill,quadrantPointFill:e.quadrantPointFill,quadrantPointTextFill:e.quadrantPointTextFill,quadrantXAxisTextFill:e.quadrantXAxisTextFill,quadrantYAxisTextFill:e.quadrantYAxisTextFill,quadrantExternalBorderStrokeFill:e.quadrantExternalBorderStrokeFill,quadrantInternalBorderStrokeFill:e.quadrantInternalBorderStrokeFill,quadrantTitleFill:e.quadrantTitleFill});g.setData({titleText:(0,a.ab)()});return g.build()}(0,a.K2)(C,"getQuadrantData");var L=(0,a.K2)((function(){g.clear();(0,a.IU)()}),"clear");var I={setWidth:P,setHeight:v,setQuadrant1Text:p,setQuadrant2Text:y,setQuadrant3Text:b,setQuadrant4Text:T,setXAxisLeftText:m,setXAxisRightText:k,setYAxisTopText:q,setYAxisBottomText:_,parseStyles:A,addPoint:S,addClass:F,getQuadrantData:C,clear:L,setAccTitle:a.SV,getAccTitle:a.iN,setDiagramTitle:a.ke,getDiagramTitle:a.ab,getAccDescription:a.m7,setAccDescription:a.EI};var E=(0,a.K2)(((t,e,i,s)=>{function r(t){return t==="top"?"hanging":"middle"}(0,a.K2)(r,"getDominantBaseLine");function o(t){return t==="left"?"start":"middle"}(0,a.K2)(o,"getTextAnchor");function l(t){return`translate(${t.x}, ${t.y}) rotate(${t.rotation||0})`}(0,a.K2)(l,"getTransformation");const h=(0,a.D7)();a.Rm.debug("Rendering quadrant chart\n"+t);const c=h.securityLevel;let d;if(c==="sandbox"){d=(0,n.Ltv)("#i"+e)}const u=c==="sandbox"?(0,n.Ltv)(d.nodes()[0].contentDocument.body):(0,n.Ltv)("body");const x=u.select(`[id="${e}"]`);const f=x.append("g").attr("class","main");const g=h.quadrantChart?.chartWidth??500;const p=h.quadrantChart?.chartHeight??500;(0,a.a$)(x,p,g,h.quadrantChart?.useMaxWidth??true);x.attr("viewBox","0 0 "+g+" "+p);s.db.setHeight(p);s.db.setWidth(g);const y=s.db.getQuadrantData();const b=f.append("g").attr("class","quadrants");const T=f.append("g").attr("class","border");const m=f.append("g").attr("class","data-points");const k=f.append("g").attr("class","labels");const q=f.append("g").attr("class","title");if(y.title){q.append("text").attr("x",0).attr("y",0).attr("fill",y.title.fill).attr("font-size",y.title.fontSize).attr("dominant-baseline",r(y.title.horizontalPos)).attr("text-anchor",o(y.title.verticalPos)).attr("transform",l(y.title)).text(y.title.text)}if(y.borderLines){T.selectAll("line").data(y.borderLines).enter().append("line").attr("x1",(t=>t.x1)).attr("y1",(t=>t.y1)).attr("x2",(t=>t.x2)).attr("y2",(t=>t.y2)).style("stroke",(t=>t.strokeFill)).style("stroke-width",(t=>t.strokeWidth))}const _=b.selectAll("g.quadrant").data(y.quadrants).enter().append("g").attr("class","quadrant");_.append("rect").attr("x",(t=>t.x)).attr("y",(t=>t.y)).attr("width",(t=>t.width)).attr("height",(t=>t.height)).attr("fill",(t=>t.fill));_.append("text").attr("x",0).attr("y",0).attr("fill",(t=>t.text.fill)).attr("font-size",(t=>t.text.fontSize)).attr("dominant-baseline",(t=>r(t.text.horizontalPos))).attr("text-anchor",(t=>o(t.text.verticalPos))).attr("transform",(t=>l(t.text))).text((t=>t.text.text));const A=k.selectAll("g.label").data(y.axisLabels).enter().append("g").attr("class","label");A.append("text").attr("x",0).attr("y",0).text((t=>t.text)).attr("fill",(t=>t.fill)).attr("font-size",(t=>t.fontSize)).attr("dominant-baseline",(t=>r(t.horizontalPos))).attr("text-anchor",(t=>o(t.verticalPos))).attr("transform",(t=>l(t)));const S=m.selectAll("g.data-point").data(y.points).enter().append("g").attr("class","data-point");S.append("circle").attr("cx",(t=>t.x)).attr("cy",(t=>t.y)).attr("r",(t=>t.radius)).attr("fill",(t=>t.fill)).attr("stroke",(t=>t.strokeColor)).attr("stroke-width",(t=>t.strokeWidth));S.append("text").attr("x",0).attr("y",0).text((t=>t.text.text)).attr("fill",(t=>t.text.fill)).attr("font-size",(t=>t.text.fontSize)).attr("dominant-baseline",(t=>r(t.text.horizontalPos))).attr("text-anchor",(t=>o(t.text.verticalPos))).attr("transform",(t=>l(t.text)))}),"draw");var D={draw:E};var z={parser:r,db:I,renderer:D,styles:(0,a.K2)((()=>""),"styles")}}}]);