#!/usr/bin/env python3
"""
Inference script for HAM10000 Skin Cancer Detection
Load trained model and make predictions on new images
"""

import torch
import torch.nn.functional as F
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import cv2
import joblib
import argparse
import os
from run_skin_cancer_pipeline import SkinCancerCNN, ResNetTransfer

class SkinCancerPredictor:
    """Class for making skin cancer predictions"""
    
    def __init__(self, model_path, model_type='resnet', device=None):
        """
        Initialize the predictor
        
        Args:
            model_path (str): Path to the trained model file
            model_type (str): Type of model ('cnn' or 'resnet')
            device (str): Device to run inference on ('cpu' or 'cuda')
        """
        self.device = device if device else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_type = model_type
        
        # Load label encoder
        try:
            self.label_encoder = joblib.load('label_encoder.pkl')
            self.class_names = {
                'akiec': 'Actinic keratoses',
                'bcc': 'Basal cell carcinoma', 
                'bkl': 'Benign keratosis-like lesions',
                'df': 'Dermatofibroma',
                'mel': 'Melanoma',
                'nv': 'Melanocytic nevi',
                'vasc': 'Vascular lesions'
            }
            self.num_classes = len(self.label_encoder.classes_)
        except FileNotFoundError:
            raise FileNotFoundError("Label encoder not found. Please run training first.")
        
        # Initialize and load model
        self.model = self._load_model(model_path)
        self.model.to(self.device)
        self.model.eval()
        
        # Define image transforms
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        print(f"Predictor initialized with {model_type} model on {self.device}")
    
    def _load_model(self, model_path):
        """Load the trained model"""
        if self.model_type.lower() == 'cnn':
            model = SkinCancerCNN(num_classes=self.num_classes)
        elif self.model_type.lower() == 'resnet':
            model = ResNetTransfer(num_classes=self.num_classes, pretrained=False)
        else:
            raise ValueError("Model type must be 'cnn' or 'resnet'")
        
        # Load state dict
        try:
            state_dict = torch.load(model_path, map_location=self.device)
            model.load_state_dict(state_dict)
            print(f"Model loaded from {model_path}")
        except FileNotFoundError:
            raise FileNotFoundError(f"Model file not found: {model_path}")
        except Exception as e:
            raise Exception(f"Error loading model: {e}")
        
        return model
    
    def preprocess_image(self, image_path):
        """
        Preprocess image for prediction
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            torch.Tensor: Preprocessed image tensor
        """
        try:
            # Load image
            if isinstance(image_path, str):
                image = cv2.imread(image_path)
                if image is None:
                    raise ValueError(f"Could not load image: {image_path}")
                # Convert BGR to RGB
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                # Assume it's already a numpy array
                image = image_path
            
            # Convert to PIL Image
            image = Image.fromarray(image)
            
            # Apply transforms
            image_tensor = self.transform(image)
            
            # Add batch dimension
            image_tensor = image_tensor.unsqueeze(0)
            
            return image_tensor
            
        except Exception as e:
            raise Exception(f"Error preprocessing image: {e}")
    
    def predict(self, image_path, return_probabilities=True):
        """
        Make prediction on a single image
        
        Args:
            image_path (str): Path to the image file
            return_probabilities (bool): Whether to return class probabilities
            
        Returns:
            dict: Prediction results
        """
        # Preprocess image
        image_tensor = self.preprocess_image(image_path)
        image_tensor = image_tensor.to(self.device)
        
        # Make prediction
        with torch.no_grad():
            outputs = self.model(image_tensor)
            probabilities = F.softmax(outputs, dim=1)
            predicted_class_idx = torch.argmax(probabilities, dim=1).item()
            confidence = probabilities[0][predicted_class_idx].item()
        
        # Convert to class name
        predicted_class = self.label_encoder.inverse_transform([predicted_class_idx])[0]
        predicted_class_name = self.class_names[predicted_class]
        
        result = {
            'predicted_class': predicted_class,
            'predicted_class_name': predicted_class_name,
            'confidence': confidence,
            'predicted_class_idx': predicted_class_idx
        }
        
        if return_probabilities:
            # Get all class probabilities
            all_probs = probabilities[0].cpu().numpy()
            class_probabilities = {}
            for i, class_code in enumerate(self.label_encoder.classes_):
                class_probabilities[class_code] = {
                    'name': self.class_names[class_code],
                    'probability': float(all_probs[i])
                }
            result['class_probabilities'] = class_probabilities
        
        return result
    
    def predict_batch(self, image_paths):
        """
        Make predictions on multiple images
        
        Args:
            image_paths (list): List of image file paths
            
        Returns:
            list: List of prediction results
        """
        results = []
        for image_path in image_paths:
            try:
                result = self.predict(image_path)
                result['image_path'] = image_path
                results.append(result)
            except Exception as e:
                print(f"Error predicting {image_path}: {e}")
                results.append({
                    'image_path': image_path,
                    'error': str(e)
                })
        
        return results

def main():
    parser = argparse.ArgumentParser(description='Skin Cancer Prediction')
    parser.add_argument('--image', type=str, required=True, help='Path to image file or directory')
    parser.add_argument('--model', type=str, default='best_model.pth', help='Path to model file')
    parser.add_argument('--model_type', type=str, default='resnet', choices=['cnn', 'resnet'], 
                       help='Type of model')
    parser.add_argument('--device', type=str, default=None, choices=['cpu', 'cuda'], 
                       help='Device to run inference on')
    parser.add_argument('--batch', action='store_true', help='Process directory of images')
    
    args = parser.parse_args()
    
    # Initialize predictor
    try:
        predictor = SkinCancerPredictor(args.model, args.model_type, args.device)
    except Exception as e:
        print(f"Error initializing predictor: {e}")
        return
    
    # Make predictions
    if args.batch and os.path.isdir(args.image):
        # Process directory
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_paths = []
        
        for file in os.listdir(args.image):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                image_paths.append(os.path.join(args.image, file))
        
        if not image_paths:
            print(f"No image files found in {args.image}")
            return
        
        print(f"Processing {len(image_paths)} images...")
        results = predictor.predict_batch(image_paths)
        
        # Print results
        for result in results:
            if 'error' in result:
                print(f"\n{result['image_path']}: ERROR - {result['error']}")
            else:
                print(f"\n{result['image_path']}:")
                print(f"  Prediction: {result['predicted_class_name']} ({result['predicted_class']})")
                print(f"  Confidence: {result['confidence']:.4f}")
    
    else:
        # Process single image
        if not os.path.isfile(args.image):
            print(f"Image file not found: {args.image}")
            return
        
        try:
            result = predictor.predict(args.image)
            
            print(f"\nPrediction for {args.image}:")
            print(f"Predicted Class: {result['predicted_class_name']} ({result['predicted_class']})")
            print(f"Confidence: {result['confidence']:.4f}")
            
            print(f"\nAll Class Probabilities:")
            for class_code, info in result['class_probabilities'].items():
                print(f"  {class_code} ({info['name']}): {info['probability']:.4f}")
                
        except Exception as e:
            print(f"Error making prediction: {e}")

if __name__ == "__main__":
    main()
