# HAM10000 Skin Cancer Detection Pipeline

A complete machine learning pipeline for skin cancer classification using the HAM10000 dataset from Kaggle.

## 📊 Dataset Overview

The HAM10000 dataset contains **10,015 dermatoscopic images** of skin lesions with 7 different diagnostic categories:

- **akiec** (327 samples): Actinic keratoses and intraepithelial carcinoma / <PERSON>'s disease
- **bcc** (514 samples): Basal cell carcinoma
- **bkl** (1,099 samples): Benign keratosis-like lesions
- **df** (115 samples): Dermatofibroma
- **mel** (1,113 samples): Melanoma
- **nv** (6,705 samples): Melanocytic nevi
- **vasc** (142 samples): Vascular lesions

### Key Dataset Characteristics
- **Total Images**: 10,015
- **Image Format**: JPG
- **Class Imbalance**: 58.30:1 ratio (nv vs df)
- **Missing Values**: 57 missing age values (handled by median imputation)

## 🚀 Pipeline Components

### 1. Environment Setup ✅
- Python virtual environment named "cancer"
- Dependencies: PyTorch, scikit-learn, pandas, numpy, mat<PERSON><PERSON><PERSON><PERSON>, seaborn, opencv-python, PIL

### 2. Data Exploration & Analysis (EDA) ✅
- Comprehensive dataset analysis
- Class distribution visualization
- Image property analysis
- Missing value identification and handling

### 3. Data Preprocessing & Cleaning ✅
- Missing value imputation (age: median, localization: 'unknown')
- Label encoding for target classes
- Train/Validation/Test split (60%/20%/20%)
- Class weight calculation for imbalance handling
- Data augmentation (rotation, flips, color jitter)

### 4. Model Development 🔄
Two model approaches implemented:

#### Custom CNN Architecture
- 4 Convolutional layers (32→64→128→256 filters)
- MaxPooling and Dropout for regularization
- 3 Fully connected layers
- **Parameters**: ~26.1M

#### ResNet18 Transfer Learning
- Pre-trained ResNet18 backbone
- Frozen early layers, fine-tuned final layers
- Custom classifier head with dropout
- **Parameters**: ~11.3M (trainable: subset)

### 5. Training Configuration
- **Loss Function**: CrossEntropyLoss with class weights
- **Optimizer**: Adam with weight decay
- **Scheduler**: ReduceLROnPlateau
- **Data Augmentation**: Random flips, rotation, color jitter
- **Normalization**: ImageNet statistics

### 6. Model Evaluation & Selection 🔄
- Comprehensive metrics: Accuracy, Precision, Recall, F1-Score
- Confusion matrices for detailed analysis
- Classification reports per class
- Best model selection based on validation performance

## 📁 File Structure

```
├── README.md                           # This file
├── skin_cancer_detection_pipeline.ipynb # Interactive Jupyter notebook
├── run_skin_cancer_pipeline.py        # Main pipeline script
├── train_models.py                     # Model training script
├── HAM10000_metadata.csv              # Dataset metadata
├── HAM10000_images_part_1/             # Image folder 1
├── HAM10000_images_part_2/             # Image folder 2
├── label_encoder.pkl                   # Saved label encoder
├── custom_cnn_model.pth               # Trained CNN model
├── resnet_transfer_model.pth          # Trained ResNet model
├── best_model.pth                     # Best performing model
└── model_results.json                 # Training results summary
```

## 🔧 Usage

### Quick Start
```bash
# 1. Activate virtual environment
source cancer/bin/activate

# 2. Run the complete pipeline
python run_skin_cancer_pipeline.py

# 3. Train models
python train_models.py

# 4. Or use interactive Jupyter notebook
jupyter notebook skin_cancer_detection_pipeline.ipynb
```

### Pipeline Steps

1. **Data Loading & EDA**
   ```python
   metadata, class_names = load_and_explore_data()
   ```

2. **Preprocessing**
   ```python
   metadata, label_encoder = preprocess_data(metadata)
   train_data, val_data, test_data = create_data_splits(metadata)
   ```

3. **Model Training**
   ```python
   # Custom CNN
   cnn_model = SkinCancerCNN(num_classes=7)
   
   # ResNet Transfer Learning
   resnet_model = ResNetTransfer(num_classes=7, pretrained=True)
   ```

4. **Evaluation**
   ```python
   results = evaluate_model(model, test_loader, device, class_names)
   ```

## 📈 Results

### Training Progress (Example)
- **Epoch 1**: Val Accuracy: 13.33%
- **Epoch 2**: Val Accuracy: 56.12% ⬆️
- **Epoch 3**: Training in progress...

### Model Comparison
| Model | Parameters | Val Accuracy | Test Accuracy | F1-Score |
|-------|------------|--------------|---------------|----------|
| Custom CNN | 26.1M | TBD | TBD | TBD |
| ResNet Transfer | 11.3M | TBD | TBD | TBD |

*Results will be updated upon training completion*

## 🎯 Key Features

### Data Handling
- ✅ Robust image loading from multiple folders
- ✅ Automatic missing value handling
- ✅ Stratified train/val/test splits
- ✅ Class imbalance mitigation with weighted loss

### Model Architecture
- ✅ Custom CNN with modern techniques (dropout, batch norm concepts)
- ✅ Transfer learning with pre-trained ResNet18
- ✅ Flexible architecture for different class counts

### Training & Evaluation
- ✅ Comprehensive training loop with validation
- ✅ Learning rate scheduling
- ✅ Best model checkpointing
- ✅ Detailed evaluation metrics
- ✅ Confusion matrix visualization

### Reproducibility
- ✅ Fixed random seeds
- ✅ Saved model states and configurations
- ✅ Documented hyperparameters

## 🔬 Technical Details

### Data Augmentation Strategy
```python
train_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.RandomHorizontalFlip(p=0.5),
    transforms.RandomVerticalFlip(p=0.5),
    transforms.RandomRotation(degrees=20),
    transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])
```

### Class Weight Calculation
```python
class_weights = compute_class_weight(
    'balanced', 
    classes=np.unique(train_data['dx_encoded']), 
    y=train_data['dx_encoded']
)
```

## 📊 Evaluation Metrics

The pipeline evaluates models using:
- **Accuracy**: Overall classification accuracy
- **Precision**: Per-class and weighted average
- **Recall**: Per-class and weighted average  
- **F1-Score**: Harmonic mean of precision and recall
- **Confusion Matrix**: Detailed per-class performance
- **Classification Report**: Comprehensive per-class metrics

## 🚀 Future Improvements

1. **Model Architecture**
   - EfficientNet transfer learning
   - Vision Transformer (ViT) implementation
   - Ensemble methods

2. **Data Enhancement**
   - Advanced augmentation techniques
   - External dataset integration
   - Synthetic data generation

3. **Training Optimization**
   - Mixed precision training
   - Gradient accumulation
   - Advanced learning rate schedules

4. **Deployment**
   - Model quantization for mobile deployment
   - REST API for inference
   - Web application interface

## 📝 Notes

- Training time varies significantly between CPU and GPU
- Models are saved in PyTorch format (.pth files)
- Label encoder is saved for consistent prediction mapping
- All visualizations are saved as high-resolution PNG files

## 🤝 Contributing

This pipeline is designed to be modular and extensible. Key areas for contribution:
- Additional model architectures
- Advanced data augmentation techniques
- Hyperparameter optimization
- Model interpretability features

---

**Status**: ✅ Environment Setup | ✅ EDA | ✅ Preprocessing | 🔄 Model Training | ⏳ Evaluation | ⏳ Documentation
