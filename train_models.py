#!/usr/bin/env python3
"""
Training script for HAM10000 Skin Cancer Detection Models
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import precision_recall_fscore_support
import seaborn as sns
import joblib
import time
from run_skin_cancer_pipeline import *

def train_model(model, train_loader, val_loader, criterion, optimizer, scheduler, num_epochs=10, device='cpu'):
    """Train the model and return training history"""
    
    model.to(device)
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    best_val_acc = 0.0
    best_model_state = None
    
    print(f"Training on {device}")
    print("-" * 50)
    
    for epoch in range(num_epochs):
        start_time = time.time()
        print(f'Epoch {epoch+1}/{num_epochs}')
        
        # Training phase
        model.train()
        running_loss = 0.0
        running_corrects = 0
        
        for batch_idx, (inputs, labels) in enumerate(train_loader):
            inputs, labels = inputs.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            _, preds = torch.max(outputs, 1)
            running_loss += loss.item() * inputs.size(0)
            running_corrects += torch.sum(preds == labels.data)
            
            # Print progress every 50 batches
            if batch_idx % 50 == 0:
                print(f'  Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')
        
        epoch_loss = running_loss / len(train_loader.dataset)
        epoch_acc = running_corrects.double() / len(train_loader.dataset)
        
        history['train_loss'].append(epoch_loss)
        history['train_acc'].append(epoch_acc.item())
        
        # Validation phase
        model.eval()
        val_running_loss = 0.0
        val_running_corrects = 0
        
        with torch.no_grad():
            for inputs, labels in val_loader:
                inputs, labels = inputs.to(device), labels.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, labels)
                
                _, preds = torch.max(outputs, 1)
                val_running_loss += loss.item() * inputs.size(0)
                val_running_corrects += torch.sum(preds == labels.data)
        
        val_epoch_loss = val_running_loss / len(val_loader.dataset)
        val_epoch_acc = val_running_corrects.double() / len(val_loader.dataset)
        
        history['val_loss'].append(val_epoch_loss)
        history['val_acc'].append(val_epoch_acc.item())
        
        epoch_time = time.time() - start_time
        print(f'  Train Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}')
        print(f'  Val Loss: {val_epoch_loss:.4f} Acc: {val_epoch_acc:.4f}')
        print(f'  Time: {epoch_time:.2f}s')
        
        # Save best model
        if val_epoch_acc > best_val_acc:
            best_val_acc = val_epoch_acc
            best_model_state = model.state_dict().copy()
            print(f'  *** New best validation accuracy: {best_val_acc:.4f} ***')
        
        # Step scheduler
        if scheduler:
            scheduler.step(val_epoch_loss)
        
        print()
    
    # Load best model
    model.load_state_dict(best_model_state)
    print(f'Training completed. Best validation accuracy: {best_val_acc:.4f}')
    
    return model, history

def evaluate_model(model, data_loader, device, class_names):
    """Evaluate model and return predictions and metrics"""
    model.eval()
    all_preds = []
    all_labels = []
    all_probs = []
    
    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = model(inputs)
            probs = F.softmax(outputs, dim=1)
            _, preds = torch.max(outputs, 1)
            
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.extend(probs.cpu().numpy())
    
    # Calculate metrics
    accuracy = accuracy_score(all_labels, all_preds)
    precision, recall, f1, _ = precision_recall_fscore_support(all_labels, all_preds, average='weighted')
    
    # Classification report
    report = classification_report(all_labels, all_preds, target_names=class_names, output_dict=True)
    
    return {
        'predictions': all_preds,
        'labels': all_labels,
        'probabilities': all_probs,
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'classification_report': report
    }

def plot_confusion_matrix(y_true, y_pred, class_names, title):
    """Plot confusion matrix"""
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names)
    plt.title(f'Confusion Matrix - {title}')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.tight_layout()
    plt.savefig(f'confusion_matrix_{title.lower().replace(" ", "_")}.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_training_history(history, title):
    """Plot training and validation loss and accuracy"""
    fig, axes = plt.subplots(1, 2, figsize=(15, 5))
    
    # Plot loss
    axes[0].plot(history['train_loss'], label='Training Loss', marker='o')
    axes[0].plot(history['val_loss'], label='Validation Loss', marker='s')
    axes[0].set_title(f'{title} - Loss')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].legend()
    axes[0].grid(True)
    
    # Plot accuracy
    axes[1].plot(history['train_acc'], label='Training Accuracy', marker='o')
    axes[1].plot(history['val_acc'], label='Validation Accuracy', marker='s')
    axes[1].set_title(f'{title} - Accuracy')
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('Accuracy')
    axes[1].legend()
    axes[1].grid(True)
    
    plt.tight_layout()
    plt.savefig(f'training_history_{title.lower().replace(" ", "_")}.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    print("HAM10000 Skin Cancer Detection - Model Training")
    print("=" * 60)
    
    # Load data and setup
    metadata, class_names = load_and_explore_data()
    metadata, label_encoder = preprocess_data(metadata)
    train_data, val_data, test_data = create_data_splits(metadata)
    class_weights_tensor = calculate_class_weights(train_data)
    train_loader, val_loader, test_loader = create_data_loaders(train_data, val_data, test_data)
    
    # Setup device and models
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\nUsing device: {device}")
    
    num_classes = len(label_encoder.classes_)
    class_names_list = list(class_names.keys())
    
    # Initialize models
    print("\nInitializing models...")
    cnn_model = SkinCancerCNN(num_classes=num_classes)
    resnet_model = ResNetTransfer(num_classes=num_classes, pretrained=True)
    
    # Setup training parameters
    criterion = nn.CrossEntropyLoss(weight=class_weights_tensor.to(device))
    
    # Train Custom CNN (reduced epochs for demo)
    print("\n" + "="*60)
    print("Training Custom CNN...")
    print("="*60)
    
    optimizer_cnn = optim.Adam(cnn_model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler_cnn = optim.lr_scheduler.ReduceLROnPlateau(optimizer_cnn, mode='min', patience=2, factor=0.5)
    
    cnn_model, cnn_history = train_model(
        cnn_model, train_loader, val_loader, criterion, optimizer_cnn, scheduler_cnn, 
        num_epochs=5, device=device  # Reduced for demo
    )
    
    # Save CNN model
    torch.save(cnn_model.state_dict(), 'custom_cnn_model.pth')
    print("Custom CNN model saved as 'custom_cnn_model.pth'")
    
    # Train ResNet Transfer Learning Model
    print("\n" + "="*60)
    print("Training ResNet Transfer Learning Model...")
    print("="*60)
    
    optimizer_resnet = optim.Adam(resnet_model.parameters(), lr=0.0001, weight_decay=1e-4)
    scheduler_resnet = optim.lr_scheduler.ReduceLROnPlateau(optimizer_resnet, mode='min', patience=2, factor=0.5)
    
    resnet_model, resnet_history = train_model(
        resnet_model, train_loader, val_loader, criterion, optimizer_resnet, scheduler_resnet, 
        num_epochs=5, device=device  # Reduced for demo
    )
    
    # Save ResNet model
    torch.save(resnet_model.state_dict(), 'resnet_transfer_model.pth')
    print("ResNet Transfer model saved as 'resnet_transfer_model.pth'")
    
    # Plot training histories
    print("\nGenerating training plots...")
    plot_training_history(cnn_history, 'Custom CNN')
    plot_training_history(resnet_history, 'ResNet Transfer Learning')
    
    # Evaluate models on test set
    print("\n" + "="*60)
    print("Evaluating models on test set...")
    print("="*60)
    
    # Evaluate CNN
    cnn_results = evaluate_model(cnn_model, test_loader, device, class_names_list)
    print(f"\nCustom CNN Test Results:")
    print(f"Accuracy: {cnn_results['accuracy']:.4f}")
    print(f"Precision: {cnn_results['precision']:.4f}")
    print(f"Recall: {cnn_results['recall']:.4f}")
    print(f"F1-Score: {cnn_results['f1_score']:.4f}")
    
    # Evaluate ResNet
    resnet_results = evaluate_model(resnet_model, test_loader, device, class_names_list)
    print(f"\nResNet Transfer Test Results:")
    print(f"Accuracy: {resnet_results['accuracy']:.4f}")
    print(f"Precision: {resnet_results['precision']:.4f}")
    print(f"Recall: {resnet_results['recall']:.4f}")
    print(f"F1-Score: {resnet_results['f1_score']:.4f}")
    
    # Plot confusion matrices
    print("\nGenerating confusion matrices...")
    plot_confusion_matrix(cnn_results['labels'], cnn_results['predictions'], 
                         class_names_list, 'Custom CNN')
    plot_confusion_matrix(resnet_results['labels'], resnet_results['predictions'], 
                         class_names_list, 'ResNet Transfer Learning')
    
    # Determine best model
    if resnet_results['accuracy'] > cnn_results['accuracy']:
        best_model = resnet_model
        best_model_name = 'ResNet Transfer Learning'
        best_results = resnet_results
        torch.save(best_model.state_dict(), 'best_model.pth')
    else:
        best_model = cnn_model
        best_model_name = 'Custom CNN'
        best_results = cnn_results
        torch.save(best_model.state_dict(), 'best_model.pth')
    
    print(f"\n" + "="*60)
    print(f"BEST MODEL: {best_model_name}")
    print(f"Test Accuracy: {best_results['accuracy']:.4f}")
    print("Best model saved as 'best_model.pth'")
    print("="*60)
    
    # Save results
    import json
    results_summary = {
        'custom_cnn': {
            'accuracy': float(cnn_results['accuracy']),
            'precision': float(cnn_results['precision']),
            'recall': float(cnn_results['recall']),
            'f1_score': float(cnn_results['f1_score'])
        },
        'resnet_transfer': {
            'accuracy': float(resnet_results['accuracy']),
            'precision': float(resnet_results['precision']),
            'recall': float(resnet_results['recall']),
            'f1_score': float(resnet_results['f1_score'])
        },
        'best_model': best_model_name
    }
    
    with open('model_results.json', 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    print("\nResults saved to 'model_results.json'")
    print("Training and evaluation completed successfully!")

if __name__ == "__main__":
    main()
