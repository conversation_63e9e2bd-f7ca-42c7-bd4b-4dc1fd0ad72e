# Core libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import cv2
from PIL import Image
import os
import glob
import warnings
warnings.filterwarnings('ignore')

# Machine Learning libraries
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import precision_recall_fscore_support, roc_auc_score
from sklearn.utils.class_weight import compute_class_weight

# Deep Learning libraries
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from torchvision import models

# Set random seeds for reproducibility
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("Libraries imported successfully!")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")

# Load metadata
metadata = pd.read_csv('HAM10000_metadata.csv')

print("Dataset Shape:", metadata.shape)
print("\nFirst 5 rows:")
print(metadata.head())

print("\nDataset Info:")
print(metadata.info())

print("\nMissing Values:")
print(metadata.isnull().sum())

print("\nUnique Values per Column:")
for col in metadata.columns:
    print(f"{col}: {metadata[col].nunique()}")

# Analyze class distribution
class_counts = metadata['dx'].value_counts()
print("Class Distribution:")
print(class_counts)
print(f"\nClass Distribution (%):\n{(class_counts / len(metadata) * 100).round(2)}")

# Create class mapping
class_names = {
    'akiec': 'Actinic keratoses',
    'bcc': 'Basal cell carcinoma', 
    'bkl': 'Benign keratosis-like lesions',
    'df': 'Dermatofibroma',
    'mel': 'Melanoma',
    'nv': 'Melanocytic nevi',
    'vasc': 'Vascular lesions'
}

print("\nClass Names:")
for code, name in class_names.items():
    count = class_counts[code]
    percentage = (count / len(metadata) * 100)
    print(f"{code}: {name} - {count} samples ({percentage:.1f}%)")

# Visualize class distribution
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# Bar plot
class_counts.plot(kind='bar', ax=axes[0], color='skyblue')
axes[0].set_title('Class Distribution', fontsize=14, fontweight='bold')
axes[0].set_xlabel('Diagnosis')
axes[0].set_ylabel('Number of Samples')
axes[0].tick_params(axis='x', rotation=45)

# Pie chart
axes[1].pie(class_counts.values, labels=class_counts.index, autopct='%1.1f%%', startangle=90)
axes[1].set_title('Class Distribution (Percentage)', fontsize=14, fontweight='bold')

plt.tight_layout()
plt.show()

# Check for class imbalance
max_class = class_counts.max()
min_class = class_counts.min()
imbalance_ratio = max_class / min_class
print(f"\nClass Imbalance Ratio: {imbalance_ratio:.2f}:1")
print(f"Most frequent class: {class_counts.idxmax()} ({max_class} samples)")
print(f"Least frequent class: {class_counts.idxmin()} ({min_class} samples)")

# Analyze other features
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# Age distribution
metadata['age'].hist(bins=30, ax=axes[0,0], alpha=0.7, color='lightcoral')
axes[0,0].set_title('Age Distribution')
axes[0,0].set_xlabel('Age')
axes[0,0].set_ylabel('Frequency')

# Sex distribution
metadata['sex'].value_counts().plot(kind='bar', ax=axes[0,1], color='lightgreen')
axes[0,1].set_title('Sex Distribution')
axes[0,1].set_xlabel('Sex')
axes[0,1].set_ylabel('Count')
axes[0,1].tick_params(axis='x', rotation=0)

# Localization distribution
loc_counts = metadata['localization'].value_counts().head(10)
loc_counts.plot(kind='barh', ax=axes[1,0], color='gold')
axes[1,0].set_title('Top 10 Localization Sites')
axes[1,0].set_xlabel('Count')

# Diagnosis type distribution
metadata['dx_type'].value_counts().plot(kind='pie', ax=axes[1,1], autopct='%1.1f%%')
axes[1,1].set_title('Diagnosis Type Distribution')

plt.tight_layout()
plt.show()

print("\nAge Statistics:")
print(metadata['age'].describe())
print(f"\nMissing age values: {metadata['age'].isnull().sum()}")

# Check image files availability
image_folders = ['HAM10000_images_part_1', 'HAM10000_images_part_2']
total_images = 0

for folder in image_folders:
    if os.path.exists(folder):
        images = glob.glob(os.path.join(folder, '*.jpg'))
        print(f"{folder}: {len(images)} images")
        total_images += len(images)
    else:
        print(f"{folder}: Folder not found")

print(f"\nTotal images found: {total_images}")
print(f"Metadata entries: {len(metadata)}")
print(f"Match: {total_images == len(metadata)}")

# Function to load and display sample images
def load_image(image_id):
    """Load image by ID from either folder"""
    for folder in image_folders:
        image_path = os.path.join(folder, f"{image_id}.jpg")
        if os.path.exists(image_path):
            return cv2.imread(image_path)
    return None

def display_sample_images(metadata, n_samples=2):
    """Display sample images for each class"""
    classes = metadata['dx'].unique()
    n_classes = len(classes)
    
    fig, axes = plt.subplots(n_classes, n_samples, figsize=(n_samples*4, n_classes*3))
    if n_classes == 1:
        axes = axes.reshape(1, -1)
    
    for i, class_name in enumerate(classes):
        class_samples = metadata[metadata['dx'] == class_name].sample(n_samples)
        
        for j, (_, row) in enumerate(class_samples.iterrows()):
            image = load_image(row['image_id'])
            if image is not None:
                # Convert BGR to RGB
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                axes[i, j].imshow(image_rgb)
                axes[i, j].set_title(f"{class_names[class_name]}\n{row['image_id']}")
                axes[i, j].axis('off')
            else:
                axes[i, j].text(0.5, 0.5, 'Image not found', 
                               ha='center', va='center', transform=axes[i, j].transAxes)
                axes[i, j].set_title(f"{class_names[class_name]}\n{row['image_id']}")
                axes[i, j].axis('off')
    
    plt.tight_layout()
    plt.show()

# Display sample images
print("Sample images from each class:")
display_sample_images(metadata, n_samples=2)

# Analyze image properties
def analyze_image_properties(metadata, sample_size=100):
    """Analyze image dimensions and properties"""
    sample_data = metadata.sample(min(sample_size, len(metadata)))
    
    widths, heights, channels = [], [], []
    
    for _, row in sample_data.iterrows():
        image = load_image(row['image_id'])
        if image is not None:
            h, w, c = image.shape
            heights.append(h)
            widths.append(w)
            channels.append(c)
    
    print(f"Analyzed {len(widths)} images:")
    print(f"Width - Min: {min(widths)}, Max: {max(widths)}, Mean: {np.mean(widths):.1f}")
    print(f"Height - Min: {min(heights)}, Max: {max(heights)}, Mean: {np.mean(heights):.1f}")
    print(f"Channels: {set(channels)}")
    
    # Plot distributions
    fig, axes = plt.subplots(1, 2, figsize=(12, 4))
    
    axes[0].hist(widths, bins=20, alpha=0.7, label='Width', color='blue')
    axes[0].hist(heights, bins=20, alpha=0.7, label='Height', color='red')
    axes[0].set_title('Image Dimensions Distribution')
    axes[0].set_xlabel('Pixels')
    axes[0].set_ylabel('Frequency')
    axes[0].legend()
    
    # Aspect ratio
    aspect_ratios = [w/h for w, h in zip(widths, heights)]
    axes[1].hist(aspect_ratios, bins=20, alpha=0.7, color='green')
    axes[1].set_title('Aspect Ratio Distribution')
    axes[1].set_xlabel('Width/Height Ratio')
    axes[1].set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.show()
    
    return widths, heights

widths, heights = analyze_image_properties(metadata, sample_size=200)

# Handle missing values
print("Missing values before cleaning:")
print(metadata.isnull().sum())

# Fill missing age values with median
median_age = metadata['age'].median()
metadata['age'].fillna(median_age, inplace=True)

# Fill missing localization with 'unknown'
metadata['localization'].fillna('unknown', inplace=True)

print("\nMissing values after cleaning:")
print(metadata.isnull().sum())

# Create age groups for analysis
metadata['age_group'] = pd.cut(metadata['age'], 
                              bins=[0, 30, 50, 70, 100], 
                              labels=['<30', '30-50', '50-70', '>70'])

print("\nAge group distribution:")
print(metadata['age_group'].value_counts())

# Encode categorical variables
label_encoder = LabelEncoder()
metadata['dx_encoded'] = label_encoder.fit_transform(metadata['dx'])

# Create class mapping
class_mapping = dict(zip(label_encoder.classes_, label_encoder.transform(label_encoder.classes_)))
print("Class encoding mapping:")
for class_name, encoded_value in class_mapping.items():
    print(f"{class_name}: {encoded_value}")

# Save the label encoder for later use
import joblib
joblib.dump(label_encoder, 'label_encoder.pkl')
print("\nLabel encoder saved as 'label_encoder.pkl'")

# Create train-validation-test split
# First split: 80% train+val, 20% test
train_val_data, test_data = train_test_split(
    metadata, test_size=0.2, random_state=42, stratify=metadata['dx_encoded']
)

# Second split: 80% train, 20% validation (from train+val)
train_data, val_data = train_test_split(
    train_val_data, test_size=0.25, random_state=42, stratify=train_val_data['dx_encoded']
)

print(f"Dataset splits:")
print(f"Train: {len(train_data)} samples ({len(train_data)/len(metadata)*100:.1f}%)")
print(f"Validation: {len(val_data)} samples ({len(val_data)/len(metadata)*100:.1f}%)")
print(f"Test: {len(test_data)} samples ({len(test_data)/len(metadata)*100:.1f}%)")

# Check class distribution in splits
print("\nClass distribution in splits:")
for split_name, split_data in [('Train', train_data), ('Validation', val_data), ('Test', test_data)]:
    print(f"\n{split_name}:")
    class_dist = split_data['dx'].value_counts()
    for class_name in class_dist.index:
        count = class_dist[class_name]
        percentage = count / len(split_data) * 100
        print(f"  {class_name}: {count} ({percentage:.1f}%)")

# Calculate class weights for handling imbalance
class_weights = compute_class_weight(
    'balanced', 
    classes=np.unique(train_data['dx_encoded']), 
    y=train_data['dx_encoded']
)

class_weight_dict = dict(zip(np.unique(train_data['dx_encoded']), class_weights))
print("Class weights for handling imbalance:")
for class_idx, weight in class_weight_dict.items():
    class_name = label_encoder.inverse_transform([class_idx])[0]
    print(f"{class_name}: {weight:.3f}")

# Convert to PyTorch tensor
class_weights_tensor = torch.FloatTensor(list(class_weight_dict.values()))
print(f"\nClass weights tensor: {class_weights_tensor}")

class SkinLesionDataset(Dataset):
    """Custom dataset for skin lesion images"""
    
    def __init__(self, dataframe, image_folders, transform=None, target_size=(224, 224)):
        self.dataframe = dataframe.reset_index(drop=True)
        self.image_folders = image_folders
        self.transform = transform
        self.target_size = target_size
        
    def __len__(self):
        return len(self.dataframe)
    
    def __getitem__(self, idx):
        row = self.dataframe.iloc[idx]
        image_id = row['image_id']
        label = row['dx_encoded']
        
        # Load image
        image = self._load_image(image_id)
        
        if image is None:
            # Return a black image if not found
            image = np.zeros((*self.target_size, 3), dtype=np.uint8)
        
        # Convert to PIL Image for transforms
        image = Image.fromarray(image)
        
        if self.transform:
            image = self.transform(image)
        
        return image, label
    
    def _load_image(self, image_id):
        """Load image by ID from folders"""
        for folder in self.image_folders:
            image_path = os.path.join(folder, f"{image_id}.jpg")
            if os.path.exists(image_path):
                image = cv2.imread(image_path)
                if image is not None:
                    # Convert BGR to RGB and resize
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    image = cv2.resize(image, self.target_size)
                    return image
        return None

# Define data transforms
train_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.RandomHorizontalFlip(p=0.5),
    transforms.RandomVerticalFlip(p=0.5),
    transforms.RandomRotation(degrees=20),
    transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

val_test_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# Create datasets
train_dataset = SkinLesionDataset(train_data, image_folders, transform=train_transform)
val_dataset = SkinLesionDataset(val_data, image_folders, transform=val_test_transform)
test_dataset = SkinLesionDataset(test_data, image_folders, transform=val_test_transform)

# Create data loaders
batch_size = 32
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=2)

print(f"Data loaders created:")
print(f"Train batches: {len(train_loader)}")
print(f"Validation batches: {len(val_loader)}")
print(f"Test batches: {len(test_loader)}")

# Test data loader
sample_batch = next(iter(train_loader))
print(f"\nSample batch shape: {sample_batch[0].shape}")
print(f"Sample labels shape: {sample_batch[1].shape}")

# Define CNN model from scratch
class SkinCancerCNN(nn.Module):
    def __init__(self, num_classes=7):
        super(SkinCancerCNN, self).__init__()
        
        # Convolutional layers
        self.conv1 = nn.Conv2d(3, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1)
        
        # Pooling and dropout
        self.pool = nn.MaxPool2d(2, 2)
        self.dropout = nn.Dropout(0.5)
        
        # Fully connected layers
        self.fc1 = nn.Linear(256 * 14 * 14, 512)
        self.fc2 = nn.Linear(512, 128)
        self.fc3 = nn.Linear(128, num_classes)
        
    def forward(self, x):
        # Convolutional layers with ReLU and pooling
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = self.pool(F.relu(self.conv3(x)))
        x = self.pool(F.relu(self.conv4(x)))
        
        # Flatten
        x = x.view(-1, 256 * 14 * 14)
        
        # Fully connected layers
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

# Define ResNet-based transfer learning model
class ResNetTransfer(nn.Module):
    def __init__(self, num_classes=7, pretrained=True):
        super(ResNetTransfer, self).__init__()
        
        # Load pretrained ResNet18
        self.resnet = models.resnet18(pretrained=pretrained)
        
        # Freeze early layers
        for param in list(self.resnet.parameters())[:-10]:
            param.requires_grad = False
        
        # Replace final layer
        num_features = self.resnet.fc.in_features
        self.resnet.fc = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(num_features, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, x):
        return self.resnet(x)

# Training function
def train_model(model, train_loader, val_loader, criterion, optimizer, scheduler, num_epochs=10, device='cpu'):
    """Train the model and return training history"""
    
    model.to(device)
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    best_val_acc = 0.0
    best_model_state = None
    
    for epoch in range(num_epochs):
        print(f'Epoch {epoch+1}/{num_epochs}')
        print('-' * 10)
        
        # Training phase
        model.train()
        running_loss = 0.0
        running_corrects = 0
        
        for inputs, labels in train_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            _, preds = torch.max(outputs, 1)
            running_loss += loss.item() * inputs.size(0)
            running_corrects += torch.sum(preds == labels.data)
        
        epoch_loss = running_loss / len(train_loader.dataset)
        epoch_acc = running_corrects.double() / len(train_loader.dataset)
        
        history['train_loss'].append(epoch_loss)
        history['train_acc'].append(epoch_acc.item())
        
        # Validation phase
        model.eval()
        val_running_loss = 0.0
        val_running_corrects = 0
        
        with torch.no_grad():
            for inputs, labels in val_loader:
                inputs, labels = inputs.to(device), labels.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, labels)
                
                _, preds = torch.max(outputs, 1)
                val_running_loss += loss.item() * inputs.size(0)
                val_running_corrects += torch.sum(preds == labels.data)
        
        val_epoch_loss = val_running_loss / len(val_loader.dataset)
        val_epoch_acc = val_running_corrects.double() / len(val_loader.dataset)
        
        history['val_loss'].append(val_epoch_loss)
        history['val_acc'].append(val_epoch_acc.item())
        
        print(f'Train Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}')
        print(f'Val Loss: {val_epoch_loss:.4f} Acc: {val_epoch_acc:.4f}')
        
        # Save best model
        if val_epoch_acc > best_val_acc:
            best_val_acc = val_epoch_acc
            best_model_state = model.state_dict().copy()
        
        # Step scheduler
        if scheduler:
            scheduler.step(val_epoch_loss)
        
        print()
    
    # Load best model
    model.load_state_dict(best_model_state)
    print(f'Best validation accuracy: {best_val_acc:.4f}')
    
    return model, history

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f'Using device: {device}')

# Initialize models
num_classes = len(label_encoder.classes_)
print(f'Number of classes: {num_classes}')

# Model 1: Custom CNN
cnn_model = SkinCancerCNN(num_classes=num_classes)
print(f'Custom CNN parameters: {sum(p.numel() for p in cnn_model.parameters()):,}')

# Model 2: ResNet Transfer Learning
resnet_model = ResNetTransfer(num_classes=num_classes, pretrained=True)
print(f'ResNet Transfer parameters: {sum(p.numel() for p in resnet_model.parameters()):,}')
print(f'ResNet Trainable parameters: {sum(p.numel() for p in resnet_model.parameters() if p.requires_grad):,}')

# Train Custom CNN
print("Training Custom CNN...")
print("=" * 50)

# Setup training parameters
criterion = nn.CrossEntropyLoss(weight=class_weights_tensor.to(device))
optimizer_cnn = optim.Adam(cnn_model.parameters(), lr=0.001, weight_decay=1e-4)
scheduler_cnn = optim.lr_scheduler.ReduceLROnPlateau(optimizer_cnn, mode='min', patience=3, factor=0.5)

# Train the model
cnn_model, cnn_history = train_model(
    cnn_model, train_loader, val_loader, criterion, optimizer_cnn, scheduler_cnn, 
    num_epochs=15, device=device
)

# Save the model
torch.save(cnn_model.state_dict(), 'custom_cnn_model.pth')
print("Custom CNN model saved as 'custom_cnn_model.pth'")

# Train ResNet Transfer Learning Model
print("\nTraining ResNet Transfer Learning Model...")
print("=" * 50)

# Setup training parameters
optimizer_resnet = optim.Adam(resnet_model.parameters(), lr=0.0001, weight_decay=1e-4)
scheduler_resnet = optim.lr_scheduler.ReduceLROnPlateau(optimizer_resnet, mode='min', patience=3, factor=0.5)

# Train the model
resnet_model, resnet_history = train_model(
    resnet_model, train_loader, val_loader, criterion, optimizer_resnet, scheduler_resnet, 
    num_epochs=20, device=device
)

# Save the model
torch.save(resnet_model.state_dict(), 'resnet_transfer_model.pth')
print("ResNet Transfer model saved as 'resnet_transfer_model.pth'")